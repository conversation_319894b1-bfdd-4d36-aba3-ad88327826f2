import time
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01020108(FukushiSiteTestCaseBase):
    """TESTRAA01020108"""

    def test_case_qaa010201_08(self):
        """test_case_qaa010201_08"""

        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")

        #ログイン
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA010")
        self.click_button_by_label("印刷")
        self.save_screenshot_migrate(driver, "010201-08-02", True)	

        if not self.check_online_report_exist(insatsu_tyouhyou_name):
            return

        # TODO(tei) 「身体障害者手帳交付（再交付）決定通知書」がありません
        #「身体障害者手帳交付（再交付）決定通知書」行の印刷チェックボックス選択
        report_param_list = [
            {
                "report_name": insatsu_tyouhyou_name,
                "params": [
                    {"title": "発行年月日", "type": "text", "value": hakkou_ymd}
                ]
            }
        ]
        self.print_online_reports(case_name="ケース名", report_param_list=report_param_list)
        # 「ファイルを開く(O)」ボタンを押下 メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        self.save_screenshot_migrate(driver, "010201-08-06", True)

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        self.save_screenshot_migrate(driver, "010201-08-09", True)	