import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01040106(FukushiSiteTestCaseBase):
    """TESTRAA01040106"""

    def test_case_raa010401_06(self):
        """test_case_raa010401_06"""

        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")

        # ログイン
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA040")

        # 印刷
        self.click_button_by_label("印刷")
        self.save_screenshot_migrate(driver, "raa010401_06-02", True)

        if not self.check_online_report_exist(insatsu_tyouhyou_name):
            return

        # 「障害者手帳通知書」行の印刷チェックボックス選択
        report_param_list = [
            {
                "report_name": insatsu_tyouhyou_name,
                "params": [
                    {"title": "発行年月日", "type": "text", "value": hakkou_ymd}
                ]
            }
        ]
        self.print_online_reports(case_name="ケース名", report_param_list=report_param_list)
        self.assert_message_area("プレビューを表示しました")
        self.save_screenshot_migrate(driver, "raa010401_06-06", True)

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        self.save_screenshot_migrate(driver, "raa010401_06-09", True)
