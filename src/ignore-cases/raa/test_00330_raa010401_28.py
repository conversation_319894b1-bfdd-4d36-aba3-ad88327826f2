import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01040128(FukushiSiteTestCaseBase):
    """TESTRAA01040128"""

    def test_case_raa010401_28(self):
        """test_case_raa010401_28"""
    
        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        keittei_ymd_start = case_data.get("keittei_ymd_start", "")
        keittei_ymd_end = case_data.get("keittei_ymd_end", "")
        bunsho_no = case_data.get("bunsho_no", "")
        order = case_data.get("order", "")
        
        # ログイン
        self.do_login()
        self.click_button_by_label("バッチ起動")
        self.save_screenshot_migrate(driver, "raa010401_28-02" , True)

        self.find_element(By.ID, "GyomuSelect").send_keys("障害")
        time.sleep(2)
        self.find_element(By.ID, "JigyoSelect").send_keys("精神手帳")
        time.sleep(2)
        self.find_element(By.ID, "ShoriKubunSelect").send_keys("随時処理")
        time.sleep(2)
        self.find_element(By.ID, "ShoriBunruiSelect").send_keys("帳票出力処理（一括）")
        self.save_screenshot_migrate(driver, "raa010401_28-07" , True)

        time.sleep(2)
        # 「障害者手帳交付決定通知書出力処理」の行の数字ボタン押下
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name)
        self.save_screenshot_migrate(driver, "raa010401_28-09" , True)

        time.sleep(2)
        params = [
            {"title": "発行年月日", "type": "text", "value": hakkou_ymd},
            {"title": "文書番号", "type": "text", "value": bunsho_no},
            {"title": "決定年月日開始", "type": "text", "value": keittei_ymd_start},
            {"title": "決定年月日終了", "type": "text", "value": keittei_ymd_end},
            {"title": "出力順序", "type": "select", "value": order}
        ]
        self.set_job_params(params)
        self.save_screenshot_migrate(driver, "raa010401_28-10" , True) 

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        self.save_screenshot_migrate(driver, "raa010401_28-13" , True)

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.save_screenshot_migrate(driver, "raa010401_28-16" , True)

        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(30,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.save_screenshot_migrate(driver, "raa010401_28-18" , True)
        
        # No.の「１」行の「ダウンロード」ボタンを押下
        self.click_by_id("DownLoad1")
        self.save_screenshot_migrate(driver, "raa010401_28-20" , True)

        # ダウンロード画面のNo.の「１」ボタンを押下
        self.click_by_id("No1")
        self.save_screenshot_migrate(driver, "raa010401_28-22" , True)

        # 「ファイルを開く(O)」ボタンを押下

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 「戻る」ボタン押下
        self.return_click()

        # 「帳票履歴」ボタン押下
        self.driver.find_element(By.ID, "span_ReportListButton").click()
        self.save_screenshot_migrate(driver, "raa010401_28-22_「帳票履歴」ボタン押下" , True)

        # 「検索」ボタン押下
        self.driver.find_element(By.ID, "span_SearchButton").click()
        self.save_screenshot_migrate(driver, "raa010401_28-22_「検索」ボタン押下" , True)

        # 帳票（PDF）表示
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name)
        self.save_screenshot_migrate(driver, "raa010401_28-22「ファイルを開く」ボタン押下" , True)

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        self.save_screenshot_migrate(driver, "raa010401_28-26" , True)

