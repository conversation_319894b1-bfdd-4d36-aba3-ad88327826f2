import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK002001(FukushiSiteTestCaseBase):
    """TESTQAK002001"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAK002.sql", params=atena_list)
        super().setUp()
    
    def test_case_qak002_001(self):
        """test_case_qak002_001"""
        driver = None
        test_data = self.common_test_data
        self.do_login()
        self.find_element(By.ID,"CmdProcess14_2").click()
        self.save_screenshot_migrate(driver, "QAK002-001-02", True)

        self.find_element(By.ID,"BangoShubetuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"BangoShubetuCmb"),"宛名コード")
        # time.sleep(2)
        self.find_element(By.ID,"CmdKakutei").click()
        self.find_element(By.ID,"TxtOldNumber").send_keys("100011")
        self.find_element(By.ID,"CmdKensakuOld").click()
        self.save_screenshot_migrate(driver, "QAK002-001-06", True)
        self.find_element(By.ID,"TxtNewNumber").send_keys("100012")
        self.find_element(By.ID,"CmdKensakuNew").click()
        self.save_screenshot_migrate(driver, "QAK002-001-08", True)
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAK002-001-09", True)
        self.find_element(By.ID,"span_Rireki2").click()
        
        self.find_element(By.ID,"CmdDelete").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAK002-001-13", True)
        self.find_element(By.ID,"CmdShoki").click()
        self.save_screenshot_migrate(driver, "QAK002-001-16", True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAK002-001-17", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAK002-001-18", True)
    
        