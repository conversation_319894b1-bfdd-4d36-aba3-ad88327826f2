import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK018001(FukushiSiteTestCaseBase):
    """TESTQAK018001"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAK018_CREATE_QAK分納仮納付書テーブル.sql", params=atena_list)
        self.exec_sqlfile("QAK018.sql", params=atena_list)
        super().setUp()
    
    def test_case_qak018_001(self):
        """test_case_qak018_001"""
        driver = None
        test_data = self.common_test_data
        self.do_login()
        self.find_element(By.ID,"CmdProcess17_1").click()
        self.save_screenshot_migrate(driver, "QAK018-001-02", True)
        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qak018_atena_code"))
        self.find_element(By.ID,"Kensaku").click()
        self.save_screenshot_migrate(driver, "QAK018-001-05", True)
        self.find_element(By.ID,"btnCommon13").click()
        self.save_screenshot_migrate(driver, "QAK018-001-07", True)
        # self.find_element(By.ID,"PageCmb").click()
        # self.select_Option(driver,self.find_element(By.ID,"PageCmb")).select_by_visible_text("02")
        self.find_element(By.ID,"PageCmb").send_keys("02") 
        self.find_element(By.ID,"CmdJumpPage").click()
        self.save_screenshot_migrate(driver, "QAK018-001-09", True)
        self.find_element(By.ID,"CmdBackPage").click()
        self.save_screenshot_migrate(driver, "QAK018-001-10", True)
        self.find_element(By.ID,"CmdNextPage").click()
        self.save_screenshot_migrate(driver, "QAK018-001-11", True)
        # self.save_screenshot_migrate(driver, "QAK018-002-01", True)
        self.find_element(By.ID,"span_CmdTsuika").click()
        self.save_screenshot_migrate(driver, "QAK018-002-02", True)
        self.find_element(By.ID,"span_CmdTainoSentakuAllSelect").click()
        self.save_screenshot_migrate(driver, "QAK018-002-03", True)
        self.find_element(By.ID,"span_CmdTainoSentakuAllDelete").click()
        self.save_screenshot_migrate(driver, "QAK018-002-04", True)
        self.find_element(By.ID,"TxtKijunYMD").click()
        self.find_element(By.ID,"TxtKijunYMD").send_keys("")
        self.find_element(By.ID,"TxtKijunYMD").send_keys(u"令和03年09月23日")
        self.find_element(By.ID,"span_CmdSaikeisan").click()
        self.save_screenshot_migrate(driver, "QAK018-002-06", True)
        self.find_element(By.ID,"CmbTainoSentakuTsuika_1").click()
        self.find_element(By.ID,"span_CmdTainoSentakuTsuika").click()
        self.save_screenshot_migrate(driver, "QAK018-002-08", True)
        self.find_element(By.ID,"TxtSeiyakuYMD").click()
        self.find_element(By.ID,"TxtSeiyakuYMD").send_keys("")
        self.find_element(By.ID,"TxtSeiyakuYMD").send_keys(u"令和03年09月24日")
        self.find_element(By.ID,"TxtStartYM").click()
        self.find_element(By.ID,"TxtStartYM").send_keys("")
        self.find_element(By.ID,"TxtStartYM").send_keys(u"令和03年10月")
        
        # self.find_element(By.ID,"NouhuKijituCbo").click()
        # self.select_Option(driver,self.find_element(By.ID,"NouhuKijituCbo"),"２５日")
        self.find_element(By.ID,"NouhuKijituCbo").send_keys("２５日") 
        
        # self.find_element(By.ID,"NouhuHouhouCbo").click()
        # self.select_Option(driver,self.find_element(By.ID,"NouhuHouhouCbo"),"納付書納付")
        self.find_element(By.ID,"NouhuHouhouCbo").send_keys("納付書納付") 
        
        # self.find_element(By.ID,"YuusenJuniCbo").click()
        # self.select_Option(driver,self.find_element(By.ID,"YuusenJuniCbo"),"本料＞督手＞延滞金")
        self.find_element(By.ID,"YuusenJuniCbo").send_keys("本料＞督手＞延滞金") 
        
        # self.find_element(By.ID,"CmbKeisanJunjo").click()
        # self.select_Option(driver,self.find_element(By.ID,"CmbKeisanJunjo"),"期別毎")
        self.find_element(By.ID,"CmbKeisanJunjo").send_keys("期別毎") 
        self.find_element(By.ID,"TxtGenmenRitu").click()
        self.find_element(By.ID,"TxtGenmenRitu").send_keys("")
        self.find_element(By.ID,"TxtGenmenRitu").send_keys("10")
        self.find_element(By.ID,"RdoTuujou").click()
        self.find_element(By.ID,"ChkTeigaku").click()
        self.find_element(By.ID,"TxtJougengaku").click()
        self.find_element(By.ID,"TxtJougengaku").send_keys("")
        self.find_element(By.ID,"TxtJougengaku").send_keys("100000")
        self.find_element(By.ID,"span_CmdTainoUtiwakeSentaku").click()
        # time.sleep(2)
        self.save_screenshot_migrate(driver, "QAK018-002-16", True)
        self.find_element(By.ID,"span_CmdTainoSentakuCancel").click()
        self.save_screenshot_migrate(driver, "QAK018-002-17", True)
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAK018-002-18", True)
        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"span_NoBtn1").click()
        self.save_screenshot_migrate(driver, "QAK018-002-23", True)
        self.find_element(By.ID,"span_CmdKoushin").click()
        self.save_screenshot_migrate(driver, "QAK018-002-24", True)
        self.find_element(By.ID,"TxtSeiyakuYMD").click()
        self.find_element(By.ID,"TxtSeiyakuYMD").send_keys("")
        self.find_element(By.ID,"TxtSeiyakuYMD").send_keys(u"令和03年09月27日")
        self.find_element(By.ID,"span_CmdTainoUtiwakeAllSelect").click()
        self.save_screenshot_migrate(driver, "QAK018-002-26", True)
        self.find_element(By.ID,"span_CmdTainoUtiwakeAllDelete").click()
        self.save_screenshot_migrate(driver, "QAK018-002-27", True)
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAK018-002-28", True)
        
        self.find_element(By.ID,"CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAK018-002-31", True)
        self.find_element(By.ID,"CmdTsuika").click()
        self.save_screenshot_migrate(driver, "QAK018-002-34", True)
        
        self.find_element(By.ID,"span_CmdShoki").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAK018-002-35", True)
        self.find_element(By.ID,"span_CmdTainoSentakuCancel").click()
        self.save_screenshot_migrate(driver, "QAK018-002-38", True)
        
        self.find_element(By.ID,"GOBACK").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.find_element(By.ID,"span_NoBtn1").click()
        self.find_element(By.ID,"btnCommon0").click()
        self.save_screenshot_migrate(driver, "QAK018-003-02", True)
        self.find_element(By.ID,"ChohyoSentakuCmb").click()
        
        self.find_element(By.ID,"CmdPrint").click()
        self.assertEqual(u"印刷します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAK018-003-04", True)
        self.find_element(By.ID,"span_CmdNofuYotei").click()
        # driver.save_screenshot("..//evidence//" + "QAK018-004-02-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-02", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-02-2" +".png")
        self.find_element(By.ID,"CmdTsuika").click()
        # driver.save_screenshot("..//evidence//" + "QAK018-004-03-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-03", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-03-2" +".png")
        self.find_element(By.ID,"TxtNofuYMD").click()
        self.find_element(By.ID,"TxtNofuYMD").send_keys("")
        self.find_element(By.ID,"TxtNofuYMD").send_keys(u"令和03年09月24日")
        self.find_element(By.ID,"TxtNofuKingaku").click()
        self.find_element(By.ID,"TxtNofuKingaku").send_keys("")
        self.find_element(By.ID,"TxtNofuKingaku").send_keys("2500")
        # self.save_screenshot_migrate(driver, "QAK018-004-06", True)
        self.find_element(By.ID,"CmdNyuryoku").click()
        # driver.save_screenshot("..//evidence//" + "QAK018-004-07-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-07", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-07-2" +".png")
        self.find_element(By.ID,"Sel1").click()
        # driver.save_screenshot("..//evidence//" + "QAK018-004-08-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-08", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-08-2" +".png")
        self.find_element(By.ID,"CmdShusei").click()
        # driver.save_screenshot("..//evidence//" + "QAK018-004-09-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-09", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-09-2" +".png")
        self.find_element(By.ID,"TxtNofuKingaku").click()
        self.find_element(By.ID,"TxtNofuKingaku").send_keys("")
        self.find_element(By.ID,"TxtNofuKingaku").send_keys("2500")
        self.find_element(By.ID,"span_CmdNyuryoku").click()
        # driver.save_screenshot("..//evidence//" + "QAK018-004-11-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-11", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-11-2" +".png")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        # driver.save_screenshot("..//evidence//" + "QAK018-004-12-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-12", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-12-2" +".png")
        self.find_element(By.ID,"Sel1").click()
        # driver.save_screenshot("..//evidence//" + "QAK018-004-15-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-15", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-15-2" +".png")
        
        self.find_element(By.ID,"CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        # driver.save_screenshot("..//evidence//" + "QAK018-004-16-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-16", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-16-2" +".png")
        self.find_element(By.ID,"Sel2").click()
        # driver.save_screenshot("..//evidence//" + "QAK018-004-19-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-19", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-19-2" +".png")
        self.find_element(By.ID,"span_CmdShusei").click()
        # driver.save_screenshot("..//evidence//" + "QAK018-004-20-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-20", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-20-2" +".png")
        self.find_element(By.ID,"TxtNofuKingaku").click()
        self.find_element(By.ID,"TxtNofuKingaku").send_keys("")
        self.find_element(By.ID,"TxtNofuKingaku").send_keys("5000")
        self.find_element(By.ID,"span_CmdNyuryoku").click()
        # driver.save_screenshot("..//evidence//" + "QAK018-004-22-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-22", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-22-2" +".png")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        # driver.save_screenshot("..//evidence//" + "QAK018-004-23-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-23", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-23-2" +".png")
        self.find_element(By.ID,"Sel1").click()
        # driver.save_screenshot("..//evidence//" + "QAK018-004-26-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-26", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-26-2" +".png")
        self.find_element(By.ID,"span_CmdCancel").click()
        # driver.save_screenshot("..//evidence//" + "QAK018-004-27-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-27", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-27-2" +".png")
        self.find_element(By.ID,"span_CmdShoki").click()
        #driver.save_screenshot("..//evidence//" + "QAK018-004-28-1" +".png")
        self.save_screenshot_migrate(driver, "QAK018-004-28", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK018-004-28-2" +".png")

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAK018-004-29", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAK018-004-30", True)
    
        