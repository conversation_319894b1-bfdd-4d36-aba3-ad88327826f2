DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

/*調査用
--QAK010 資格
--QAK001-001
SELECT * FROM WR$$JICHITAI_CODE$$FA..QAK障害者広域連合送付対象 				WHERE 宛名コード = '00100011' AND 業務コード = 'QAK010'
--QAK002-001
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK新旧番号管理						WHERE 番号 IN ('00100011','00100012') AND 業務コード = 'QAK010'
--QAK003-001

--QAK004-001
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK資格履歴 							WHERE 宛名コード = '00100014' AND 業務コード = 'QAK010' 
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK被保険者資格内容 					WHERE 宛名コード = '00100014' AND 業務コード = 'QAK010' 

--その他
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK被保険者資格内容T 					WHERE 宛名コード = '00100011' AND 業務コード = 'QAK010' 
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK被保険者証発行内容 					WHERE 宛名コード = '00100011' AND 業務コード = 'QAK010' 
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK被保険者証発行内容T 				WHERE 宛名コード = '00100011' AND 業務コード = 'QAK010'
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK住所地特例者管理 					WHERE 宛名コード = '00100011' AND 業務コード = 'QAK010' 
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK住所地特例者管理T 					WHERE 宛名コード = '00100011' AND 業務コード = 'QAK010' 
SELECT * FROM WR$$JICHITAI_CODE$$FA..QAK後期高齢者世帯情報 					WHERE 宛名コード = '00100011' AND 業務コード = 'QAK010'
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK資格履歴							WHERE 宛名コード = '00100011' AND 業務コード = 'QAK010'
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK広域送付税WK 						--WHERE 宛名コード = '00100001' AND 業務コード = 'QAK010'
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK生活保護セットアップ情報 			--WHERE 宛名コード = '00100001' AND 業務コード = 'QAK010' 
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK国保資格喪失者情報テーブル 			WHERE 宛名コード = '00100011' --AND 業務コード = 'QAK010'
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK_文字コード変換対応 				--WHERE 宛名コード = '00100001' AND 業務コード = 'QAK010'
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK老人保健セットアップ情報 			--WHERE 宛名コード = '00100001' AND 業務コード = 'QAK010' 
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK広域送付対象WK 						WHERE 宛名コード = '00100011' --AND 業務コード = 'QAK010' 
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK広域送受信データ個人番号変換対応 	--WHERE 宛名コード = '00100001' AND 業務コード = 'QAK010' 
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK広域送受信データ世帯番号変換対応 	--WHERE 宛名コード = '00100001' AND 業務コード = 'QAK010' 
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAZ連携ファイル処理結果				WHERE 業務コード = 'QAK010' 
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK_文字コード変換エラー 				WHERE 宛名コード = '00100011' AND 業務コード = 'QAK010' 

SELECT * FROM WR$$JICHITAI_CODE$$QA..QAK区間異動更新テーブル管理 			WHERE 業務コード = 'QAK010'
SELECT * FROM WR$$JICHITAI_CODE$$QA..QAZ連携ファイル情報					WHERE 業務コード = 'QAK010' 
SELECT * FROM WR$$JICHITAI_CODE$$FA..QFAコード対応 							--WHERE 宛名コード = '00100001' AND 業務コード = 'QAK010'
*/

--QAK001-001
DELETE FROM WR$$JICHITAI_CODE$$FA..QAK障害者広域連合送付対象 				WHERE 宛名コード = '00100011' AND 業務コード = 'QAK010'
--QAK002-001
DELETE FROM WR$$JICHITAI_CODE$$QA..QAK新旧番号管理							WHERE 番号 = '00100011' AND 業務コード = 'QAK010'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAK新旧番号管理							WHERE 番号 = '00100012' AND 業務コード = 'QAK010'
--QAK003-001

--QAK004-001
DELETE FROM WR$$JICHITAI_CODE$$QA..QAK資格履歴								WHERE 宛名コード = '00100011' AND 業務コード = 'QAK010' 
DELETE FROM WR$$JICHITAI_CODE$$QA..QAK被保険者資格内容						WHERE 宛名コード = '00100011' AND 業務コード = 'QAK010'


IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END