import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK005001(FukushiSiteTestCaseBase):
    """TESTQAK005001"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAK005.sql", params=atena_list)
        super().setUp()
    
    def test_case_qak005_001(self):
        """test_case_qak005_001"""
        driver = None
        test_data = self.common_test_data
        self.do_login()
        self.find_element(By.ID,"CmdProcess16_1").click()
        self.save_screenshot_migrate(driver, "QAK005-001-02", True)
        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qak005_atena_code"))
        self.find_element(By.ID,"Kensaku").click()
        self.save_screenshot_migrate(driver, "QAK005-001-05", True)
        self.find_element(By.ID,"span_CmdMaeSoutouNendo").click()
        self.save_screenshot_migrate(driver, "QAK005-001-06", True)
        self.find_element(By.ID,"span_CmdAtoSoutouNendo").click()
        self.save_screenshot_migrate(driver, "QAK005-001-07", True)
        self.find_element(By.ID,"span_CmdMaeFukaRireki").click()
        self.save_screenshot_migrate(driver, "QAK005-001-08", True)
        self.find_element(By.ID,"span_CmdAtoFukaRireki").click()
        self.save_screenshot_migrate(driver, "QAK005-001-09", True)
        self.find_element(By.ID,"span_CmdKonkyoHyoji").click()
        self.save_screenshot_migrate(driver, "QAK005-001-10", True)
        self.find_element(By.ID,"span_CmdShikakuHyoji").click()
        self.save_screenshot_migrate(driver, "QAK005-001-11", True)
        self.find_element(By.ID,"span_CmdDokujiKeigenHyoji").click()
        self.save_screenshot_migrate(driver, "QAK005-001-12", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK005//" + "QAK005-001-12-2" +".png")
        self.find_element(By.ID,"span_CmdKiwariHyoji").click()
        self.save_screenshot_migrate(driver, "QAK005-001-13", True)
        self.find_element(By.ID,"span_CmdFukaIchiran").click()
        self.save_screenshot_migrate(driver, "QAK005-002-02", True)
        self.find_element(By.ID,"span_Sel2").click()
        self.find_element(By.NAME,"img").click()
        self.find_element(By.ID,"btnCommon13").click()
        self.save_screenshot_migrate(driver, "QAK005-003-02", True)
        
        self.find_element(By.ID,"CmbFukaNendo").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbFukaNendo"),"平成30年")
        # time.sleep(2)
        self.find_element(By.ID,"TxtShikuchosonHakkoYMD").click()
        self.find_element(By.ID,"TxtShikuchosonHakkoYMD").send_keys("")
        self.find_element(By.ID,"TxtShikuchosonHakkoYMD").send_keys(u"令和03年09月10日")
        self.find_element(By.ID,"TxtKouikiHakkoYMD").click()
        self.find_element(By.ID,"TxtKouikiHakkoYMD").send_keys("")
        self.find_element(By.ID,"TxtKouikiHakkoYMD").send_keys(u"令和03年09月10日")
        
        self.find_element(By.ID,"CmbTuchishoYoshiki").send_keys("確定賦課・異動賦課用")
        # time.sleep(2)
        self.accept_next_alert = True
        self.find_element(By.ID,"CmdPrint").click()
        self.assertEqual(u"印刷します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAK005-003-07", True)
        # ERROR: Caught exception [ERROR: Unsupported command [selectWindow | win_ser_1 | ]]
        # driver.close()
        # ERROR: Caught exception [ERROR: Unsupported command [selectWindow | win_ser_local | ]]
        # ERROR: Caught exception [ERROR: Unsupported command [selectWindow | win_ser_2 | ]]
        # driver.close()
        # ERROR: Caught exception [ERROR: Unsupported command [selectWindow | win_ser_local | ]]

        # time.sleep(25)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAK005-003-08", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAK005-003-09", True)
    
        