USE [WR$$JICHITAI_CODE$$QA]

-- QAZ福祉世帯
DELETE [dbo].[QAZ福祉世帯] 
where 業務コード = 'QYH010'
and 自治体コード = '$$JICHITAI_CODE$$'
and 履歴番号 = '174'

INSERT INTO [dbo].[QAZ福祉世帯] ([業務コード],[履歴番号],[履歴分類],[自治体コード],[福祉事務所コード],[支所コード],[本人宛名コード],[福祉世帯員宛名コード],[該当日],[非該当日],[本人から見た続柄],[受給者との関係],[汎用項目],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES ('QYH010','174','2021','$$JICHITAI_CODE$$','$$JICHITAI_CODE$$','','00220010','00220010','20220401','99999999','0000000001','0000000001','0000000000','0','9501','9501',getdate(),getdate(),'00000000')

INSERT INTO [dbo].[QAZ福祉世帯] ([業務コード],[履歴番号],[履歴分類],[自治体コード],[福祉事務所コード],[支所コード],[本人宛名コード],[福祉世帯員宛名コード],[該当日],[非該当日],[本人から見た続柄],[受給者との関係],[汎用項目],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES ('QYH010','174','2021','$$JICHITAI_CODE$$','$$JICHITAI_CODE$$','','00220010','00220011','20220401','99999999','0000000000','9999999999','0000000000','0','9501','9501',getdate(),getdate(),'00000000')

-- QYHひとり親世帯等明細
DELETE [dbo].[QYHひとり親世帯等明細] 
where 業務コード = 'QYH010'
and 自治体コード = '$$JICHITAI_CODE$$'
and 履歴番号 = '174'

INSERT INTO [dbo].[QYHひとり親世帯等明細]([業務コード],[履歴番号],[自治体コード],[福祉事務所コード],[支所コード],[年度],[宛名コード],[ひとり親明細1],[ひとり親明細2],[ひとり親明細3],[ひとり親明細4],[ひとり親明細5],[ひとり親明細6],[ひとり親明細7],[ひとり親明細8],[ひとり親明細9],[ひとり親明細10],[ひとり親明細11],[ひとり親明細12],[ひとり親明細13],[ひとり親明細14],[ひとり親明細15],[ひとり親明細16],[ひとり親明細17],[ひとり親明細18],[ひとり親明細19],[ひとり親明細20],[ひとり親明細21],[ひとり親明細22],[ひとり親明細23],[ひとり親明細24],[備考],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QYH010','174','$$JICHITAI_CODE$$','$$JICHITAI_CODE$$','','2021','00220010','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','','0','9501','9501',getdate(),getdate(),'00000000')

-- QYH資格内容
DELETE [dbo].[QYH資格内容] 
where 業務コード = 'QYH010'
and 自治体コード = '$$JICHITAI_CODE$$'
and 履歴番号 = '174'

INSERT INTO [dbo].[QYH資格内容]([業務コード],[履歴番号],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[年度],[バーコード世帯番号],[受給者区分],[保護者負担額計],[所得割額],[均等割額],[扶養人数00_15歳],[扶養人数16_18歳],[前年度所得割額],[前年度均等割額],[前年度00_15歳扶養人数],[前年度16_18歳扶養人数],[当年度所得割額],[当年度均等割額],[当年度00_15歳扶養人数],[当年度16_18歳扶養人数],[優遇措置対象人数],[優遇措置対象人数_H28以降],[保育園等対象人数],[認可外保育園対象人数],[仮保護者宛名コード],[生活保護世帯フラグ],[未申告世帯フラグ],[前年度未申告世帯フラグ],[ひとり親世帯等フラグ],[口座停止フラグ],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QYH010','174','$$JICHITAI_CODE$$','$$JICHITAI_CODE$$','','00220010','2021','02100220010','0000000000',0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,'','0','0','1','0','0','0','9501','9501',getdate(),getdate(),'00000000')

-- QYH資格履歴
DELETE [dbo].[QYH資格履歴] 
where 業務コード = 'QYH010'
and 履歴番号 = '174'
and 自治体コード = '$$JICHITAI_CODE$$'

INSERT INTO [dbo].[QYH資格履歴]([業務コード],[履歴番号],[履歴分類],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[申請年月日],[申請種別],[申請理由],[申請内容入力日],[進達年月日1],[進達判定年月日1],[進達結果1],[進達内容入力日1],[進達年月日2],[進達判定年月日2],[進達結果2],[進達内容入力日2],[決定年月日],[決定結果],[決定理由],[決定内容入力日],[業務固有コード1],[業務固有コード2],[業務固有コード3],[職権フラグ],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QYH010','174','2021','$$JICHITAI_CODE$$','$$JICHITAI_CODE$$','','00220010','20220401',1,1,'20220412','00000000','00000000',0,'00000000','00000000','00000000',0,'00000000','20220412',1,0,'20220412','','','','0','0','9501','9501',getdate(),getdate(),'00000000')

-- QYH幼児管理
DELETE [dbo].[QYH幼児管理] 
where 業務コード = 'QYH010'
and 履歴番号 = '174'
and 幼児履歴番号 = '191'
and 自治体コード = '$$JICHITAI_CODE$$'

INSERT INTO [dbo].[QYH幼児管理]([業務コード],[履歴番号],[幼児履歴番号],[幼児宛名コード],[保護者宛名コード],[年度],[学年],[自治体コード],[福祉事務所コード],[支所コード],[子NO],[保護者負担額],[入園料合計額],[保育料合計額],[学納金合計額],[給食費合計額],[その他合計額],[通知書用備考1],[通知書用備考2],[委任フラグ],[提出番号],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QYH010','174','191','00220011','00220010','2021','4','$$JICHITAI_CODE$$','$$JICHITAI_CODE$$','',1,249999975,9999999,119999988,119999988,0,0,'0000000000','','1',0,'0','9501','9501',getdate(),getdate(),'00000000')

-- QYH幼児在園状態
DELETE [dbo].[QYH幼児在園状態] 
where 業務コード = 'QYH010'
and 自治体コード = '$$JICHITAI_CODE$$'
and 幼児履歴番号 = '191'

INSERT INTO [dbo].[QYH幼児在園状態]([業務コード],[自治体コード],[福祉事務所コード],[支所コード],[年度],[幼児宛名コード],[保護者宛名コード],[幼児履歴番号],[入園フラグ],[入園年月日],[退園フラグ],[退園年月日],[枝番],[在園期間開始月],[在園期間終了月],[算定期間開始月],[算定期間終了月],[認定開始年月日],[認定終了年月日],[幼稚園コード],[組コード],[入園料],[入園料月額],[保育料月額],[保育料合計],[その他年額],[学納金月額],[学納金合計],[給食費],[名簿番号],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QYH010','$$JICHITAI_CODE$$','$$JICHITAI_CODE$$','','2021','00220011','00220010','191','1','20210401','0','99999999',1,4,15,'0','0','20210401','20220331','0000000001','0000000004',9999999,832920,9999999,119999988,0,9999999,119999988,0,0,'0','9501','9501',getdate(),getdate(),'00000000')

-- QYH幼児補助金詳細
DELETE [dbo].[QYH幼児補助金詳細] 
where 業務コード = 'QYH010'
and 自治体コード = '$$JICHITAI_CODE$$'
and 幼児履歴番号 = '191'

INSERT INTO [dbo].[QYH幼児補助金詳細]([業務コード],[自治体コード],[福祉事務所コード],[支所コード],[幼児履歴番号],[年度],[幼児宛名コード],[保護者宛名コード],[事業コード],[申請年月日],[申請種別コード],[決定年月日],[決定結果コード],[決定理由コード],[終了年月日],[終了申請理由コード],[代表補助金階層コード],[代表補助金階層区分コード],[計算時子NO],[計算時判定方式],[交付予定額合計],[交付確定額合計],[月数],[職権フラグ],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QYH010','$$JICHITAI_CODE$$','$$JICHITAI_CODE$$','','191','2021','00220011','00220010','040','20210401',1,'20210405',1,0,'99999999',0,'01','0000000000',1,'0',5000,5000,0,'0','0','9501','9501',getdate(),getdate(),'00000000')

