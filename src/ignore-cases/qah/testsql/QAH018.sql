DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

DELETE FROM WR$$JICHITAI_CODE$$QA..QAH職種管理 WHERE 業務コード = 'QAH020' and 賃金区分 = '1' and 職種コード = '0000000100'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAH単価管理 WHERE 業務コード = 'QAH020' and 賃金区分 = '1' and 職種コード = '0000000100'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAH職種管理 WHERE 業務コード = 'QAH020' and 賃金区分 = '2' and 職種コード = '0000000100'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAH単価管理 WHERE 業務コード = 'QAH020' and 賃金区分 = '2' and 職種コード = '0000000100'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAH賃金対象者管理 WHERE 業務コード = 'QAH020' and 賃金区分 = '1' and 氏名 = 'テスト　賃金'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAH賃金対象者管理 WHERE 業務コード = 'QAH020' and 賃金区分 = '1' and 氏名 = 'テスト　従事者'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAH賃金対象者管理 WHERE 業務コード = 'QAH020' and 賃金区分 = '2' and 氏名 = 'テスト　報償'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAH賃金対象者職種管理 WHERE 業務コード = 'QAH020' and 賃金区分 = '1' and 職種コード = '0000000100'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAH賃金対象者職種管理 WHERE 業務コード = 'QAH020' and 賃金区分 = '2' and 職種コード = '0000000100'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAH事業管理 WHERE 業務コード = 'QAH020' and 賃金区分 = '1' and 事業区分 = '0000000001' 
DELETE FROM WR$$JICHITAI_CODE$$QA..QAH事業管理 WHERE 業務コード = 'QAH020' and 賃金区分 = '2' and 事業区分 = '0000000001' 
DELETE FROM WR$$JICHITAI_CODE$$QA..QAH賃金実績管理 WHERE 業務コード = 'QAH020' and 賃金区分 = '1' and 事業区分 = '0000000001'  and 職種コード = '0000000100' 

IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END