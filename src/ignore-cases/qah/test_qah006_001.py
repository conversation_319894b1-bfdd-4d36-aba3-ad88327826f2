import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAH006001(FukushiSiteTestCaseBase):
    """TESTQAH006001"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAH006.sql", params=atena_list)
        super().setUp()
    
    def test_case_qah006_001(self):
        """test_case_qah006_001"""
        driver = None
        test_data = self.common_test_data
        self.do_login()
        #メインメニュー・健康管理押下
        self.find_element(By.ID,"CmdProcess32_1").send_keys(Keys.ENTER)
        """
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "Kana<PERSON>himei")))
        self.save_screenshot_migrate(driver, "QAH006-006-1-2" , True)

        self.find_element(By.ID,"KanaShimei").send_keys("")
        #self.find_element(By.ID,"KanaShimei").send_keys(u"ｹﾝｺｳ")
        self.execute_script('document.getElementById("KanaShimei").value="%s";' %'ｹﾝｺｳ')
        self.find_element(By.ID,"Kensaku").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "Tsugi")))
        self.save_screenshot_migrate(driver, "QAH006-006-1-5" , True)

        # self.find_element(By.ID,"Tsugi").click()
        self.find_element(By.ID,"NoBtn2").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdTokutei4")))
        """
        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qah006_atena_code"))
        self.find_element(By.ID,"span_Kensaku").click()

        self.save_screenshot_migrate(driver, "QAH006-006-2-9" , True)

        self.find_element(By.ID,"span_CmdTokutei4").click()

        self.find_element(By.ID,"span_CmdShogamen").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdTransit")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-10" , True)

        self.find_element(By.ID,"span_CmdTransit").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-11" , True)
        # 共通2　個人検索選択
        # time.sleep(10)

        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | index=0 | ]]
        #self.find_element(By.ID,"CmdCommon2").click()
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | relative=parent | ]]


        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "KanaShimei")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-13" , True)

        """
        self.find_element(By.ID,"KanaShimei").click()
        self.find_element(By.ID,"KanaShimei").send_keys("")
        #self.find_element(By.ID,"KanaShimei").send_keys(u"ｹﾝｺｳ")
        self.execute_script('document.getElementById("KanaShimei").value="%s";' %'ｹﾝｺｳ')

        self.find_element(By.ID,"Kensaku").click()
        # self.find_element(By.ID,"Tsugi").click()
        self.find_element(By.ID,"NoBtn2").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmKenkouShinsa3")))
        """
        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qah006_atena_code"))
        self.find_element(By.ID,"span_Kensaku").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-18" , True)

        self.find_element(By.ID,"span_CmKenkouShinsa3").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdTsuika")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-20" , True)

        self.find_element(By.ID,"span_CmdTsuika").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmbFukushi")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-21" , True)

        # self.find_element(By.ID,"CmbFukushi").click()
        # self.select_Option(driver,self.find_element(By.ID,"CmbFukushi"),"第一区")
        self.find_element(By.ID,"CmbFukushi").send_keys("第一区")
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20210401")
        self.find_element(By.ID,"TxtJushinBasho").click()
        self.find_element(By.ID,"TxtJushinBasho").send_keys("")
        self.find_element(By.ID,"TxtJushinBasho").send_keys("0000000001")
        # self.find_element(By.ID,"CmbShudanKobetsu").click()
        # self.select_Option(driver,self.find_element(By.ID,"CmbShudanKobetsu"),"集団")
        self.find_element(By.ID,"CmbShudanKobetsu").send_keys("集団")
        self.find_element(By.ID,"01001001").click()
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys("1")
        self.find_element(By.ID,"01001002").click()
        self.find_element(By.ID,"01001002").send_keys("")
        self.find_element(By.ID,"01001002").send_keys("1")
        self.find_element(By.ID,"01001003").click()
        self.find_element(By.ID,"01001003").send_keys("")
        self.find_element(By.ID,"01001003").send_keys("1")
        self.find_element(By.ID,"01001004").click()
        self.find_element(By.ID,"01001004").send_keys("")
        self.find_element(By.ID,"01001004").send_keys("1")
        # self.find_element(By.ID,"01001005").click()
        # self.select_Option(driver,self.find_element(By.ID,"01001005"),"国保")
        self.find_element(By.ID,"01001005").send_keys("国保")
        # self.find_element(By.ID,"01001006").click()
        # self.select_Option(driver,self.find_element(By.ID,"01001006"),"特定健康診査")
        self.find_element(By.ID,"01001006").send_keys("特定健康診査")
        # self.find_element(By.ID,"01001007").click()
        # self.select_Option(driver,self.find_element(By.ID,"01001007"),"補助あり")
        self.find_element(By.ID,"01001007").send_keys("補助あり")
        self.find_element(By.ID,"01001008").click()
        self.find_element(By.ID,"01001008").send_keys("")
        self.find_element(By.ID,"01001008").send_keys("1")
        self.find_element(By.ID,"01002001").click()
        self.find_element(By.ID,"01002001").send_keys("")
        self.find_element(By.ID,"01002001").send_keys("1")
        self.find_element(By.ID,"01002002").click()
        self.find_element(By.ID,"01002002").send_keys("")
        self.find_element(By.ID,"01002002").send_keys("1")
        self.find_element(By.ID,"01002003").click()
        self.find_element(By.ID,"01002003").send_keys("")
        self.find_element(By.ID,"01002003").send_keys("1")
        self.find_element(By.ID,"01002004").click()
        self.find_element(By.ID,"01002004").send_keys("")
        self.find_element(By.ID,"01002004").send_keys("1")
        # self.find_element(By.ID,"01002005").click()
        # self.select_Option(driver,self.find_element(By.ID,"01002005"),"実測")
        self.find_element(By.ID,"01002005").send_keys("実測")
        self.find_element(By.ID,"01002006").click()
        self.find_element(By.ID,"01002006").send_keys("")
        self.find_element(By.ID,"01002006").send_keys("1")
        # self.find_element(By.ID,"01003001").click()
        # self.select_Option(driver,self.find_element(By.ID,"01003001"),"特記すべきことあり")
        self.find_element(By.ID,"01003001").send_keys("特記すべきことあり")
        self.find_element(By.ID,"textArea01003002").click()
        self.find_element(By.ID,"textArea01003002").send_keys("")
        self.find_element(By.ID,"textArea01003002").send_keys("1")
        # self.find_element(By.ID,"01003003").click()
        # self.select_Option(driver,self.find_element(By.ID,"01003003"),"特記すべきことあり")
        self.find_element(By.ID,"01003003").send_keys("特記すべきことあり")
        self.find_element(By.ID,"textArea01003004").click()
        self.find_element(By.ID,"textArea01003004").send_keys("")
        self.find_element(By.ID,"textArea01003004").send_keys("1")
        # self.find_element(By.ID,"01003005").click()
        # self.select_Option(driver,self.find_element(By.ID,"01003005"),"特記すべきことあり")
        self.find_element(By.ID,"01003005").send_keys("特記すべきことあり")
        self.find_element(By.ID,"textArea01003006").click()
        self.find_element(By.ID,"textArea01003006").send_keys("")
        self.find_element(By.ID,"textArea01003006").send_keys("1")
        self.find_element(By.ID,"01004001").click()
        self.find_element(By.ID,"01004001").send_keys("")
        self.find_element(By.ID,"01004001").send_keys("1")
        self.find_element(By.ID,"01004002").click()
        self.find_element(By.ID,"01004002").send_keys("")
        self.find_element(By.ID,"01004002").send_keys("1")
        # self.find_element(By.ID,"01004003").click()
        # self.select_Option(driver,self.find_element(By.ID,"01004003"),"食後10時間未満")
        self.find_element(By.ID,"01004003").send_keys("食後10時間未満")
        self.find_element(By.ID,"01005001").click()
        self.find_element(By.ID,"01005001").send_keys("")
        self.find_element(By.ID,"01005001").send_keys("1")
        self.find_element(By.ID,"01005002").click()
        self.find_element(By.ID,"01005002").send_keys("")
        self.find_element(By.ID,"01005002").send_keys("1")
        self.find_element(By.ID,"01005003").click()
        self.find_element(By.ID,"01005003").send_keys("")
        self.find_element(By.ID,"01005003").send_keys("1")
        self.find_element(By.ID,"01006001").click()
        self.find_element(By.ID,"01006001").send_keys("")
        self.find_element(By.ID,"01006001").send_keys("1")
        self.find_element(By.ID,"01006002").click()
        self.find_element(By.ID,"01006002").send_keys("")
        self.find_element(By.ID,"01006002").send_keys("1")
        self.find_element(By.ID,"01006003").click()
        self.find_element(By.ID,"01006003").send_keys("")
        self.find_element(By.ID,"01006003").send_keys("1")
        self.find_element(By.ID,"01007001").click()
        self.find_element(By.ID,"01007001").send_keys("")
        self.find_element(By.ID,"01007001").send_keys("1")
        self.find_element(By.ID,"01008001").click()
        self.find_element(By.ID,"01008001").send_keys("")
        self.find_element(By.ID,"01008001").send_keys("1")
        self.find_element(By.ID,"01008002").click()
        self.find_element(By.ID,"01008002").send_keys("")
        self.find_element(By.ID,"01008002").send_keys("1")
        # self.find_element(By.ID,"01009001").click()
        # self.select_Option(driver,self.find_element(By.ID,"01009001"),"－")
        self.find_element(By.ID,"01009001").send_keys("－")
        # self.find_element(By.ID,"01009002").click()
        # self.select_Option(driver,self.find_element(By.ID,"01009002"),"－")
        self.find_element(By.ID,"01009002").send_keys("－")
        self.find_element(By.ID,"01010001").click()
        self.find_element(By.ID,"01010001").send_keys("")
        self.find_element(By.ID,"01010001").send_keys("1")
        self.find_element(By.ID,"01010002").click()
        self.find_element(By.ID,"01010002").send_keys("")
        self.find_element(By.ID,"01010002").send_keys("1")
        self.find_element(By.ID,"01010003").click()
        self.find_element(By.ID,"01010003").send_keys("")
        self.find_element(By.ID,"01010003").send_keys("1")
        self.find_element(By.ID,"textArea01010004").click()
        self.find_element(By.ID,"textArea01010004").send_keys("")
        self.find_element(By.ID,"textArea01010004").send_keys("1")
        # self.find_element(By.ID,"01011001").click()
        # self.select_Option(driver,self.find_element(By.ID,"01011001"),"異常所見あり")
        self.find_element(By.ID,"01011001").send_keys("異常所見あり")
        self.find_element(By.ID,"textArea01011002").click()
        self.find_element(By.ID,"textArea01011002").send_keys("")
        self.find_element(By.ID,"textArea01011002").send_keys("1")
        self.find_element(By.ID,"textArea01011003").click()
        self.find_element(By.ID,"textArea01011003").send_keys("")
        self.find_element(By.ID,"textArea01011003").send_keys("1")
        # self.find_element(By.ID,"01012001").click()
        # self.select_Option(driver,self.find_element(By.ID,"01012001"),"異常なし")
        self.find_element(By.ID,"01012001").send_keys("異常なし")
        # self.find_element(By.ID,"01012002").click()
        # self.select_Option(driver,self.find_element(By.ID,"01012002")).select_by_visible_text("0")
        self.find_element(By.ID,"01012002").send_keys("0")
        # self.find_element(By.ID,"01012003").click()
        # self.select_Option(driver,self.find_element(By.ID,"01012003")).select_by_visible_text("0")
        self.find_element(By.ID,"01012003").send_keys("0")
        self.find_element(By.ID,"textArea01012004").click()
        self.find_element(By.ID,"textArea01012004").send_keys("")
        self.find_element(By.ID,"textArea01012004").send_keys("1")
        self.find_element(By.ID,"textArea01012005").click()
        self.find_element(By.ID,"textArea01012005").send_keys("")
        self.find_element(By.ID,"textArea01012005").send_keys("1")
        # self.find_element(By.ID,"01013001").click()
        # self.select_Option(driver,self.find_element(By.ID,"01013001"),"基準該当")
        self.find_element(By.ID,"01013001").send_keys("基準該当")
        # self.find_element(By.ID,"01013002").click()
        # self.select_Option(driver,self.find_element(By.ID,"01013002"),"積極的支援")
        self.find_element(By.ID,"01013002").send_keys("積極的支援")
        self.find_element(By.ID,"textArea01013003").click()
        self.find_element(By.ID,"textArea01013003").send_keys("")
        self.find_element(By.ID,"textArea01013003").send_keys("1")
        self.find_element(By.ID,"01013004").click()
        self.find_element(By.ID,"01013004").send_keys("")
        self.find_element(By.ID,"01013004").send_keys("1")
        # self.find_element(By.ID,"01014001").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014001"),"服薬あり")
        self.find_element(By.ID,"01014001").send_keys("服薬あり")
        # self.find_element(By.ID,"01014002").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014002"),"服薬あり")
        self.find_element(By.ID,"01014002").send_keys("服薬あり")
        # self.find_element(By.ID,"01014003").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014003"),"服薬あり")
        self.find_element(By.ID,"01014003").send_keys("服薬あり")
        # self.find_element(By.ID,"01014004").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014004"),"はい")
        self.find_element(By.ID,"01014004").send_keys("はい")
        # self.find_element(By.ID,"01014005").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014005"),"はい")
        self.find_element(By.ID,"01014005").send_keys("はい")
        # self.find_element(By.ID,"01014006").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014006"),"はい")
        self.find_element(By.ID,"01014006").send_keys("はい")
        # self.find_element(By.ID,"01014007").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014007"),"はい")
        self.find_element(By.ID,"01014007").send_keys("はい")
        # self.find_element(By.ID,"01014008").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014008"),"はい")
        self.find_element(By.ID,"01014008").send_keys("はい")
        # self.find_element(By.ID,"01014009").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014009"),"はい")
        self.find_element(By.ID,"01014009").send_keys("はい")
        # self.find_element(By.ID,"01014010").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014010"),"はい")
        self.find_element(By.ID,"01014010").send_keys("はい")
        # self.find_element(By.ID,"01014011").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014011"),"はい")
        self.find_element(By.ID,"01014011").send_keys("はい")
        # self.find_element(By.ID,"01014012").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014012"),"はい")
        self.find_element(By.ID,"01014012").send_keys("はい")
        # self.find_element(By.ID,"01014013").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014013"),"はい")
        self.find_element(By.ID,"01014013").send_keys("はい")
        # self.find_element(By.ID,"01014014").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014014"),"速い")
        self.find_element(By.ID,"01014014").send_keys("速い")
        # self.find_element(By.ID,"01014015").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014015"),"はい")
        self.find_element(By.ID,"01014015").send_keys("はい")
        # self.find_element(By.ID,"01014016").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014016"),"はい")
        self.find_element(By.ID,"01014016").send_keys("はい")
        # self.find_element(By.ID,"01014017").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014017"),"はい")
        self.find_element(By.ID,"01014017").send_keys("はい")
        # self.find_element(By.ID,"01014018").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014018"),"毎日")
        self.find_element(By.ID,"01014018").send_keys("毎日")
        # self.find_element(By.ID,"01014019").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014019"),"1合未満")
        self.find_element(By.ID,"01014019").send_keys("1合未満")
        # self.find_element(By.ID,"01014020").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014020"),"はい")
        self.find_element(By.ID,"01014020").send_keys("はい")
        # self.find_element(By.ID,"01014021").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014021"),"意思なし")
        self.find_element(By.ID,"01014021").send_keys("意思なし")
        # self.find_element(By.ID,"01014022").click()
        # self.select_Option(driver,self.find_element(By.ID,"01014022"),"はい")
        self.find_element(By.ID,"01014022").send_keys("はい")
        self.find_element(By.ID,"01015001").click()
        self.find_element(By.ID,"01015001").send_keys("")
        self.find_element(By.ID,"01015001").send_keys("202104")
        self.find_element(By.ID,"01016001").click()
        self.find_element(By.ID,"01016001").send_keys("")
        self.find_element(By.ID,"01016001").send_keys("1")
        self.find_element(By.ID,"01016002").click()
        self.find_element(By.ID,"01016002").send_keys("")
        self.find_element(By.ID,"01016002").send_keys("1")
        self.find_element(By.ID,"01017001").click()
        self.find_element(By.ID,"01017001").send_keys("")
        self.find_element(By.ID,"01017001").send_keys("20210401")
        self.find_element(By.ID,"01017002").click()
        self.find_element(By.ID,"01017002").send_keys("")
        self.find_element(By.ID,"01017002").send_keys("20210401")
        """
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20210401")
        self.find_element(By.ID,"TxtJushinBasho").click()
        self.find_element(By.ID,"TxtJushinBasho").send_keys("")
        self.find_element(By.ID,"TxtJushinBasho").send_keys("0000000001")
        self.find_element(By.ID,"CmbShudanKobetsu").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbShudanKobetsu"),"個別")
        self.find_element(By.ID,"01001001").click()
        
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys("65")
        """

        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "btnCommon0")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-24" , True)

        self.find_element(By.ID,"btnCommon0").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-26" , True)

        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-28" , True)

        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdHokenShidouKanri3")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-30" , True)

        self.find_element(By.ID,"CmdHokenShidouKanri3").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdTsuika")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-32" , True)

        self.find_element(By.ID,"span_CmdTsuika").click()
        # # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmbFukushi")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-33" , True)
        """
        self.find_element(By.ID,"CmbFukushi").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbFukushi"),"第一区")
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20210401")
        self.find_element(By.ID,"01001001").click()
        self.select_Option(driver,self.find_element(By.ID,"01001001"),"利用")
        self.find_element(By.ID,"01001002").click()
        self.select_Option(driver,self.find_element(By.ID,"01001002"),"積極的支援")
        self.find_element(By.ID,"01002001").click()
        self.select_Option(driver,self.find_element(By.ID,"01002001"),"変化なし")
        self.find_element(By.ID,"01002002").click()
        self.select_Option(driver,self.find_element(By.ID,"01002002"),"変化なし")
        self.find_element(By.ID,"01002003").click()
        self.select_Option(driver,self.find_element(By.ID,"01002003"),"変化なし")
        self.find_element(By.ID,"01002004").click()
        self.select_Option(driver,self.find_element(By.ID,"01002004"),"変化なし")
        self.find_element(By.ID,"01002005").click()
        self.find_element(By.ID,"01002005").send_keys("")
        self.find_element(By.ID,"01002005").send_keys("1")
        self.find_element(By.ID,"01002006").click()
        self.find_element(By.ID,"01002006").send_keys("")
        self.find_element(By.ID,"01002006").send_keys("1")
        self.find_element(By.ID,"01002007").click()
        self.find_element(By.ID,"01002007").send_keys("")
        self.find_element(By.ID,"01002007").send_keys("1")
        self.find_element(By.ID,"01002008").click()
        self.find_element(By.ID,"01002008").send_keys("")
        self.find_element(By.ID,"01002008").send_keys("1")
        self.find_element(By.ID,"01002009").click()
        self.find_element(By.ID,"01002009").send_keys("")
        self.find_element(By.ID,"01002009").send_keys("1")
        self.find_element(By.ID,"01002010").click()
        self.select_Option(driver,self.find_element(By.ID,"01002010"),"変化なし")
        self.find_element(By.ID,"01003001").click()
        self.select_Option(driver,self.find_element(By.ID,"01003001"),"完了")
        self.find_element(By.ID,"01004001").click()
        self.find_element(By.ID,"01004001").send_keys("")
        self.find_element(By.ID,"01004001").send_keys("1")
        self.find_element(By.ID,"01005001").click()
        self.find_element(By.ID,"01005001").send_keys("")
        self.find_element(By.ID,"01005001").send_keys("1")
        self.find_element(By.ID,"01006001").click()
        self.find_element(By.ID,"01006001").send_keys("")
        self.find_element(By.ID,"01006001").send_keys("20210401")
        """
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20210401")
        # self.find_element(By.ID,"TxtJushinBasho").click()
        # self.find_element(By.ID,"TxtJushinBasho").send_keys("")
        # self.find_element(By.ID,"TxtJushinBasho").send_keys("0000000001")
        # self.find_element(By.ID,"CmbShudanKobetsu").click()
        # self.select_Option(driver,self.find_element(By.ID,"CmbShudanKobetsu"),"個別")
        # self.find_element(By.ID,"01001005").click()
        # self.find_element(By.ID,"01001005").send_keys("")
        # self.find_element(By.ID,"01001005").send_keys("123")
        self.find_element(By.ID,"01001001").click()
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys("利用")
        self.find_element(By.ID,"01001002").click()
        self.find_element(By.ID,"01001002").send_keys("")
        self.find_element(By.ID,"01001002").send_keys("積極的支援")

        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-35" , True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAH006//" + "QAH006-006-2-35-3" +".png")

        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdHyoujiKirikae")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-37" , True)

        self.find_element(By.ID,"CmdHyoujiKirikae").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdHokenShidouKekka3")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-39" , True)

        self.find_element(By.ID,"CmdHokenShidouKekka3").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdSusumi")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-41" , True)

        self.find_element(By.ID,"CmdSusumi").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdModori")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-42" , True)

        self.find_element(By.ID,"CmdModori").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_Btn_QAH_Calendar0")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-43" , True)

        self.find_element(By.ID,"span_Btn_QAH_Calendar0").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-44" , True)
        # 令和02年選択        
        # time.sleep(10)

        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | index=0 | ]]
        #self.find_element(By.XPATH,u"//td[@onclick=\"button_click('令和02年');\"]").click()
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | relative=parent | ]]

        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdTouNendo")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-45" , True)

        self.find_element(By.ID,"span_CmdTouNendo").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "SelectZissekiKubun")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-46" , True)

        self.find_element(By.ID,"SelectZissekiKubun").click()
        self.select_Option(driver,self.find_element(By.ID,"SelectZissekiKubun"),"初回")

        self.find_element(By.ID,"CmdZissekiAdd").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdTsuika")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-49" , True)

        self.find_element(By.ID,"span_CmdTsuika").click()
        # # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmbFukushi")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-50" , True)
        """
        self.find_element(By.ID,"CmbFukushi").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbFukushi"),"第一区")
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20210401")
        self.find_element(By.ID,"TxtJushinBasho").click()
        self.find_element(By.ID,"TxtJushinBasho").send_keys("")
        self.find_element(By.ID,"TxtJushinBasho").send_keys("0000000001")
        self.find_element(By.ID,"01001001").click()
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys("1")
        self.find_element(By.ID,"01001002").click()
        self.find_element(By.ID,"01001002").send_keys("")
        self.find_element(By.ID,"01001002").send_keys("1")
        self.find_element(By.ID,"01001003").click()
        self.find_element(By.ID,"01001003").send_keys("")
        self.find_element(By.ID,"01001003").send_keys("1")
        self.find_element(By.ID,"01001004").click()
        self.select_Option(driver,self.find_element(By.ID,"01001004"),"積極的支援")
        self.find_element(By.ID,"01001005").click()
        self.select_Option(driver,self.find_element(By.ID,"01001005"),"意思なし")
        self.find_element(By.ID,"01001006").click()
        self.find_element(By.ID,"01001006").send_keys("")
        self.find_element(By.ID,"01001006").send_keys("1")
        self.find_element(By.ID,"01001007").click()
        self.select_Option(driver,self.find_element(By.ID,"01001007"),"個別支援")
        self.find_element(By.ID,"01001008").click()
        self.find_element(By.ID,"01001008").send_keys("")
        self.find_element(By.ID,"01001008").send_keys("1")
        self.find_element(By.ID,"01001009").click()
        self.select_Option(driver,self.find_element(By.ID,"01001009"),"医師")
        self.find_element(By.ID,"01001010").click()
        self.find_element(By.ID,"01001010").send_keys("")
        self.find_element(By.ID,"01001010").send_keys("1")
        self.find_element(By.ID,"01001011").click()
        self.find_element(By.ID,"01001011").send_keys("")
        self.find_element(By.ID,"01001011").send_keys("1")
        self.find_element(By.ID,"01001012").click()
        self.find_element(By.ID,"01001012").send_keys("")
        self.find_element(By.ID,"01001012").send_keys("1")
        self.find_element(By.ID,"01001013").click()
        self.find_element(By.ID,"01001013").send_keys("")
        self.find_element(By.ID,"01001013").send_keys("1")
        self.find_element(By.ID,"01001014").click()
        self.find_element(By.ID,"01001014").send_keys("")
        self.find_element(By.ID,"01001014").send_keys("1")
        self.find_element(By.ID,"01001015").click()
        self.find_element(By.ID,"01001015").send_keys("")
        self.find_element(By.ID,"01001015").send_keys("1")
        self.find_element(By.ID,"01001016").click()
        self.find_element(By.ID,"01001016").send_keys("")
        self.find_element(By.ID,"01001016").send_keys("1")
        self.find_element(By.ID,"01001017").click()
        self.find_element(By.ID,"01001017").send_keys("")
        self.find_element(By.ID,"01001017").send_keys("1")
        self.find_element(By.ID,"01001018").click()
        self.find_element(By.ID,"01001018").send_keys("")
        self.find_element(By.ID,"01001018").send_keys("1")
        self.find_element(By.ID,"01001019").click()
        self.find_element(By.ID,"01001019").send_keys("")
        self.find_element(By.ID,"01001019").send_keys("1")
        self.find_element(By.ID,"01001020").click()
        self.find_element(By.ID,"01001020").send_keys("")
        self.find_element(By.ID,"01001020").send_keys("1")
        self.find_element(By.ID,"01001021").click()
        self.find_element(By.ID,"01001021").send_keys("")
        self.find_element(By.ID,"01001021").send_keys("1")
        self.find_element(By.ID,"01001022").click()
        self.find_element(By.ID,"01001022").send_keys("")
        self.find_element(By.ID,"01001022").send_keys("1")
        self.find_element(By.ID,"01001023").click()
        self.find_element(By.ID,"01001023").send_keys("")
        self.find_element(By.ID,"01001023").send_keys("1")
        self.find_element(By.ID,"01001024").click()
        self.find_element(By.ID,"01001024").send_keys("")
        self.find_element(By.ID,"01001024").send_keys("1")
        self.find_element(By.ID,"01001025").click()
        self.find_element(By.ID,"01001025").send_keys("")
        self.find_element(By.ID,"01001025").send_keys("1")
        self.find_element(By.ID,"01001026").click()
        self.find_element(By.ID,"01001026").send_keys("")
        self.find_element(By.ID,"01001026").send_keys("1")
        self.find_element(By.ID,"01001027").click()
        self.find_element(By.ID,"01001027").send_keys("")
        self.find_element(By.ID,"01001027").send_keys("1")
        self.find_element(By.ID,"01001028").click()
        self.find_element(By.ID,"01001028").send_keys("")
        self.find_element(By.ID,"01001028").send_keys("1")
        self.find_element(By.ID,"01001029").click()
        self.find_element(By.ID,"01001029").send_keys("")
        self.find_element(By.ID,"01001029").send_keys("1")
        self.find_element(By.ID,"01001030").click()
        self.find_element(By.ID,"01001030").send_keys("")
        self.find_element(By.ID,"01001030").send_keys("1")
        self.find_element(By.ID,"01001031").click()
        self.find_element(By.ID,"01001031").send_keys("")
        self.find_element(By.ID,"01001031").send_keys("1")
        self.find_element(By.ID,"01001032").click()
        self.find_element(By.ID,"01001032").send_keys("")
        self.find_element(By.ID,"01001032").send_keys("1")
        self.find_element(By.ID,"01001033").click()
        self.find_element(By.ID,"01001033").send_keys("")
        self.find_element(By.ID,"01001033").send_keys("1")
        self.find_element(By.ID,"01001034").click()
        self.find_element(By.ID,"01001034").send_keys("")
        self.find_element(By.ID,"01001034").send_keys("1")
        self.find_element(By.ID,"01001035").click()
        self.find_element(By.ID,"01001035").send_keys("")
        self.find_element(By.ID,"01001035").send_keys("20210401")
        self.find_element(By.ID,"01001036").click()
        self.select_Option(driver,self.find_element(By.ID,"01001036"),"初回")
        self.find_element(By.ID,"01001037").click()
        self.find_element(By.ID,"01001037").send_keys("")
        self.find_element(By.ID,"01001037").send_keys("1")
        """
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20210401")
        self.find_element(By.ID,"TxtJushinBasho").click()
        self.find_element(By.ID,"TxtJushinBasho").send_keys("")
        self.find_element(By.ID,"TxtJushinBasho").send_keys("0000000001")
        # self.find_element(By.ID,"CmbShudanKobetsu").click()
        # self.select_Option(driver,self.find_element(By.ID,"CmbShudanKobetsu"),"個別")
        # self.find_element(By.ID,"01001005").click()
        # self.find_element(By.ID,"01001005").send_keys("")
        # self.find_element(By.ID,"01001005").send_keys("321")
        self.find_element(By.ID,"01001001").click()
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys("6")

        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdGyomuKirikae2")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-52" , True)

        self.find_element(By.ID,"span_CmdGyomuKirikae2").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdGyomuKirikae3")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-53" , True)

        self.find_element(By.ID,"span_CmdGyomuKirikae3").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdGyomuKirikae4")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-54" , True)

        self.find_element(By.ID,"span_CmdGyomuKirikae4").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdGyomuKirikae5")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-55" , True)

        self.find_element(By.ID,"span_CmdGyomuKirikae5").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-56" , True)
        
        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-58" , True)

        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdHyoujiKirikae")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-60" , True)

        self.find_element(By.ID,"CmdHyoujiKirikae").click()

        self.find_element(By.ID,"CmdJyogai").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdTsuika")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-62" , True)

        self.find_element(By.ID,"CmdTsuika").click()
        # self.find_element(By.ID,"CmbJyogaiJiyu").click()
        # self.select_Option(driver,self.find_element(By.ID,"CmbJyogaiJiyu"),"除外事由1")
        self.find_element(By.ID,"CmbJyogaiJiyu").send_keys("除外事由1")
        # self.find_element(By.ID,"CmbKaijyoJiyu").click()
        # self.select_Option(driver,self.find_element(By.ID,"CmbKaijyoJiyu"),"解除事由1")
        self.find_element(By.ID,"CmbKaijyoJiyu").send_keys("解除事由1")
        self.find_element(By.ID,"TxtJyogaiYMD").click()
        self.find_element(By.ID,"TxtJyogaiYMD").send_keys("")
        self.find_element(By.ID,"TxtJyogaiYMD").send_keys("20210501")
        self.find_element(By.ID,"TxtKaijyoYMD").click()
        self.find_element(By.ID,"TxtKaijyoYMD").send_keys("")
        self.find_element(By.ID,"TxtKaijyoYMD").send_keys("20210601")

        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdShuusei")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-65" , True)

        self.find_element(By.ID,"CmdShuusei").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "TxtJyogaiYMD")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-66" , True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAH006//" + "QAH006-006-2-66-2" +".png")

        self.find_element(By.ID,"TxtJyogaiYMD").click()
        self.find_element(By.ID,"TxtKaijyoYMD").click()
        self.find_element(By.ID,"TxtKaijyoYMD").send_keys("")
        self.find_element(By.ID,"TxtKaijyoYMD").send_keys("20210605")

        
        self.find_element(By.ID,"span_CmdShoki").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdShuusei")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-68" , True)

        self.find_element(By.ID,"span_CmdShuusei").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "TxtKaijyoYMD")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-69" , True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAH006//" + "QAH006-006-2-69-2" +".png")

        self.find_element(By.ID,"TxtKaijyoYMD").click()
        self.find_element(By.ID,"TxtKaijyoYMD").send_keys("")
        self.find_element(By.ID,"TxtKaijyoYMD").send_keys("20210605")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-71" , True)

        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdJyogai")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-73" , True)

        self.find_element(By.ID,"CmdJyogai").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdSakujo")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-75" , True)

        
        self.find_element(By.ID,"CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-76" , True)

        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdJyushinken")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-78" , True)

        self.find_element(By.ID,"span_CmdJyushinken").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdTsuika")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-80" , True)

        self.find_element(By.ID,"span_CmdTsuika").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmbFukushi")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-81" , True)

        # self.find_element(By.ID,"CmbFukushi").click()
        # self.select_Option(driver,self.find_element(By.ID,"CmbFukushi"),"第一区")
        self.find_element(By.ID,"CmbFukushi").send_keys("第一区")
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20210401")
      
        self.find_element(By.ID,"01001001").click()
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys("1")
        # self.find_element(By.ID,"01001002").click()
        # self.select_Option(driver,self.find_element(By.ID,"01001002"),"特定健康診査")
        self.find_element(By.ID,"01001002").send_keys("特定健康診査")
        self.find_element(By.ID,"01001003").click()
        self.find_element(By.ID,"01001003").send_keys("")
        self.find_element(By.ID,"01001003").send_keys("20210401")
        self.find_element(By.ID,"01001004").click()
        self.find_element(By.ID,"01001004").send_keys("")
        self.find_element(By.ID,"01001004").send_keys("1")
        self.find_element(By.ID,"textArea01002001").click()
        self.find_element(By.ID,"textArea01002001").send_keys("")
        self.find_element(By.ID,"textArea01002001").send_keys("1")
        self.find_element(By.ID,"01003001").click()
        self.find_element(By.ID,"01003001").send_keys("")
        self.find_element(By.ID,"01003001").send_keys("20210401")
        """
        self.find_element(By.ID,"TxtJushinBasho").click()
        self.find_element(By.ID,"TxtJushinBasho").send_keys("")
        self.find_element(By.ID,"TxtJushinBasho").send_keys("0000000001")
        self.find_element(By.ID,"CmbShudanKobetsu").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbShudanKobetsu"),"個別")
        self.find_element(By.ID,"01001001").click()
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys("80004")
        self.find_element(By.ID,"01001002").click()
        self.select_Option(driver,self.find_element(By.ID,"01001002"),"特定健診")

        """
        
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-83" , True)

        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdRiyouken")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-85" , True)

        self.find_element(By.ID,"span_CmdRiyouken").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdTsuika")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-87" , True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAH006//" + "QAH006-006-2-87-3" +".png")
        
        self.find_element(By.ID,"span_CmdTsuika").click()

        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmbFukushi")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-88" , True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAH006//" + "QAH006-006-2-88-3" +".png")

        # self.find_element(By.ID,"CmbFukushi").click()
        # self.select_Option(driver,self.find_element(By.ID,"CmbFukushi"),"第一区")
        self.find_element(By.ID,"CmbFukushi").send_keys("第一区")
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20210401")
        self.find_element(By.ID,"01001001").click()
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys("1")
        self.find_element(By.ID,"01002001").click()
        self.find_element(By.ID,"01002001").send_keys("")
        self.find_element(By.ID,"01002001").send_keys("1")
        self.find_element(By.ID,"01003001").click()
        self.find_element(By.ID,"01003001").send_keys("")
        self.find_element(By.ID,"01003001").send_keys("1")
        self.find_element(By.ID,"01004001").click()
        self.find_element(By.ID,"01004001").send_keys("")
        self.find_element(By.ID,"01004001").send_keys("20210401")
        self.find_element(By.ID,"01005001").click()
        self.find_element(By.ID,"01005001").send_keys("")
        self.find_element(By.ID,"01005001").send_keys("20210401")
        # self.find_element(By.ID,"01006001").click()
        # self.select_Option(driver,self.find_element(By.ID,"01006001"),"負担なし")
        self.find_element(By.ID,"01006001").send_keys("負担なし")
        self.find_element(By.ID,"01006002").click()
        self.find_element(By.ID,"01006002").send_keys("")
        self.find_element(By.ID,"01006002").send_keys("1")
        self.find_element(By.ID,"01006003").click()
        self.find_element(By.ID,"01006003").send_keys("")
        self.find_element(By.ID,"01006003").send_keys("1")
        self.find_element(By.ID,"01006004").click()
        self.find_element(By.ID,"01006004").send_keys("")
        self.find_element(By.ID,"01006004").send_keys("1")
        self.find_element(By.ID,"01006005").click()
        self.find_element(By.ID,"01006005").send_keys("")
        self.find_element(By.ID,"01006005").send_keys("1")
        self.find_element(By.ID,"01007001").click()
        self.find_element(By.ID,"01007001").send_keys("")
        self.find_element(By.ID,"01007001").send_keys("1")
        self.find_element(By.ID,"01008001").click()
        self.find_element(By.ID,"01008001").send_keys("")
        self.find_element(By.ID,"01008001").send_keys("1")
        # self.find_element(By.ID,"01009001").click()
        # self.select_Option(driver,self.find_element(By.ID,"01009001"),"特定健康診査")
        self.find_element(By.ID,"01009001").send_keys("特定健康診査")
        # self.find_element(By.ID,"01010001").click()
        # self.select_Option(driver,self.find_element(By.ID,"01010001"),"積極的支援")
        self.find_element(By.ID,"01010001").send_keys("積極的支援")
        self.find_element(By.ID,"01011001").click()
        self.find_element(By.ID,"01011001").send_keys("")
        self.find_element(By.ID,"01011001").send_keys("20210401")
        """
        self.save_screenshot_migrate(driver, "QAH006-006-2-88" , True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//"  + "QAH006-006-2-88-3" +".png")
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20210401")
        self.find_element(By.ID,"TxtJushinBasho").click()
        self.find_element(By.ID,"TxtJushinBasho").send_keys("")
        self.find_element(By.ID,"TxtJushinBasho").send_keys("0000000001")
        self.find_element(By.ID,"CmbShudanKobetsu").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbShudanKobetsu"),"個別")
        self.find_element(By.ID,"01001001").click()
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys("123")
        """
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-90" , True)

        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdHyouka")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-92" , True)

        self.find_element(By.ID,"CmdHyouka").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_BtnTaiJyu")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-94" , True)

        self.find_element(By.ID,"span_BtnTaiJyu").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "BtnBMI")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-95" , True)

        self.find_element(By.ID,"BtnBMI").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "BtnSyuShikuKi")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-96" , True)

        self.find_element(By.ID,"BtnSyuShikuKi").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "BtnKakuCyoKi")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-97" , True)

        self.find_element(By.ID,"BtnKakuCyoKi").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdModori")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-98" , True)

        self.find_element(By.ID,"CmdModori").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdSusumi")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-99" , True)

        self.find_element(By.ID,"CmdSusumi").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_Btn_QAH_Calendar0")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-100" , True)

        self.find_element(By.ID,"span_Btn_QAH_Calendar0").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-102" , True)
        # 令和02年選択
        # time.sleep(10)

        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | index=0 | ]]
        #self.find_element(By.XPATH,u"//td[@onclick=\"button_click('令和02年');\"]").click()
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | relative=parent | ]]

        self.find_element(By.ID,"span_CmdTouNendo").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdKomento")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-103" , True)

        self.find_element(By.ID,"span_CmdKomento").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdTsuika")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-105" , True)

        self.find_element(By.ID,"span_CmdTsuika").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmbComentoShurui")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-106" , True)

        # self.find_element(By.ID,"CmbComentoShurui").click()
        # # self.select_Option(driver,self.find_element(By.ID,"CmbComentoShurui"),"食事・運動習慣ともに改善されています。")
        # self.select_Option(driver,self.find_element(By.ID,"CmbComentoShurui"),"改善点(食事)")
        self.find_element(By.ID,"CmbComentoShurui").send_keys("改善点(食事)")
        self.find_element(By.ID,"TxtComento").click()
        self.find_element(By.ID,"TxtComento").send_keys("")
        self.find_element(By.ID,"TxtComento").send_keys("1")

        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdShuusei")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-108" , True)

        self.find_element(By.ID,"CmdShuusei").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "TxtComento")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-109" , True)

        self.find_element(By.ID,"TxtComento").click()
        self.find_element(By.ID,"TxtComento").send_keys("")
        self.find_element(By.ID,"TxtComento").send_keys("2")

        
        self.find_element(By.ID,"span_CmdShoki").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "span_CmdShuusei")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-111" , True)

        self.find_element(By.ID,"span_CmdShuusei").click()
        self.find_element(By.ID,"TxtComento").click()
        self.find_element(By.ID,"TxtComento").send_keys("")
        self.find_element(By.ID,"TxtComento").send_keys("3")

        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-114" , True)

        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdKomento")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-116" , True)

        self.find_element(By.ID,"CmdKomento").click()
        
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdSakujo")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-118" , True)

        self.find_element(By.ID,"CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-119" , True)

        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-121" , True)

        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmdPrintOut")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-123" , True)

        self.find_element(By.ID,"CmdPrintOut").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-125" , True)

        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-127" , True)

        self.find_element(By.ID,"GOBACK").click()
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "GOBACK")))
        self.save_screenshot_migrate(driver, "QAH006-006-2-129" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-130", True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-131", True)	
        

        
    
        