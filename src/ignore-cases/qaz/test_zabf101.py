import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTZABF101(FukushiSiteTestCaseBase):
    """TESTZABF101"""
    
    def test_case_zabf101_001(self):
        """test_case_zabf101_001"""
        driver = None
        test_data = self.common_test_data
        self.do_login()

        self.click_button_by_label("入院時支援金申請入力")
        self.save_screenshot_migrate(driver, "ZABF101-001-02", True)
        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("ZABF101_atena_code"))
        self.find_element(By.ID, "<PERSON><PERSON><PERSON>").click()
        self.save_screenshot_migrate(driver, "ZABF101-001-05", True)
        self.find_element(By.ID, "span_CmdTsuika").click()
        self.find_element(By.ID, "span_CmdShinai1").click()
        self.save_screenshot_migrate(driver, "ZABF101-001-08", True)

        self.find_element(By.ID, "Kana1").click()
        self.find_element(By.ID, "Kana1").send_keys("ｶﾅ")
        self.find_element(By.ID, "span_KensakuKana").click()
        self.find_element(By.ID, "JushoCode2").click()
        self.find_element(By.ID, "JushoCode2").send_keys("204")
        self.find_element(By.ID, "JushoCode3").click()
        self.find_element(By.ID, "JushoCode3").send_keys("0045")
        self.find_element(By.ID, "JushoCode4").click()
        self.find_element(By.ID, "JushoCode4").send_keys("001")
        self.find_element(By.ID,"P").send_keys("○番地○-○-○")
        self.find_element(By.ID, "Banchi").click()
        self.find_element(By.ID, "Banchi").send_keys("2")
        self.find_element(By.ID, "Go").click()
        self.find_element(By.ID, "Go").send_keys("2")
        self.find_element(By.ID, "Goedaban").click()
        self.find_element(By.ID, "Goedaban").send_keys("2")
        self.find_element(By.ID, "Gokoedaban").click()
        self.find_element(By.ID, "Gokoedaban").send_keys("3")
        self.find_element(By.ID, "span_Check").click()
        self.save_screenshot_migrate(driver, "ZABF101-001-14", True)

        self.find_element(By.ID, "Yubin1").click()
        self.find_element(By.ID, "Yubin1").send_keys("891")
        self.find_element(By.ID, "Yubin2").click()
        self.find_element(By.ID, "Yubin2").send_keys("0403")
        self.find_element(By.ID, "KensakuYubinBango").click()
        self.find_element(By.ID,"P").send_keys("○番地○-○-○")
        self.find_element(By.ID, "Banchi").click()
        self.find_element(By.ID, "Banchi").send_keys("2")
        self.find_element(By.ID, "Go").click()
        self.find_element(By.ID, "Go").send_keys("2")
        self.find_element(By.ID, "Goedaban").click()
        self.find_element(By.ID, "Goedaban").send_keys("2")
        self.find_element(By.ID, "Gokoedaban").click()
        self.find_element(By.ID, "Gokoedaban").send_keys("3")
        self.find_element(By.ID, "Check").click()
        self.save_screenshot_migrate(driver, "ZABF101-001-19", True)

        self.find_element(By.ID, "Kanji1").click()
        self.find_element(By.ID, "Kanji1").send_keys("十二町")
        self.find_element(By.ID, "span_KensakuKanji").click()
        self.find_element(By.ID, "Sel202170000173").click()
        self.find_element(By.ID,"P").send_keys("○番地○-○-○")
        self.find_element(By.ID, "Banchi").click()
        self.find_element(By.ID, "Banchi").send_keys("2")
        self.find_element(By.ID, "Go").click()
        self.find_element(By.ID, "Go").send_keys("2")
        self.find_element(By.ID, "Goedaban").click()
        self.find_element(By.ID, "Goedaban").send_keys("2")
        self.find_element(By.ID, "Gokoedaban").click()
        self.find_element(By.ID, "Gokoedaban").send_keys("3")
        self.find_element(By.ID, "Yubin1").click()
        self.find_element(By.ID, "Yubin1").send_keys("111")
        self.find_element(By.ID, "Yubin2").click()
        self.find_element(By.ID, "Yubin2").send_keys("0011")
        self.find_element(By.ID, "span_Check").click()
        self.save_screenshot_migrate(driver, "ZABF101-001-26", True)
        self.find_element(By.ID, "span_Commit").click()
        self.save_screenshot_migrate(driver, "ZABF101-001-28", True)

        self.find_element(By.ID, "span_CmdShinai1").click()
        self.save_screenshot_migrate(driver, "ZABF101-001-30", True)
        self.find_element(By.ID, "Kana1").click()
        self.find_element(By.ID, "Kana1").send_keys("ｶﾅ")
        self.find_element(By.ID, "span_KensakuKana").click()
        self.find_element(By.ID, "span_SortKey").click()
        self.save_screenshot_migrate(driver, "ZABF101-001-34", True)
        self.find_element(By.ID, "span_SortKey").click()
        self.save_screenshot_migrate(driver, "ZABF101-001-36", True)
        self.find_element(By.ID, "span_Clear").click()
        self.save_screenshot_migrate(driver, "ZABF101-001-38", True)
    
        