import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAKF104(FukushiSiteTestCaseBase):
    """TESTRAKF104"""

    def test_case_rakf104_001(self):
        """test_case_rakf104_001"""
        driver = None
        test_data = self.common_test_data
        self.do_login()

        self.click_button_by_label("資格管理")
        self.save_screenshot_migrate(driver, "RAKF104-001-02", True)

        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("rakf104_atena_code"))
        self.find_element(By.ID, "<PERSON>sa<PERSON>").click()
        self.save_screenshot_migrate(driver, "RAKF104-001-05", True)

        self.find_common_buttons()
        self.common_button_click(button_text="被保険者証発行")
        self.save_screenshot_migrate(driver, "RAKF104-001-07", True)

        self.find_common_buttons()
        self.common_button_click(button_text="被保険者証発行履歴")
        self.save_screenshot_migrate(driver, "RAKF104-001-09", True)

        self.find_element(By.ID, "CmdNumber1").click()
        self.save_screenshot_migrate(driver, "RAKF104-001-11", True)

        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"GOBACK").click()

    
        