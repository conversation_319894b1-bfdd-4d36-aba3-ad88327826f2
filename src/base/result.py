import os
import sys
import time
import copy
import traceback
from unittest import TestResult, TextTestResult
from unittest.result import failfast

class WebRingsSiteTestResult(TextTestResult):

    start_time = None
    stop_time = None
    default_prefix = "TestResults_"

    def __init__(self, stream, descriptions, verbosity):
        self.buffer = True
        self._stdout_data = None
        self._stderr_data = None
        self.successes = []
        self.subtests = {}
        self.callback = None
        self.report_files = []
        self.test_start_time = None
        self.test_stop_time = None
        super().__init__(stream=stream, descriptions=descriptions, verbosity=verbosity)

    def getDescription(self, test):
        doc_first_line = test.shortDescription()
        if self.descriptions and doc_first_line:
            return ' '.join((str(test), doc_first_line))
        else:
            return str(test)

    def startTest(self, test):
        """ Called before execute each method. """
        self.start_time = time.time()
        TestResult.startTest(self, test)
        if self.showAll:
            self.stream.write(" " + self.getDescription(test))
            self.stream.write(" ..... ")

    def stopTest(self, test):
        """ Called after excute each test method. """
        TextTestResult.stopTest(self, test)
        self.stop_time = time.time()

    def addSuccess(self, test):
        """ Called when a test executes successfully. """
        self.successes.append(test)
        self.stream.writeln("ok!!!")
