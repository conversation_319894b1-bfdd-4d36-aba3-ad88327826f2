DELETE FROM WR$$JICHITAI_CODE$$QZ..QZ文言管理マスタ WHERE 自治体コード = '99101' AND 業務コード = 'QAA020' AND 帳票ID = 'AB040201' AND データ作成担当者 = 'ST_QMAF017';

INSERT INTO WR$$JICHITAI_CODE$$QZ..QZ文言管理マスタ
(自治体コード, 福祉事務所コード, 支所コード, 業務コード, 帳票ID, 文言種類, 連番, 有効期間開始, 有効期間終了, 文言名, 文面内容, 削除フラグ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム)VALUES
('99101', '00000', '000', 'QAA020', 'AB040101', '1', '1', '00000000', '99999999', '', '　上記のものは、下記のとおり知的障害者福祉法により、療育手帳の交付を受けたもので', '1', '案件番号【17919】', '案件番号【17919】', getdate(), getdate(), '00000000'),
('99101', '00000', '000', 'QAA020', 'AB040101', '1', '2', '00000000', '99999999', '', 'あることを証明します。', '0', '案件番号【17919】', '案件番号【17919】', getdate(), getdate(), '00000000');

INSERT INTO WR$$JICHITAI_CODE$$QZ..QZ文言管理マスタ
(自治体コード, 福祉事務所コード, 支所コード, 業務コード, 帳票ID, 文言種類, 連番, 有効期間開始, 有効期間終了, 文言名, 文面内容, 削除フラグ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム)VALUES
('99101', '00000', '000', 'QAA020', 'AB040201', '99', '1', '00000000', '99999999', '', 'STテスト用文言１', '0', 'ST_QMAF017', 'ST_QMAF017', getdate(), getdate(), '00000000'),
('99101', '00000', '000', 'QAA020', 'AB040201', '99', '2', '00000000', '99999999', '', 'STテスト用文言２', '0', 'ST_QMAF017', 'ST_QMAF017', getdate(), getdate(), '00000000'),
('99101', '00000', '000', 'QAA020', 'AB040201', '99', '3', '00000000', '99999999', '', 'STテスト用文言３', '0', 'ST_QMAF017', 'ST_QMAF017', getdate(), getdate(), '00000000'),
('99101', '00000', '000', 'QAA020', 'AB040201', '99', '4', '00000000', '99999999', '', 'STテスト用文言４', '0', 'ST_QMAF017', 'ST_QMAF017', getdate(), getdate(), '00000000'),
('99101', '00000', '000', 'QAA020', 'AB040201', '99', '5', '00000000', '99999999', '', 'STテスト用文言５', '0', 'ST_QMAF017', 'ST_QMAF017', getdate(), getdate(), '00000000'),
('99101', '00000', '000', 'QAA020', 'AB040201', '99', '6', '00000000', '99999999', '', 'STテスト用文言６', '0', 'ST_QMAF017', 'ST_QMAF017', getdate(), getdate(), '00000000'),
('99101', '00000', '000', 'QAA020', 'AB040201', '99', '7', '00000000', '99999999', '', 'STテスト用文言７', '0', 'ST_QMAF017', 'ST_QMAF017', getdate(), getdate(), '00000000'),
('99101', '00000', '000', 'QAA020', 'AB040201', '99', '8', '00000000', '99999999', '', 'STテスト用文言８', '0', 'ST_QMAF017', 'ST_QMAF017', getdate(), getdate(), '00000000');

