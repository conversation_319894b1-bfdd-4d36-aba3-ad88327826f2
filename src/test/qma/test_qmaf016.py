
from base.fukushi_case import FukushiSiteTestCaseBase
from selenium.webdriver.common.by import By

class Test_QMAF016(FukushiSiteTestCaseBase):
    """Test_QMAF016"""
    def setUp(self):
        test_data = self.common_test_data
        #atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("qmaf016.sql")
        super().setUp() 


    def test_case_001(self):       
        """test_case_001"""
        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.click_button_by_label("手帳コードマスタメンテナンス")
        self.screen_shot("QMAF016_1",caption="QMAF016_画面初期化")

        self.driver.find_element(By.ID, "CmbTeTyoCode").click()
        dropdown = self.driver.find_element(By.ID, "CmbTeTyoCode")
        dropdown.find_element(By.XPATH, "//option[. = '********** : 障害名接続文字列']").click()
        self.driver.find_element(By.ID, "TxtDate").click()
        self.driver.find_element(By.ID, "TxtDate").send_keys("令和05年11月24日")
        self.driver.find_element(By.ID, "ChkDelete").click()
        self.driver.find_element(By.ID, "span_CmdSearch").click()
        self.screen_shot("QMAF016_2",caption="QMAF016_画面検索結果")
        self.driver.find_element(By.ID, "span_CmdAdd").click()
        self.screen_shot("QMAF016_3",caption="QMAF016_追加ボタン")       
        self.driver.find_element(By.ID, "TxtRegCode").click()
        self.driver.find_element(By.ID, "TxtRegCode").send_keys("0000000002")
        self.driver.find_element(By.ID, "TxtRegCodeShort").click()
        self.driver.find_element(By.ID, "TxtRegCodeShort").send_keys("コード略称２")
        self.driver.find_element(By.ID, "TxtRegCodeName").click()
        self.driver.find_element(By.ID, "TxtRegCodeName").send_keys("コード名称２")
        self.driver.find_element(By.ID, "TxtRegEnableStartDate").click()
        self.driver.find_element(By.ID, "TxtRegEnableStartDate").send_keys("令和05年11月24日")        
        self.driver.find_element(By.ID, "CmdRegist").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMAF016_4",caption="QMAF016_登録／復活ボタン押下_I01")
        self.driver.find_element(By.ID, "Sel2").click()
        self.driver.find_element(By.ID, "span_CmdEdit").click()
        self.screen_shot("QMAF016_5",caption="QMAF016_修正")        
        self.driver.find_element(By.ID, "TxtRegCodeName").click()
        self.driver.find_element(By.ID, "TxtRegCodeName").send_keys("コード名称２修正")
        self.driver.find_element(By.ID, "span_CmdRegist").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMAF016_6",caption="QMAF016_登録／復活ボタン押下_U01")       
        self.driver.find_element(By.ID, "Sel2").click()
        self.driver.find_element(By.ID, "span_CmdDelete").click()
        assert self.driver.switch_to.alert.text == "削除します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMAF016_7",caption="QMAF016_削除ボタン押下_D01")   
        self.driver.find_element(By.ID, "span_CmdUserClear").click()
        self.screen_shot("QMAF016_8",caption="QMAF016_検索条件クリア")           
        self.driver.find_element(By.ID, "CmdClear").click()
        self.screen_shot("QMAF016_9",caption="QMAF016_初期表示")              
        self.driver.find_element(By.ID, "CmbTeTyoCode").click()
        dropdown = self.driver.find_element(By.ID, "CmbTeTyoCode")
        dropdown.find_element(By.XPATH, "//option[. = '********** : 身障手帳等級']").click()
        self.driver.find_element(By.ID, "span_CmdSearch").click()
        self.driver.find_element(By.ID, "span_CmdAdd").click()
        self.driver.find_element(By.ID, "span_CmdTyusi").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.screen_shot("QMAF016_10",caption="QMAF016_登録内容取消")            
        self.driver.find_element(By.ID, "CmbPage").click()
        dropdown = self.driver.find_element(By.ID, "CmbPage")
        dropdown.find_element(By.XPATH, "//option[. = '03']").click()
        self.driver.find_element(By.ID, "CmdJumpPage").click()
        self.screen_shot("QMAF016_11",caption="QMAF016_へ移動")              
        self.driver.find_element(By.ID, "CmdNextPage").click()
        self.screen_shot("QMAF016_12",caption="QMAF016_次頁")            
        self.driver.find_element(By.ID, "CmdBackPage").click()
        self.screen_shot("QMAF016_13",caption="QMAF016_前頁")        