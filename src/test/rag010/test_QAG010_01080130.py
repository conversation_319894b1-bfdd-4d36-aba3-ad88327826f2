import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

#転用元シナリオ:TestQAG010_01080110
class TestQAG010_01080130(FukushiSiteTestCaseBase):
    """TestQAG010_01080130"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 新規申請した住民に対し却下登録ができることを確認する。
    def test_QAG010_01080130(self):
        """認定結果の登録_却下_"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        kettei_ymd = case_data.get("kettei_ymd", "")
        kettei_kekka = case_data.get("kettei_kekka", "")
        kettei_riyu = case_data.get("kettei_riyu", "")
        area_kyakka_riyu = case_data.get("area_kyakka_riyu", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG010")
        # 1 自立支援医療(更生医療)資格管理: 「決定内容入力」ボタン押下
        self.click_by_id(idstr="CmdButton1_1")
        self.click_button_by_label("決定内容入力")

        # 2 自立支援医療(更生医療)資格管理: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理_2")

        # 3 自立支援医療(更生医療)資格管理: 決定日「20230701」入力決定結果「却下」選択決定理由「要件非該当のため」選択却下理由「却下理由入力テスト★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★」選択
        self.form_input_by_id(idstr="TxtKetteiYMD", value=kettei_ymd)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=kettei_kekka)
        self.form_input_by_id(idstr="KetteiRiyuCmb", text=kettei_riyu)
        self.form_input_by_id(idstr="TxtAreaKyakkaRiyu", value=area_kyakka_riyu)

        # 4 自立支援医療(更生医療)資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 5 自立支援医療(更生医療)資格管理: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(更生医療)資格管理_5")
