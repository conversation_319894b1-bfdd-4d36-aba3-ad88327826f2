import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG010_01080702(FukushiSiteTestCaseBase):
    """TestQAG010_01080702"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 18歳未満の対象者に対して、申請事由「資格喪失」、申請理由「死亡」の申請を登録できることを確認する。
    def test_QAG010_01080702(self):
        """資格喪失申請情報登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        shinsei_ymd = case_data.get("shinsei_ymd", "")
        soushitsu_ymd = case_data.get("soushitsu_ymd", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG010")
        # 1 自立支援医療(更生医療)資格管理画面: 「申請内容入力」ボタン押下
        self.click_by_id(idstr="CmdButton1_1")
        self.click_button_by_label("申請内容入力")
        time.sleep(2)
        # 2 自立支援医療(更生医療)資格管理画面: 申請事由「資格喪失」選択申請理由「死亡」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("ShinseiShubetsu", ""))
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("ShinseiRiyuu", ""))
        self.screen_shot("自立支援医療(更生医療)資格管理画面_2")

        # 3 自立支援医療(更生医療)資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 4 自立支援医療(更生医療)資格管理画面: 申請日「20241101」入力
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinsei_ymd)

        # 5 自立支援医療(更生医療)資格管理画面: 喪失日「20241101」入力
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value=soushitsu_ymd)
        self.screen_shot("自立支援医療(更生医療)資格管理画面_5")

        # 6 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_6")

        # 7 自立支援医療(更生医療)資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 8 自立支援医療(更生医療)資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(更生医療)資格管理画面_8")
