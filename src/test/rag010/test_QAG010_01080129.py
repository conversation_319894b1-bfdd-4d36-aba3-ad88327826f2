import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

#転用元シナリオ:TestQAG010_01080109
class TestQAG010_01080129(FukushiSiteTestCaseBase):
    """TestQAG010_01080129"""
    # @unittest.skip('skipped')
    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 申請済の住民に対し判定結果登録ができることを確認する
    def test_QAG010_01080129(self):
        """判定結果入力"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        shintatsu_hantei1_ymd = case_data.get("shintatsu_hantei1_ymd", "")
        shintatsu_hantei1 = case_data.get("shintatsu_hantei1", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG010")
        # 1 自立支援医療(更生医療)資格管理: 「判定結果入力」ボタン押下
        self.click_by_id(idstr="CmdButton1_1")
        self.click_button_by_label("判定結果入力")

        # 2 自立支援医療(更生医療)資格管理: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理_2")

        # 3 自立支援医療(更生医療)資格管理: 判定年月日「20230701」入力判定結果「決定」選択
        self.form_input_by_id(idstr="TxtShintatsuHantei1YMD", value=shintatsu_hantei1_ymd)
        self.form_input_by_id(idstr="ShintatsuHantei1Cmb", text=shintatsu_hantei1)

        # 4 自立支援医療(更生医療)資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 5 自立支援医療(更生医療)資格管理: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(更生医療)資格管理_5")
