import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060817(FukushiSiteTestCaseBase):
    """TestQAJ010_01060817"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060817"]
        super().setUp()
    
    # 前履歴以前で決定済みの負担上限額を変更登録できることを確認する。
    def test_QAJ010_01060817(self):
        """負担上限額変更決定登録"""
        
        case_data = self.test_data["TestQAJ010_01060817"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ010")
        # 2 障害福祉サービス申請管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")
        
        # 3 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_3")
        
        # 4 障害福祉サービス決定管理画面: 決定日「20230701」入力、決定結果「支給」選択
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230701")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        
        # 5 障害福祉サービス決定管理画面: 「負担額」ボタン押下
        self.click_button_by_label("負担額")
        
        # 6 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_6")
        
        # 7 障害福祉サービス決定管理画面: 所得区分「一般１」選択、利用者負担上限月額「37200」入力
        self.form_input_by_id(idstr="ShotokuKubunCmb", text="一般１")
        self.form_input_by_id(idstr="TxtFutangakuJogengaku", value="37200")
        self.click_button_by_label("療養介護")
        self.form_input_by_id(idstr="TxtRyouyouKaigoJogengaku", value="1000")
        self.form_input_by_id(idstr="TxtShokujiRyouyouJogengaku", value="1000")
        
        # 8 障害福祉サービス決定管理画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        self.click_button_by_label("入力完了")
        
        # 9 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_9")
        
        # 10 障害福祉サービス申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 11 障害福祉サービス申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害福祉サービス申請管理画面_11")
        
