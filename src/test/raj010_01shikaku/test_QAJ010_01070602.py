import time
import datetime
from datetime import timedelta
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01070602(FukushiSiteTestCaseBase):
    """TestQAJ010_01070602"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01070602"]
        sql_params = {"ATENA_CODE": case_data.get("atena_code", "")}
        super().setUp()
    
        
    def test_QAJ010_01070602(self):
        """RAJJ0062（審査会資料特記事項出力_個別）"""

        date = datetime.date.today()
        today = format(date, '%Y%m%d')

        case_data = self.test_data["TestQAJ010_01070602"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ010")

        # 2 障害福祉サービス申請管理画面: 「特記事項」ボタン押下
        self.click_button_by_label("調査内容等")
        self.click_button_by_label("特記事項")
        self.screen_shot("イメージ確認画面_2")
        
        # 3 イメージ確認画面: 「印刷」ボタン押下
        self.exec_RAJF016_online_print("印刷")

        # 4 イメージ確認画面: 表示
        # Assert: メッセージエリアに「プレビューを表示しました」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        self.screen_shot("イメージ確認画面_4")

        # 5 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_5")
        
        # 6 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 7 イメージ確認画面: 「戻る」ボタン押下
        self.return_click()
        
        # 8 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_8")



