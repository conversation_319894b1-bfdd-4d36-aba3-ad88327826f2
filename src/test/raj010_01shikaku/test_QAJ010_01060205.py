import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060205(FukushiSiteTestCaseBase):
    """TestQAJ010_01060205"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060205"]
        super().setUp()
    
    # 訓練給付の支給決定を登録できることを確認する。 受給者証番号が自動附番されることを確認する。決定サービスを登録できることを確認する。利用者負担額を登録できることを確認する。
    def test_QAJ010_01060205(self):
        """訓練等給付支給決定登録"""
        
        case_data = self.test_data["TestQAJ010_01060205"]
        atena_code = case_data.get("atena_code", "")
        hakkoTxt = case_data.get("hakkoTxt", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ010")
        # 2 障害福祉サービス決定管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")
        
        # 3 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_3")
        
        # 4 障害福祉サービス決定管理画面: 決定日「20230601」入力決定結果「不支給」選択
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230601")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="不支給")
        
        # 5 障害福祉サービス決定管理画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        
        # 6 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_6")
        
        # 7 障害福祉サービス申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 8 障害福祉サービス申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害福祉サービス申請管理画面_8")

        # 9 障害福祉サービス申請管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        
        # 10 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_10")
        
        # 11 帳票印刷画面: 「却下決定通知書（介護給付費等）」行の印刷チェックボックス選択、「却下決定通知書（介護給付費等）」行の発行年月日チェックボックス選択、発行年月日「20230601」入力
        exec_params = [
            {"report_name": case_data.get("report_name",""),
             "params":[
                 {"title": "交付日", "value":hakkoTxt},
                {"title": "文書番号", "value":"11111"}
                ]
            }
        ] 
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_11")
        
        # 12 帳票印刷画面: 「印刷」ボタン押下
        #self.click_button_by_label("印刷")
        
        # 13 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        
        # 14 帳票（PDF）: 表示
        #self.screen_shot("帳票（PDF）_14")
        
        # 15 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 16 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        
        # 17 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_17")

        # 18 障害福祉サービス申請管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        self.click_by_id("CmdKetteiKirikae")
        
        # 19 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_19")
        
        # 20 障害福祉サービス決定管理画面: 決定日「20230601」入力、決定結果「支給」選択
        self.click_button_by_label("決定内容")
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230601")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="支給")
        
        # 21 障害福祉サービス決定管理画面: 特記事項「食費等実費負担に係る境界層措置対象者」選択、その他特記事項「その他特記事項入力テスト」入力、受給者証交付年月日「20230601」入力、決定時備考「決定時備考入力テスト」入力
        self.form_input_by_id(idstr="TokkiJikouCmb_1", text="食費等実費負担に係る境界層措置対象者")
        self.form_input_by_id(idstr="TxtTokkijiko", value="その他特記事項入力テスト")
        self.form_input_by_id(idstr="TxtJukyushashoKofuYMD", value="20230601")
        self.form_input_by_id(idstr="TxtKetteijiBiko", value="決定時備考入力テスト")
        
        # 22 障害福祉サービス決定管理画面: 「決定サービス」ボタン押下
        self.click_button_by_label("決定サービス")
        
        # 23 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_23")
        
        # 24 障害福祉サービス決定管理画面: 開始日「20230601」入力、資格状態「追加」選択、サービス種類「共同生活援助」選択、サービス区分　共同生活援助基本　行の状態区分「追加」選択、サービス区分　共同生活援助基本　以外の行の状態区分「　　」（空白）選択、サービス区分　共同生活援助基本　行の当該月の日数にチェック、サービス決定備考「共同生活援助サービス決定備考入力テスト」入力
        self.form_input_by_id(idstr="TxtKetteiServiceShikyuKetteiYMD", value="20230601")
        self.form_input_by_id(idstr="ServiceShuruiJokyoCmb_1", text="追加")
        self.form_input_by_id(idstr="ServiceShuruiCmb_1", text="共同生活援助")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_1", text="追加")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_2", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_3", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_4", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_5", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_6", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_7", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_8", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_9", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_10", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_11", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_12", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_13", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_14", text="")
        self.form_input_by_id(idstr="TxtTxtShikyuryo1_1_1", value="")
        self.form_input_by_id(idstr="TxtTxtShikyuryo1_1_13", value="")
        self.form_input_by_id(idstr="TxtTxtShikyuryo1_2_13", value="")
        #self.form_input_by_id(idstr="ChkGaitoTsuki1_1_1", value="1")
        self.click_button_by_label("無")
        self.form_input_by_id(idstr="ServiceKetteiBiko_1_1", value="共同生活援助サービス決定備考入力テスト")
        
        # 25 障害福祉サービス決定管理画面: 資格状態「追加」選択、サービス種類「就労移行」選択、サービス区分　就労移行基本　行の状態区分「追加」選択、サービス区分　就労移行基本　以外の行の状態区分「　　」（空白）選択、サービス区分　就労移行基本　行の原則の日数にチェック、サービス決定備考「就労移行サービス決定備考入力テスト」入力
        self.form_input_by_id(idstr="ServiceShuruiJokyoCmb_2", text="追加")
        self.form_input_by_id(idstr="ServiceShuruiCmb_2", text="就労移行")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_2_1", text="追加")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_2_2", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_2_3", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_2_4", text="")
        #self.form_input_by_id(idstr="ChkGensoku1_2_1", value="")
        
        # 26 障害福祉サービス決定管理画面: 暫定支給期間開始日「20230601」入力、暫定支給期間終了日「20230630」入力
        self.form_input_by_id(idstr="TxtZanteiShikyuStartYMD", value="20230601")
        self.form_input_by_id(idstr="TxtZanteiShikyuEndYMD", value="20230630")
        
        # 27 障害福祉サービス決定管理画面: 「負担額」ボタン押下
        self.click_button_by_label("負担額")
        
        # 28 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_28")
        
        # 29 障害福祉サービス決定管理画面: 「世帯範囲情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("世帯範囲情報")
        
        # 30 世帯範囲情報画面: 表示
        
        # 31 世帯範囲情報画面: 世帯員入日「20230401」入力
        self.form_input_by_id(idstr="TxtSetaiStartYMD1", value="20230401")
        self.screen_shot("世帯範囲情報画面_31")
        
        # 32 世帯範囲情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())

        # 33 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_33")
        
        # 34 障害福祉サービス決定管理画面: 「収入・資産入力」ボタン押下
        self.click_button_by_label("収入・資産入力")
        
        # 35 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_35")
        
        # 36 減免申請・収入資産登録画面: 「確定」ボタン押下
        self.click_button_by_label("確定")
        
        # 37 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_37")
        
        # 38 減免申請・収入資産登録画面: 「取込」ボタン押下
        self.click_button_by_label("取込")
        self.form_input_by_id(idstr="KazeiHikazeiRd_1", value="1")
        
        # 39 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_39")
        
        # 40 減免申請・収入資産登録画面: 「計算ボタン」ボタン押下
        self.click_button_by_label("計算ボタン")
        
        # 41 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_41")
        
        # 42 減免申請・収入資産登録画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 43 障害福祉サービス決定管理画面: 表示
        self.screen_shot("障害福祉サービス決定管理画面_43")
        
        # 44 障害福祉サービス決定管理画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        
        # 45 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_45")
        
        # 46 障害福祉サービス申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 47 障害福祉サービス申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害福祉サービス申請管理画面_47")
        
