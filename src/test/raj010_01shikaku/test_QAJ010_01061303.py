import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01061303(FukushiSiteTestCaseBase):
    """TestQAJ010_01061303"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01061303"]
        super().setUp()
    
    # 支給（給付）決定取消通知書を出力できることを確認する。
    def test_QAJ010_01061303(self):
        """取消通知書出力"""
        
        case_data = self.test_data["TestQAJ010_01061303"]
        atena_code = case_data.get("atena_code", "")
        date = datetime.date.today()
        thismonth = format(date, '%Y%m')
        today = format(date, '%Y%m%d')

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ010")
        # 2 障害福祉サービス申請管理画面: 「印刷」ボタン押下
        self.find_element(By.ID,"CmdInsatsu").click()
        
        # 3 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_3")
        
        # 4 帳票印刷画面: 「支給（給付）決定取消通知書」行の印刷チェックボックス選択「支給（給付）決定取消通知書」行の発行年月日チェックボックス選択発行年月日「カレンダーで入力当日の日付」選択
        # 5 帳票印刷画面: 「印刷」ボタン押下
        exec_params = [
            {"report_name":case_data.get("tyouhyoumei","") ,
             "params":[
                {"title": "交付日", "value":today},
                {"title": "文書番号", "value":"11111"},
                {"title": "枝番号", "value":"1"},
                {"title": "返還期限", "value":today}
                ]
            }
        ]
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_4")
        
        # 6 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: メッセージエリアに「プレビューを表示しました」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        
        # 7 帳票（PDF）: 表示
        self.screen_shot("帳票（PDF）_7")
     
        # 8 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 9 帳票印刷画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click()
        
        # 10 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_10")
        
