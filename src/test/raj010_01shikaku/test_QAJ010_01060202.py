import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060202(FukushiSiteTestCaseBase):
    """TestQAJ010_01060202"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060202"]
        super().setUp()
    
    # 支給申請書兼利用者負担額減額・免除等申請書を出力できることを確認する。18歳以上の対象者に対して申請種別「新規」、給付区分「訓練給付」の申請を登録できることを確認する。
    def test_QAJ010_01060202(self):
        """訓練等給付申請情報登録"""
        
        case_data = self.test_data["TestQAJ010_01060202"]
        atena_code = case_data.get("atena_code", "")
        insatsuChk = case_data.get("insatsuChk", "")
        hakkoChk = case_data.get("hakkoChk", "")
        hakkoTxt = case_data.get("hakkoTxt", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ010")
        # 2 障害福祉サービス申請管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")
        
        # 3 障害福祉サービス申請管理画面: 申請区分「新規」選択、申請理由「新規」選択、給付区分「訓練給付」選択、障害種別「身障」選択
        self.screen_shot("障害福祉サービス申請管理画面_3")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("shinseishubetu",""))
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("sinseiRiyuu",""))
        self.form_input_by_id(idstr=case_data.get("kyuhukubun",""), value="1")
        self.form_input_by_id(idstr=case_data.get("shougai",""), value="1")
        
        # 4 障害福祉サービス申請管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")
        
        # 5 障害福祉サービス申請管理画面: 申請日「20230401」入力、主たる障害種別「身障」選択
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        self.form_input_by_id(idstr="MainShougaiShubetsuCmb", text="身障")
        self.screen_shot("障害福祉サービス申請管理画面_5")
        
        # 6 障害福祉サービス申請管理画面: 受付場所「〇〇」選択
        self.form_input_by_id(idstr="TantoShokatsukuCmb",  value=case_data.get("kanribasho",""))
        #self.form_input_by_id(idstr="UketsukeBashoCmb", text=case_data.get("uketsukebasho",""))
        
        # 7 障害福祉サービス申請管理画面: 担当場所「〇〇」選択
        #self.form_input_by_id(idstr="TantoBashoCmb", text=case_data.get("tantobasho",""))
       
        # 8 障害福祉サービス申請管理画面: 計画作成者「事業者」選択
        self.form_input_by_id(idstr="KeikakuKubunCmb", text="事業者")
        
        # 9 障害福祉サービス申請管理画面: 医療的ケア判定スコア「999」入力
        self.form_input_by_id(idstr="TxtIryotekiCareHanteiScore", value="999")
        
        # 10 障害福祉サービス申請管理画面: 補足項目「補足項目入力テスト」入力
        self.form_input_by_id(idstr="TxtHosokuKoumoku", value="補足項目入力テスト")
        
        # 11 障害福祉サービス申請管理画面: 補足項目「▼」ボタン押下
        self.click_by_id("CmdHosokuKoumokuDown")
        
        # 12 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_12")
        
        # 13 障害福祉サービス申請管理画面: 届出区分「代理人」選択、対象者との関係「事業者」選択
        self.click_by_id("申請者情報表示部_CmdDown")
        self.form_input_by_id(idstr="申請者情報表示部_CmdTodokedeKbn", text="代理人")
        self.form_input_by_id(idstr="申請者情報表示部_CmdTaishoshaKankei", text="事業者")
        
        # 14 障害福祉サービス申請管理画面: 「事業所検索」ボタン押下
        self.click_button_by_label("サービス事業所検索")
        
        # 15 事業所検索画面: 表示
        self.screen_shot("事業所検索画面_15")
        
        # 16 事業所検索画面: 事業所番号入力
        self.form_input_by_id(idstr="TxtJigyo", value=case_data.get("gigyosya",""))
        
        # 17 事業所検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")
        
        # 18 事業所検索画面: 表示
        self.screen_shot("事業所検索画面_18")
        
        # 19 事業所検索画面: 事業所一覧 No.「１」ボタン押下
        self.click_button_by_label("1")
        
        # 20 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_20")
        
        # 21 障害福祉サービス申請管理画面: カナ氏名_氏「ｼﾝｾｲ」入力、カナ氏名_名「ﾃｽﾄ」入力、氏名_氏「申請氏」入力、氏名_名「申請名」入力、郵便番号「123」-「4567」入力、電話番号「123-456-7890」入力、住所「〇〇」選択、方書「申請者方書」入力
        #self.form_input_by_id(idstr="KanaShimei_Shi", text="ｼﾝｾｲ")
        #self.form_input_by_id(idstr="KanaShimei_Mei", text="ﾃｽﾄ")
        #self.form_input_by_id(idstr="KanjiShimei_Shi", text="申請氏")
        #self.form_input_by_id(idstr="KanjiShimei_Mei", text="申請名")
        #self.form_input_by_id(idstr="Address2", text="申請者方書")
        
        # 22 障害福祉サービス申請管理画面: 申請サービス　処理「追加」選択
        self.form_input_by_id(idstr="ServiceShuruiJokyoCmb_1", text="追加")
        
        # 23 障害福祉サービス申請管理画面: 申請サービス　サービス種類「共同生活援助」選択
        #self.form_input_by_id(idstr="ServiceShuruiCmb_1", text="共同生活援助")

        self.select_by_name("ServiceShuruiCmb_1", text="共同生活援助")

        
        # 24 障害福祉サービス申請管理画面: 申請サービス　サービス区分　共同生活援助基本　行の状態区分「追加」選択、サービス区分　共同生活援助基本　以外の行の状態区分「　　」（空白）選択、サービス区分　共同生活援助基本　行の当該月の日数にチェック

        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_1", text="追加")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_2", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_3", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_4", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_5", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_6", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_7", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_8", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_9", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_10", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_11", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_12", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_13", text="")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_14", text="")
        self.form_input_by_id(idstr="TxtShikyuryo1_1_1", value="30")
        self.screen_shot("障害福祉サービス申請管理画面_24")
        
        # 25 障害福祉サービス申請管理画面: 利用施設事業所番号「〇〇」入力
        self.form_input_by_id(idstr="TxtRiyoShisetasuJigyoshoBango", value=case_data.get("txtriyoshisetsu",""))
        
        # 26 障害福祉サービス申請管理画面: 「利用施設事業所番号」ボタン押下
        self.click_button_by_label("利用施設事業所番号")
        
        # 27 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_27")
        
        # 28 障害福祉サービス申請管理画面: 「福祉世帯情報」ボタン押下
        self.click_button_by_label("福祉世帯情報")
        
        # 29 福祉世帯情報画面: 表示
        
        # 30 福祉世帯情報画面: 該当日「20230401」入力本人から見た続柄「本人」入力受給者との関係「本人」入力
        #self.click_by_id("NoBtn1")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230401")
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人")
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20230401")
        self.form_input_by_id(idstr="HoninCmb_2", text="母")
        self.form_input_by_id(idstr="JukyuCmb_2", text="続柄なし")
        self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230401")
        self.form_input_by_id(idstr="HoninCmb_3", text="子")
        self.form_input_by_id(idstr="JukyuCmb_3", text="続柄なし")
        
        # 31 福祉世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        
        # 32 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_32")
        
        # 33 障害福祉サービス申請管理画面: 「住記情報」ボタン押下
        self.click_button_by_label("住記情報")
        
        # 34 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_34")
        
        # 35 世帯一覧画面: 世帯一覧「1」Noボタン押下
        self.click_button_by_label("1")
        
        # 36 住記情報画面: 表示
        self.screen_shot("住記情報画面_36")
        
        # 37 住記情報画面: 「戻る」ボタン押下
        self.return_click()
        
        # 38 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_38")
        
        # 39 世帯一覧画面: 「戻る」ボタン押下
        self.return_click()
        
        # 40 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_40")
        
        # 41 障害福祉サービス申請管理画面: 「所得情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("所得情報")
        
        # 42 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_42")
        
        # 43 住民税世帯情報画面: 対象者世帯一覧「1」ボタン押下
        self.click_button_by_label("1")
        
        # 44 住民税個人情報画面: 表示
        self.screen_shot("住民税個人情報画面_44")
        
        # 45 住民税個人情報画面: 「戻る」ボタン押下
        self.return_click()
        
        # 46 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_46")
        
        # 47 住民税世帯情報画面: 「戻る」ボタン押下
        self.return_click()
        
        # 48 障害福祉サービス申請管理画面: 「生活保護情報」ボタン押下
        self.click_button_by_label("生活保護情報")
        
        # 49 生活保護情報画面: 表示
        self.screen_shot("生活保護情報画面_49")

        # 50 世帯一覧画面: 「追加」ボタン押下
        self.click_button_by_label("初期表示")
        self.click_button_by_label("追加")
        
        # 51 生活保護情報画面: ケース番号「12345」入力 マスタ区分「入力」選択   生保開始日「20230701」入力、生保喪失日「（空白）」
        self.form_input_by_id(idstr="TbxCase", value="12345")
        self.form_input_by_id(idstr="CmbMstKbn", text="入力")
        self.form_input_by_id(idstr="TbxDayBegin", value="20230701")
        self.form_input_by_id(idstr="TbxDayEnd", value="")
        
        # 52 生活保護情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 53 生活保護情報画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("生活保護情報画面_53")
        
        # 54 生活保護情報画面: 「戻る」ボタン押下
        self.return_click()
        
        # 55 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_55")
        
        # 56 障害福祉サービス申請管理画面: 「手帳情報」ボタン押下
        self.click_button_by_label("手帳情報")
        
        # 57 手帳情報画面: 表示
        self.screen_shot("手帳情報画面_57")
        
        # 58 手帳情報画面: 「戻る」ボタン押下
        self.return_click()
        
        # 59 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_59")
        
        # 60 障害福祉サービス申請管理画面: 「住所管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("住所管理")
        
        # 61 住所管理画面: 表示
        self.screen_shot("住所管理画面_61")
        
        # 62 住所管理画面: 「送付先」ボタン押下
        #self.click_button_by_label("送付先")
        
        # 63 住所管理画面: 「追加」ボタン押下
        self.click_button_by_label("追加")
        
        # 64 住所管理画面: 「住記情報索引」ボタン押下
        self.click_button_by_label("住記情報索引")
        time.sleep(3)
        self.assertEqual(u"現在表示されている項目が上書きされます。よろしいですか？", self.alert_ok())
        self.screen_shot("住所管理画面_64")
        self.form_input_by_id(idstr="TxtKanaShimei_Uji", value="シンセイ")
        self.form_input_by_id(idstr="TxtKanaShimei_Na", value="テスト")
        self.form_input_by_id(idstr="TxtShimei_Uji", value="申請氏")
        self.form_input_by_id(idstr="TxtShimei_Na", value="申請名")
        self.screen_shot("住所管理画面_64")
        
        # 65 住所管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())

        
        # 66 住所管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        #self.assert_message_area("登録しました ")
        self.screen_shot("住所管理画面_66")
        
        # 67 住所管理画面: 「戻る」ボタン押下
        self.return_click()
        
        # 68 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_68")
        
        # 69 障害福祉サービス申請管理画面: 「連絡先管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("連絡先管理")
        
        # 70 連絡先管理画面: 表示
        self.screen_shot("連絡先管理画面_70")
        
        # 71 連絡先管理画面: 本人連絡先の「追加」ボタン押下
        # self.click_button_by_label("追加")
        self.click_by_id("BtnTsuika_Honnin")
        
        # 72 連絡先管理画面: 業務区分　？？、電話番号公開範囲　？？、優先順位「携帯電話番号」選択、公開/非公開「公開」チェック、自宅電話番号 「111-1111-1111」入力、留守電「チェック」入力、携帯電話番号 「222-2222-2222」入力、FAX番号「333-3333-3333」入力、勤務先名「勤務先入力テスト」入力、勤務先電話番号「444-4444-4444」入力、勤務先内線番号「123」入力、勤務先FAX番号「555-5555-5555」入力   メールアドレス1「<EMAIL>」入力  メールアドレス2「<EMAIL>」備考「備考テスト入力１２３４５６７８９０」入力  種別「その他」選択、連絡先「種別連絡先入力テスト１２３４５６７８９０」入力  備考「種別備入力テスト１２３４５６７８９０」入力

        self.form_input_by_id(idstr="CmbYusenTEL", text="携帯電話番号")
        self.form_input_by_id(idstr="TxtTelJitaku", value="111-1111-1111")
        self.form_input_by_id(idstr="ChkRusuden", value="1")
        self.form_input_by_id(idstr="TxtTelKeitai", value="222-2222-2222")
        self.form_input_by_id(idstr="TxtFaxJitaku", value="333-3333-3333")
        self.form_input_by_id(idstr="TxtKinmuSaki", value="勤務先入力テスト")
        self.form_input_by_id(idstr="TxtTelKinmu", value="444-4444-4444")
        self.form_input_by_id(idstr="TxtFaxKinmu", value="555-5555-5555")
        self.form_input_by_id(idstr="TxtMail", value="<EMAIL>")
        self.form_input_by_id(idstr="TxtMail2", value="<EMAIL>")
        self.form_input_by_id(idstr="TxtBikou", value="備考テスト入力１２３４５６７８９０")
        self.form_input_by_id(idstr="SelectHanyo1", text="その他")
        self.form_input_by_id(idstr="TxtHanyoRenraku1", value="種別連絡先入力テスト１２３４５６７８９０")
        self.form_input_by_id(idstr="TxtHanyoBiko1", value="種別備入力テスト１２３４５６７８９０")
        self.screen_shot("連絡先管理画面_72")
        self.screen_shot("連絡先管理画面_72")
        
        # 73 連絡先管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 74 連絡先管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("連絡先管理画面_74")
        
        # 75 連絡先管理画面: 緊急連絡先入力の「追加」ボタン押下
        # self.click_button_by_label("追加")
        self.click_by_id("BtnTsuika_Kinkyu")
        
        # 76 個人検索画面: 「個人検索」ボタン押下
        self.click_button_by_label("個人検索")
        
        # 77 個人検索画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=case_data.get("atena_code",""))
        
        # 78 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        
        # 79 連絡先管理画面: 続柄「その他」選択自宅電話番号 「111-1111-1111」入力、留守電「チェック」入力、携帯電話番号 「222-2222-2222」入力、FAX番号「333-3333-3333」入力、勤務先「勤務先入力テスト１２３４５６７８９０」入力、勤務先電話番号「444-4444-4444」入力
        self.form_input_by_id(idstr="CmbTsudukigara", text="その他")
        self.form_input_by_id(idstr="TxtTelJitaku_Kinkyu", value="111-1111-1111")
        self.form_input_by_id(idstr="ChkRusuden_Kinkyu", value="1")
        self.form_input_by_id(idstr="TxtTelKeitai_Kinkyu", value="222-2222-2222")
        self.form_input_by_id(idstr="TxtFaxJitaku_Kinkyu", value="333-3333-3333")
        self.form_input_by_id(idstr="TxtKinmuSaki_Kinkyu", value="勤務先入力テスト１２３４５６７８９０")
        self.form_input_by_id(idstr="TxtTelKinmu_Kinkyu", value="444-4444-4444")
        
        # 80 連絡先管理画面: 「登録」ボタン押下
        # self.click_button_by_label("登録")
        self.find_element(By.ID,"BtnTouroku_Kinkyu").click()
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 81 連絡先管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("連絡先管理画面_81")
        
        # 82 連絡先管理画面: 「戻る」ボタン押下
        self.return_click()
        
        # 83 障害福祉サービス申請管理画面: 「メモ情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("メモ情報")
        
        # 84 メモ情報画面: 表示
        self.screen_shot("メモ情報画面_84")
        
        # 85 メモ情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")
        
        # 86 メモ情報画面: 内容「メモ入力テスト１２３４５６７８９０」
        self.form_input_by_id(idstr="TxtNaiyo", value="メモ入力テスト１２３４５６７８９０")
       
        # 87 メモ情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 88 メモ情報画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("メモ情報画面_88")
        
        # 89 メモ情報画面: 「戻る」ボタン押下
        self.return_click()
        
        # 90 障害福祉サービス申請管理画面: 「介護保険情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("介護保険情報")
        
        # 91 介護保険情報画面: 表示
        self.screen_shot("介護保険情報画面_91")
        
        # 92 介護保険情報画面: 「戻る」ボタン押下
        self.return_click()
        
        # 93 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_93")
        
        # 94 障害福祉サービス申請管理画面: 「受給者台帳」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("受給者台帳")
        
        # 95 受給者台帳画面: 表示
        self.screen_shot("受給者台帳画面_95")
        
        # 96 受給者台帳画面: 「戻る」ボタン押下
        self.return_click()
        
        # 97 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_97")
        
        # 98 障害福祉サービス申請管理画面: 「保険情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("保険情報")
        
        # 99 保険情報画面: 表示
        self.screen_shot("保険情報画面_99")
        
        # 100 保険情報画面: 「戻る」ボタン押下
        self.return_click()
        
        # 101 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_101")
        
        # 102 障害福祉サービス申請管理画面: 「届出保険情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("届出保険情報")
        
        # 103 届出保険情報画面: 表示
        self.screen_shot("届出保険情報画面_103")
        
        # 104 届出保険情報画面: 「戻る」ボタン押下
        self.return_click()
        
        # 105 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_105")
        
        # 106 障害福祉サービス申請管理画面: 「サービス詳細修正」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("サービス詳細修正")
        
        # 107 サービス詳細修正画面: 表示
        self.screen_shot("サービス詳細修正画面_107")
        
        # 108 サービス詳細修正画面: 「戻る」ボタン押下
        self.return_click()
        
        # 109 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_109")
        
        # 110 障害福祉サービス申請管理画面: 「給付台帳」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("給付台帳")
        
        # 111 給付台帳画面: 表示
        self.screen_shot("給付台帳画面_111")
        
        # 112 給付台帳画面: サービス提供年月「〇〇」入力
        self.form_input_by_id(idstr="TxtSTeikyoYM", value=case_data.get("teikyouYM",""))
        
        # 113 給付台帳画面: 「検索」ボタン押下
        self.click_button_by_label("検索")
        
        # 114 給付台帳画面: 表示
        self.screen_shot("給付台帳画面_114")
        
        # 115 給付台帳画面: 「戻る」ボタン押下
        self.return_click()
        
        # 116 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_116")
        
        # 117 障害福祉サービス申請管理画面: 「病名管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("病名管理")
        
        # 118 病名管理画面: 表示
        self.screen_shot("病名管理画面_118")
        
        # 119 病名管理画面: 「追加」ボタン押下
        self.click_button_by_label("追加")
        
        # 120 病名管理画面: 表示
        self.screen_shot("病名管理画面_120")
        
        # 121 病名管理画面: 「病名検索」ボタン押下
        self.click_button_by_label("病名検索")
        
        # 122 病名検索画面: 表示
        self.screen_shot("病名検索画面_122")
        
        # 123 病名検索画面: 病名No.「１」ボタン押下
        self.click_button_by_label("1")
        
        # 124 病名管理画面: 表示
        self.form_input_by_id(idstr="CheckDaihyou1", value="1")
        self.screen_shot("病名管理画面_124")
        
        # 125 病名管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 126 病名管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("病名管理画面_126")
        
        # 127 病名管理画面: 「戻る」ボタン押下
        self.return_click()
        
        # 128 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_128")
        
        # 129 障害福祉サービス申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 130 障害福祉サービス申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害福祉サービス申請管理画面_130")
        
