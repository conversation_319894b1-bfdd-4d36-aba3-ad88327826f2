import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060802(FukushiSiteTestCaseBase):
    """TestQAJ010_01060802"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060802"]
        super().setUp()
    
    # 【パターン：障害者－支給量変更】障害福祉サービスの支給決定登録が完了している対象者に対して、支給量変更申請を登録できることを確認する。
    def test_QAJ010_01060802(self):
        """支給量変更申請登録"""
        
        case_data = self.test_data["TestQAJ010_01060802"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ010")
        # 2 障害福祉サービス申請管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")
        
        # 3 障害福祉サービス申請管理画面: 申請区分「支給量変更」選択
        time.sleep(1)
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="支給量変更")
        self.screen_shot("障害福祉サービス申請管理画面_3")
        
        # 4 障害福祉サービス申請管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")
        
        # 5 障害福祉サービス申請管理画面: 申請日「20230601」入力
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230601")
        self.screen_shot("障害福祉サービス申請管理画面_5")
        
        # 6 障害福祉サービス申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())

        # 7 障害福祉サービス申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害福祉サービス申請管理画面_7")
        
