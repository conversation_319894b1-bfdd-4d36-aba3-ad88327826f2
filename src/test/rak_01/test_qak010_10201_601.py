import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK01010201601(FukushiSiteTestCaseBase):
    """TESTQAK01010201601"""

    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("test_qak010_10201_601.sql", params=atena_list)
        super().setUp()

    def test_case_qak010_10201_601(self):
        """test_case_qak010_10201_601"""
        driver = None
        case_data = self.test_data[self.__class__.__name__]
        atenaCD = case_data.get("宛名番号", "")

        # ログイン
        self.do_login()

        # 後期高齢者医療　資格
        # →「住基送付者登録」ボタン押下
        self.click_button_by_label("住基送付者登録")

        # 「宛名番号」テキストボックス入力
        self.form_input_by_id(idstr="AtenaCD", value=atenaCD)

        # 「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("10201-601-06")

        # 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 「住所地特例者」チェックボックスをチェック
        self.click_by_id("ChkTokurei")

         # 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        self.assert_message_area("登録しました。")
        self.screen_shot("10201-601-10")

        # 「戻る」ボタン押下
        self.return_click()

        # 「戻る」ボタン押下
        self.return_click()