DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

DELETE WR$$JICHITAI_CODE$$QA..QAJサービス事業者マスタ            WHERE サービス事業者コード = '0010000003'
DELETE WR$$JICHITAI_CODE$$QA..QAJサービス事業者マスタ            WHERE サービス事業者コード = '0010000004'
DELETE WR$$JICHITAI_CODE$$QA..QAJサービス事業者提供マスタ            WHERE サービス事業者コード = '0010000003'
DELETE WR$$JICHITAI_CODE$$QA..QAJサービス事業者提供マスタ            WHERE サービス事業者コード = '0010000004'


IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END