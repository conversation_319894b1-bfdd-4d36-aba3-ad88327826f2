DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

DELETE WR$$JICHITAI_CODE$$QA..QAJ高額_障害児施設宛名情報            　　WHERE 宛名コード = '$$QAJ503_ATENA_CODE_3$$'　and 業務コード='QAJ040'
DELETE WR$$JICHITAI_CODE$$QA..QAJ放課後等デイサービス基本報酬区分指標管理　　WHERE 宛名コード = '$$QAJ503_ATENA_CODE_3$$'　and 業務コード='QAJ040'
DELETE WR$$JICHITAI_CODE$$QA..QAJ医療的ケア判定スコア情報  　　　　　　  WHERE 宛名コード = '$$QAJ503_ATENA_CODE_3$$'　and 業務コード='QAJ040'
DELETE WR$$JICHITAI_CODE$$QA..QAJ国保連過誤申立書情報                    WHERE 宛名コード = '$$QAJ503_ATENA_CODE_1$$'　and 業務コード='QAJ040'
UPDATE WR$$JICHITAI_CODE$$QA..QAJ国保連過誤申立書情報                    SET 過誤決定年月日 = '00000000' WHERE 受給者証番号 = '$$JukyushaNo_1$$'　and 業務コード='QAJ040'
UPDATE WR$$JICHITAI_CODE$$QA..QAJ国保連過誤申立書情報                    SET 過誤決定年月日 = '00000000' WHERE 受給者証番号 = '$$JukyushaNo_2$$'　and 業務コード='QAJ040'
UPDATE WR$$JICHITAI_CODE$$QA..QAJ支給実績基本                            SET 過誤申立書情報作成有無 = 0,点検結果 = '1',受付年月 = '$$uketukeYM$$' WHERE 宛名コード = '$$QAJ503_ATENA_CODE_1$$'　and 業務コード='QAJ040'
UPDATE WR$$JICHITAI_CODE$$QA..QAJ支給実績基本                            SET 過誤申立書情報作成有無 = 0 WHERE 宛名コード = '$$QAJ503_ATENA_CODE_2$$'　and 業務コード='QAJ040'
DELETE WR$$JICHITAI_CODE$$QA..QAJ国保連審査結果一覧情報                 WHERE  受給者証番号 = '$$JukyushaNo_1$$'　and 業務コード='QAJ040'
DELETE WR$$JICHITAI_CODE$$QA..QAJ国保連契約情報                      WHERE 宛名コード = '$$QAJ503_ATENA_CODE_3$$' AND 業務コード = 'QAJ040'

IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END