use WR$$JICHITAI_CODE$$QA

insert into QKS資格履歴 ([業務コード],[履歴番号],[履歴分類],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[申請年月日],[申請種別],[申請理由],[申請内容入力日],[進達年月日1],[進達判定年月日1],[進達結果1],[進達内容入力日1],[進達年月日2],[進達判定年月日2],[進達結果2],[進達内容入力日2],[決定年月日],[決定結果],[決定理由],[決定内容入力日],[業務固有コード1],[業務固有コード2],[業務固有コード3],[職権フラグ],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム]) values ('QKS100',599,5,'99101','00000','','000000000000705','20130923',1,0,'00000000','00000000','00000000',0,'00000000','00000000','00000000',0,'00000000','20130923',2,0,'00000000','0000000001','','','0','0','9501','9501',convert(DateTime,'2013-09-23 16:53:28.340',21),convert(DateTime,'2013-10-02 11:24:05.023',21),'QKSF004 ');

update QKS違約金整理基本
set 削除フラグ = '1'
from QKS違約金整理基本
where 業務コード = 'QKS100'
	AND 貸付番号 = '1000000351'
	AND 削除フラグ = '0';

update QKS償還計画詳細 set 削除フラグ = '0' where 貸付番号 = '3000000012' and 削除フラグ = '1';

update QKS償還計画詳細 set 違約金減免年月日 = '00000000' where 貸付番号 = '3000000012';

