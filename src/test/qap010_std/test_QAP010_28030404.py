from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28030404(KodomoSiteTestCaseBase):
    """TestQAP010_28030404"""

    def setUp(self):
        super().setUp()

    # 利用者負担額（切替後）決定に関わる各種帳票の出力ができることを確認する。
    def test_QAP010_28030404(self):
        """各種帳票作成"""
        case_data_220_QAZF100001 = self.test_data["220_QAZF100001"]
        case_data_222_QAZF100002 = self.test_data["222_QAZF100002"]
        case_data_223_QAPF100300 = self.test_data["223_QAPF100300"]
        case_data_227_QAPF107800 = self.test_data["227_QAPF107800"]
        case_data_236_ZEAF000400 = self.test_data["236_ZEAF000400"]
        case_data_238_ZEAF000400 = self.test_data["238_ZEAF000400"]
        case_data_239_ZEAF002200 = self.test_data["239_ZEAF002200"]
        case_data_251_QAZF100001 = self.test_data["251_QAZF100001"]
        case_data_253_QAZF100002 = self.test_data["253_QAZF100002"]
        case_data_254_QAPF100300 = self.test_data["254_QAPF100300"]
        case_data_257_QAPF106500 = self.test_data["257_QAPF106500"]
        case_data_259_QAPF900500 = self.test_data["259_QAPF900500"]
        case_data_269_ZEAF000400 = self.test_data["269_ZEAF000400"]
        case_data_271_ZEAF000400 = self.test_data["271_ZEAF000400"]
        case_data_272_ZEAF002200 = self.test_data["272_ZEAF002200"]
        tab_index = 0
        # 216 メインメニュー画面:「子ども子育て表示」
        self.do_login_new_tab()

        # 217 メインメニュー画面: メインメニューから「子ども子育て支援」クリック
        # 218 メインメニュー画面: 「世帯情報」クリック
        # 219 メインメニュー画面: 「検索」ダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3="検索",
                                 is_new_tab=True)
        tab_index += 1

        # 220 検索条件入力画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
                value=case_data_220_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
                text=case_data_220_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
                value=case_data_220_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
                text=case_data_220_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
                value=case_data_220_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
                value=case_data_220_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
                text=case_data_220_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
                text=case_data_220_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
                value=case_data_220_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
                value=case_data_220_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
                value=case_data_220_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
                value=case_data_220_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
                value=case_data_220_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
                text=case_data_220_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
                value=case_data_220_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
                value=case_data_220_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("検索条件入力画面_220")

        # 221 検索条件入力画面: 「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        # 222 世帯履歴画面: 対象の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_222_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code = atena_code, tab_index = tab_index)

        self.click_button_by_label("1")
        self.screen_shot("世帯台帳画面_222")

        # 223 世帯台帳画面: 対象児童の「No.」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_223_QAPF100300.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)
        self.screen_shot("児童台帳画面_223")

        # 224 児童台帳画面:「賦課」ボタン押下
        self.click_button_by_label("賦課")
        self.screen_shot("児童賦課情報画面_224")

        # 225 児童賦課情報画面:「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 226 印刷指示画面:「保育料(利用料)変更通知書」のチェックボックスにチェックを入れる
        # 227 印刷指示画面:「発行年月日」「処理対象年月」「印刷区分」「変更理由」を入力します
        exec_params = [
            {
                "report_name": case_data_227_QAPF107800.get("chouhyou_mei", ""),
                "params_1": [
                    {"title": "発行場所", "value": case_data_227_QAPF107800.get("hakkou_basho", "")},
                    {"title": "トレイ", "value": case_data_227_QAPF107800.get("torei", "")},
                    {"title": "部数", "value": case_data_227_QAPF107800.get("busuu", "")}
                ],
                "params_2": [
                    {"title": "発行年月日", "value": case_data_227_QAPF107800.get("hakko_nengetsu_bi", "")},
                    {"title": "処理対象年月", "value": case_data_227_QAPF107800.get("shori_taishou_nengetsu", "")},
                    {"title": "印刷区分", "value": case_data_227_QAPF107800.get("insatsu_kubun", "")},
                    {"title": "変更理由", "value": case_data_227_QAPF107800.get("henkou_riyuu", "")}
                ]
            },
        ]
        checked_reports_count = self.select_kodomo_report(report_param_list=exec_params, tab_index=tab_index,
                                                          screen_shot_name="印刷指示画面画面_227",
                                                          screen_id="QAPF107800")

        # 228 印刷指示画面画面:「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF107800_printBtn_button")

        # 229 印刷指示画面画面:「印刷してよろしいですか」に「はい」ボタン押下
        self.alert_accept()

        # 230 ファイルダウンロード画面: 出力対象で選択した帳票の「No1」ボタン押下
        # 231 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        self.print_kodomo_reports(limit_wait_count=20, time_span_sec=4, tab_index=tab_index,
                                  screen_shot_name="ファイルダウンロード画面_231",
                                  count_report=checked_reports_count)

        # 232 印刷指示画面画面:「閉じる」ボタン押下
        self.click_button_by_label("閉じる")

        # 233 メインメニュー画面: メインメニューから「バッチ管理」クリック
        # 234 メインメニュー画面:「即時実行」クリック
        # 235 メインメニュー画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=False)
        tab_index += 1

        # 236 スケジュール個別追加画面: 業務名: <子ども子育て支援>: サブシステム名：<賦課>: 処理名：<月次賦課処理>
        # 237 スケジュール個別追加画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_236_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_236_ZEAF000400.get("sabu_shisutemu_mei", ""),
                                        shoriNM=case_data_236_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加画面_237")

        # 238 スケジュール個別追加画面: 「保育料変更者一覧・保育料変更通知書作成処理」の「No」ボタン押下：
        self.click_batch_job_button_by_label(job_label=case_data_238_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 239 実行指示画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_239_ZEAF002200.get("shokan_ku", "")},
            {"title": "支所", "type": "select",
             "value": case_data_239_ZEAF002200.get("shisho", "")},
            {"title": "発行年月日", "type": "text",
             "value": case_data_239_ZEAF002200.get("hakko_nengetsu_bi", "")},
            {"title": "対象年月", "type": "text",
             "value": case_data_239_ZEAF002200.get("taishou_nengetsu", "")},
            {"title": "施設コード", "type": "text",
             "value": case_data_239_ZEAF002200.get("shisetsu_code", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_239_ZEAF002200.get("yuubin_kuunai_tokubetsu_umu", "")},
            {"title": "並び順", "type": "select", "value": case_data_239_ZEAF002200.get("narabi_jun", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示画面_239")

        # 240 実行指示画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 241 実行管理画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理画面_241")

        # 242 実行管理画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 243 結果確認画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理画面_243")

        # 244 納品物管理画面: 納品物の「ダウンロード」ボタン押下
        # 245 ファイルダウンロード画面:「No1」ボタン押下
        # 246 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        # 247 納品物管理画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 248 メインメニュー画面: メインメニューから「子ども子育て支援」クリック
        # 249 メインメニュー画面: 「世帯情報」クリック
        # 250 メインメニュー画面: 「検索」ダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3="検索",
                                 is_new_tab=False)
        tab_index += 1

        # 251 検索条件入力画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
                value=case_data_251_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
                text=case_data_251_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
                value=case_data_251_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
                text=case_data_251_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
                value=case_data_251_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
                value=case_data_251_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
                text=case_data_251_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
                text=case_data_251_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
                value=case_data_251_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
                value=case_data_251_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
                value=case_data_251_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
                value=case_data_251_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
                value=case_data_251_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
                text=case_data_251_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
                value=case_data_251_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
                value=case_data_251_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("検索条件入力画面_251")

        # 252 検索条件入力画面: 「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        # 253 世帯履歴画面: 対象の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_253_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code = atena_code, tab_index = tab_index)

        self.click_button_by_label("1")
        self.screen_shot("世帯台帳画面_253")

        # 254 世帯台帳画面: 対象児童の「No.」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_254_QAPF100300.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)

        # 255 児童台帳画面:「入所管理」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103500", label="入所管理")
        self.screen_shot("児童台帳画面_255")

        # 256 児童台帳画面: 「減免・調整申請」ボタン押下
        self.click_button_by_label("減免・調整申請")

        # 257 保育料減免・調整管理（階層その他）画面: 印刷対象の申請情報履歴を選択
        self.click_button_by_label(case_data_257_QAPF106500.get("shinsei_jouhou_rireki_no", ""))
        self.screen_shot("保育料減免・調整管理（階層その他）画面_257")

        # 258 保育料減免・調整管理（階層その他）:「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 259 印刷指示画面:「保育料(利用料)変更通知書」のチェックボックスにチェックを入れる
        # 260 印刷指示画面:「発行年月日」「処理対象年月」「印刷区分」「変更理由」を入力します
        exec_params = [
            {
                "report_name": case_data_259_QAPF900500.get("chouhyou_mei", ""),
                "params_1": [
                    {"title": "発行場所", "value": case_data_259_QAPF900500.get("hakkou_basho", "")},
                    {"title": "トレイ", "value": case_data_259_QAPF900500.get("torei", "")},
                    {"title": "部数", "value": case_data_259_QAPF900500.get("busuu", "")}
                ],
                "params_2": [
                    {"title": "発行年月日", "value": case_data_259_QAPF900500.get("hakko_nengetsu_bi", "")}
                ]
            },
        ]
        checked_reports_count = self.select_kodomo_report(report_param_list=exec_params, tab_index=tab_index,
                                                          screen_shot_name="印刷指示画面画面_260",
                                                          screen_id="QAPF900500")

        # 261 印刷指示画面画面:「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF900500_printBtn_button")

        # 262 印刷指示画面画面:「印刷してよろしいですか」に「はい」ボタン押下
        self.alert_accept()
        self.screen_shot("ファイルダウンロード画面_262")

        # 263 ファイルダウンロード画面: 出力対象で選択した帳票の「No1」ボタン押下
        # 264 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        self.print_kodomo_reports(limit_wait_count=20, time_span_sec=4, tab_index=tab_index,
                                  screen_shot_name="ファイルダウンロード画面_264",
                                  count_report=checked_reports_count)

        # 265 印刷指示画面画面:「閉じる」ボタン押下
        self.click_button_by_label("閉じる")

        # 266 メインメニュー画面: メインメニューから「バッチ管理」クリック
        # 267 メインメニュー画面:「即時実行」クリック
        # 268 メインメニュー画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=False)
        tab_index += 1

        # 269 スケジュール個別追加画面: 業務名: <子ども子育て支援>: サブシステム名：<入所>: 処理名：<保育所徴収金減免決定(却下)通知書出力処理>
        # 270 スケジュール個別追加画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_269_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_269_ZEAF000400.get("sabu_shisutemu_mei", ""),
                                        shoriNM=case_data_269_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加画面_270")

        # 271 スケジュール個別追加画面: 「保育所徴収金減免決定（却下）通知書出力」の「No」ボタン押下：
        self.click_batch_job_button_by_label(job_label=case_data_271_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 272 実行指示画面: パラメータを入力
        params = [
            {"title": "対象年月", "type": "text", "value": case_data_272_ZEAF002200.get("taishou_nengetsu", "")},
            {"title": "出力区分", "type": "select",
             "value": case_data_272_ZEAF002200.get("shutsuryoku_kubun", "")},
            {"title": "再発行区分", "type": "select",
             "value": case_data_272_ZEAF002200.get("saihakko_kubun", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_272_ZEAF002200.get("yuubin_kuunai_tokubetsu_umu", "")},
            {"title": "発行年月日", "type": "text",
             "value": case_data_272_ZEAF002200.get("hakko_nengetsu_bi", "")},
            {"title": "並び順", "type": "select", "value": case_data_272_ZEAF002200.get("narabi_jun", "")},
            {"title": "所管区", "type": "select", "value": case_data_272_ZEAF002200.get("shokan_ku", "")},
            {"title": "施設種類", "type": "select", "value": case_data_272_ZEAF002200.get("shisetsu_shurui", "")},
            {"title": "入所形態", "type": "select", "value": case_data_272_ZEAF002200.get("nyusho_keitai", "")},
            {"title": "実施区分", "type": "select", "value": case_data_272_ZEAF002200.get("jisshi_kubun", "")},
            {"title": "公私区分", "type": "select", "value": case_data_272_ZEAF002200.get("koushi_kubun", "")},
            {"title": "事業所番号", "type": "text", "value": case_data_272_ZEAF002200.get("jigyousho_bangou", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示画面_272")

        # 273 実行指示画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 274 実行管理画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理画面_274")

        # 275 実行管理画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 276 結果確認画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理画面_276")

        # 277 納品物管理画面: 納品物の「ダウンロード」ボタン押下
        # 278 ファイルダウンロード画面:「No1」ボタン押下
        # 279 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        # 280 納品物管理画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
