from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28010102(KodomoSiteTestCaseBase):
    """TestQAP010_28010102"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28010102(self):
        tab_index = 0
        case_data_143_QAZF100001 = self.test_data["143_QAZF100001"]
        case_data_145_QAZF100002 = self.test_data["145_QAZF100002"]
        case_data_147_QAPF100300 = self.test_data["147_QAPF100300"]
        case_data_153_QAPF103600 = self.test_data["153_QAPF103600"]
        case_data_160_ZEAF000400 = self.test_data["160_ZEAF000400"]
        case_data_162_ZEAF000400 = self.test_data["162_ZEAF000400"]
        case_data_163_ZEAF002200 = self.test_data["163_ZEAF002200"]
        case_data_173_ZEAF000400 = self.test_data["173_ZEAF000400"]
        case_data_179_QAZF100001 = self.test_data["179_QAZF100001"]
        case_data_181_QAZF100002 = self.test_data["181_QAZF100002"]
        case_data_183_QAPF100300 = self.test_data["183_QAPF100300"]
        case_data_188_QAPF900100 = self.test_data["188_QAPF900100"]
        case_data_198_ZEAF000400 = self.test_data["198_ZEAF000400"]
        case_data_200_ZEAF000400 = self.test_data["200_ZEAF000400"]
        case_data_201_ZEAF002200 = self.test_data["201_ZEAF002200"]
        case_data_211_ZEAF000400 = self.test_data["211_ZEAF000400"]
        case_data_212_ZEAF002200 = self.test_data["212_ZEAF002200"]

        # 139 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 140 メインメニュー 画面: メインメニューから「子ども子育て支援」をクリック
        # 141 メインメニュー 画面:「世帯情報」をクリック
        # 142 メインメニュー 画面:「検索」をダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3="検索",
                                 is_new_tab=True)
        tab_index += 1

        # 143 検索条件入力 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
            value=case_data_143_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
            text=case_data_143_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
            value=case_data_143_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
            text=case_data_143_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
            value=case_data_143_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
            value=case_data_143_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
            text=case_data_143_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
            text=case_data_143_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
            value=case_data_143_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
            value=case_data_143_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
            value=case_data_143_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
            value=case_data_143_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
            value=case_data_143_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
            text=case_data_143_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
            value=case_data_143_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
            value=case_data_143_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("検索条件入力 画面_143")

        # 144 検索条件入力 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        # 145 世帯履歴 画面: 支給認定情報延期登録する世帯台帳履歴の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            self.select_atena_code_after_relative_search(atena_code=case_data_145_QAZF100002.get("atena_code", ""),
                                                         tab_index=tab_index,
                                                         col_number=1)
        self.click_button_by_label("1")
        self.screen_shot("世帯台帳 画面_145")

        # 146 世帯台帳 画面:「児童一覧」タブをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF100300_jidoiciran_li")

        # 147 世帯台帳 画面: 支給認定情報延期登録する児童の「№」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_147_QAPF100300.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)
        self.screen_shot("児童台帳 画面_147")

        # 148 児童台帳 画面:「支給認定登録・履歴」タブをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103500_ShikyuNinteiTorokuRireki_li")

        # 149 児童台帳 画面: 支給認定情報延期登録する「№」ボタン押下
        self.click_button_by_label("1")
        self.screen_shot("支給認定情報 画面_149")

        # 150 支給認定情報 画面:「修正」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103600_btnEditChg_button")

        # 151 支給認定情報 画面:「処分延期」タブをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103600_syobunenkijoho_li")

        # 152 支給認定情報 画面:「行追加」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103600_btnShobunEnkiAdd_button")
        self.screen_shot("支給認定情報_処分延期 画面_152")

        # 153 支給認定情報 画面: 延期に関する情報を入力
        index_new_shobun_enki_jouhou = len(
            self.find_elements_by_css_selector("#tab0" + str(tab_index) + "_ZZZ000000_tblQAPF1036005 > tbody > tr")) - 1
        # 処分見込み期間
        self.form_input_by_id(
            idstr="tab0" + str(
                tab_index) + "_ZZZ000000_txtShobunenKiINFOShobunMikomiKikan_" + str(
                index_new_shobun_enki_jouhou) + "_3_textboxInput",
            value=case_data_153_QAPF103600.get("shobun_mikomi_kikan", ""))
        # 延期の理由
        self.form_input_by_id(
            idstr="tab0" + str(
                tab_index) + "_ZZZ000000_selShobunenKiINFOenKiRiyu_" + str(index_new_shobun_enki_jouhou) + "_6_select",
            text=case_data_153_QAPF103600.get("enki_no_riyuu", ""))
        self.screen_shot("支給認定情報_処分延期 画面_153")

        # 154 支給認定情報 画面:「入力チェック」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103600_checkbtn_button")
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF103600_msg_span",
                                 msg="入力チェックが完了しました。")

        # 155 支給認定情報 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103600_regbtn_button")

        # 156 支給認定情報 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF103600_msg_span", msg="更新しました。")
        self.screen_shot("支給認定情報 画面_156")

        # 157 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 158 メインメニュー 画面:「即時実行」クリック
        # 159 メインメニュー 画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=False)
        tab_index += 1

        # 160 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：支給認定 <処理名>：支給認定延期一括更新処理
        # 161 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_160_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_160_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_160_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_160")

        # 162 スケジュール個別追加 画面:「支給認定延期一括更新対象　抽出」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_162_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 163 実行指示 画面: パラメータを入力
        params = [
            {"title": "発行年月日", "type": "text", "value": case_data_163_ZEAF002200.get("hakkou_nengappi", "")},
            {"title": "並び順", "type": "select", "value": case_data_163_ZEAF002200.get("narabijun", "")},
            {"title": "指定期間開始", "type": "text", "value": case_data_163_ZEAF002200.get("shitei_kikan_kaishi", "")},
            {"title": "指定期間終了", "type": "text",
             "value": case_data_163_ZEAF002200.get("shitei_kikan_shuuryou", "")},
            {"title": "延期日数", "type": "text", "value": case_data_163_ZEAF002200.get("enki_nissuu", "")},
            {"title": "抽出年月日", "type": "text", "value": case_data_163_ZEAF002200.get("chuushutsu_nengappi", "")},
            {"title": "延期理由", "type": "select", "value": case_data_163_ZEAF002200.get("enki_riyuu", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_163")

        # 164 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 165 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_165")

        # 166 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")
        self.screen_shot("結果確認 画面_166")

        # 167 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")

        # 168 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 169 ファイルダウンロード 画面:「No1」ボタン押下
        # 170 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 171 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 172 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_172")

        # 173 スケジュール個別追加 画面:「支給認定延期一括更新対象　更新」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_173_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)
        self.screen_shot("実行指示 画面_173")

        # 174 実行指示 画面:「実行」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002200_executebtn_button")

        # 175 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_175")

        # 176 メインメニュー 画面: メインメニューから「子ども子育て支援」をクリック
        # 177 メインメニュー 画面:「世帯情報」をクリック
        # 178 メインメニュー 画面:「検索」をダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3="検索",
                                 is_new_tab=False)
        tab_index += 1

        # 179 検索条件入力 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
            value=case_data_179_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
            text=case_data_179_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
            value=case_data_179_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
            text=case_data_179_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
            value=case_data_179_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
            value=case_data_179_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
            text=case_data_179_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
            text=case_data_179_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
            value=case_data_179_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
            value=case_data_179_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
            value=case_data_179_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
            value=case_data_179_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
            value=case_data_179_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
            text=case_data_179_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
            value=case_data_179_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
            value=case_data_179_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("検索条件入力 画面_179")

        # 180 検索条件入力 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        # 181 世帯履歴 画面: 支給認定延期通知書出力を出力する世帯台帳履歴の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            self.select_atena_code_after_relative_search(atena_code=case_data_181_QAZF100002.get("atena_code", ""),
                                                         tab_index=tab_index,
                                                         col_number=1)
        self.click_button_by_label("1")
        self.screen_shot("世帯台帳 画面_181")

        # 182 世帯台帳 画面:「児童一覧」タブをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF100300_jidoiciran_li")

        # 183 世帯台帳 画面: 支給認定延期通知書出力を出力するする児童の「№」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_183_QAPF100300.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)
        self.screen_shot("世帯台帳 画面_183")

        # 184 児童台帳 画面:「支給認定登録・履歴」タブをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103500_ShikyuNinteiTorokuRireki_li")

        # 185 児童台帳 画面: 支給認定延期通知書出力を出力する「№」ボタン押下
        self.click_button_by_label("1")
        self.screen_shot("支給認定情報 画面_185")

        # 186 支給認定情報 画面:「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103600_printbtn_button")
        self.screen_shot("印刷指示画面 画面_186")

        # 187 印刷指示画面 画面: 出力対象「支給認定交付延期通知書」にチェック
        # 188 印刷指示画面 画面: パラメータを入力
        exec_params = [
            {
                "report_name": case_data_188_QAPF900100.get("chouhyou_mei_1", ""),
                "params_1": [
                    {"title": "発行場所", "value": case_data_188_QAPF900100.get("hakkou_basho_1", "")},
                    {"title": "トレイ", "value": case_data_188_QAPF900100.get("torei_1", "")},
                    {"title": "部数", "value": case_data_188_QAPF900100.get("busuu_1", "")}
                ],
                "params_2": [
                    {"title": "発行年月日", "value": case_data_188_QAPF900100.get("hakkou_nengappi_1", "")}
                ]
            }
        ]
        checked_reports_count = self.select_kodomo_report(report_param_list=exec_params, tab_index=tab_index,
                                                          screen_shot_name="印刷指示画面 画面_188",
                                                          screen_id="QAPF900100")

        # 189 印刷指示画面 画面:「入力チェック」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF900100_checkBtn_button")
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF900100_msg_span",
                                 msg="入力チェックが完了しました。")

        # 190 印刷指示画面 画面:「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF900100_printBtn_button")

        # 191 印刷指示画面 画面:「印刷してよろしいですか」に「はい」ボタン押下
        self.alert_accept()

        # 192 ファイルダウンロード 画面: 出力対象で選択した帳票の「No1」ボタン押下
        # 193 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        self.print_kodomo_reports(limit_wait_count=20, time_span_sec=4, tab_index=tab_index,
                                  screen_shot_name="ファイルダウンロード 画面_192",
                                  count_report=checked_reports_count)

        # 194 印刷指示画面 画面:「閉じる」ボタン押下
        self.click_button_by_label("閉じる")

        # 195 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 196 メインメニュー 画面:「即時実行」クリック
        # 197 メインメニュー 画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=False)
        tab_index += 1

        # 198 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：支給認定 <処理名>：支給認定証_交付延期通知書_一覧出力処理
        # 199 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_198_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_198_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_198_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_198")

        # 200 スケジュール個別追加 画面:「支給認定証延期通知書　抽出」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_200_ZEAF000400.get("batch_job_003", ""),
                                             tab_index=tab_index)

        # 201 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_201_ZEAF002200.get("shokanku", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_201_ZEAF002200.get("hakkou_nengappi", "")},
            {"title": "延期登録日開始", "type": "text",
             "value": case_data_201_ZEAF002200.get("enki_tourokubi_kaishi", "")},
            {"title": "延期登録日終了", "type": "text",
             "value": case_data_201_ZEAF002200.get("enki_tourokubi_shuuryou", "")},
            {"title": "申請日開始", "type": "text", "value": case_data_201_ZEAF002200.get("shinseibi_kaishi", "")},
            {"title": "申請日終了", "type": "text", "value": case_data_201_ZEAF002200.get("shinseibi_shuuryou", "")},
            {"title": "児童宛名コード", "type": "text", "value": case_data_201_ZEAF002200.get("jidou_atena_koudo", "")},
            {"title": "並び順", "type": "select", "value": case_data_201_ZEAF002200.get("narabijun", "")},
            {"title": "再発行区分", "type": "select", "value": case_data_201_ZEAF002200.get("saihakkou_kubun", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_201")

        # 202 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 203 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_203")

        # 204 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 205 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_205")

        # 206 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 207 ファイルダウンロード 画面:「No1」ボタン押下
        # 208 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 209 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 210 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_210")

        # 211 スケジュール個別追加 画面:「支給認定証交付延期通知書出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_211_ZEAF000400.get("batch_job_004", ""),
                                             tab_index=tab_index)

        # 212 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_212_ZEAF002200.get("shokanku", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_212")

        # 213 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 214 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_214")

        # 215 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 216 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_216")

        # 217 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 218 ファイルダウンロード 画面:「No1」ボタン押下
        # 219 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 220 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
