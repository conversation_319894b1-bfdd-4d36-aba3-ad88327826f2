from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28070704(KodomoSiteTestCaseBase):
    """TestQAP010_28070704"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28070704(self):
        """還付口座登録"""

        case_data_135_JABF400200 = self.test_data["135_JABF400200"]
        case_data_139_JABF400300 = self.test_data["139_JABF400300"]
        case_data_140_JABF400300 = self.test_data["140_JABF400300"]
        case_data_148_JAPF000200 = self.test_data["148_JAPF000200"]
        tab_index = 0

        self.do_login_new_tab()

        # 132 メインメニュー 画面: 「収納」ボタン押下
        # 133 メインメニュー 画面: 「還付口座振込」ボタン押下
        # 134 メインメニュー 画面: 「還付（充当）還付振込管理」ボタンをダブルクリック
        self._goto_menu_by_label(menu_level_1="収納", menu_level_2="還付口座振込",
                                 menu_level_3="還付（充当）還付振込管理", is_new_tab=True)
        tab_index += 1

        # 135 （還付（充当）還付振込管理）検索条件入力 画面: 入力できること
        # 検索条件1:
        # 振込依頼種別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_selHurikomiIraiShubetu_select",
            text=case_data_135_JABF400200.get("furikomi_irai_shubetsu", ""))
        # 還付状態
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_selKanpuJoutai_select",
            text=case_data_135_JABF400200.get("kanpu_jotai", ""))
        # 支出区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_selShishutuKubun_select",
            text=case_data_135_JABF400200.get("shishutsu_kubun", ""))
        # 還付登録日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_txtKanpuTourokubiJi_textboxInput",
            value=case_data_135_JABF400200.get("kanpu_torokubi_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_txtKanpuTourokubiItaru_textboxInput",
            value=case_data_135_JABF400200.get("kanpu_torokubi_to", ""))
        # 支出決定日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_txtShishutuKetteiHiJi_textboxInput",
            value=case_data_135_JABF400200.get("shishutsu_ketteibi_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_txtShishutuKetteiHiItaru_textboxInput",
            value=case_data_135_JABF400200.get("shishutsu_ketteibi_to", ""))
        # 還付通知日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_txtKanpuTuutibiJi_textboxInput",
            value=case_data_135_JABF400200.get("kanpu_tsuchibi_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_txtKanpuTuutibiItaru_textboxInput",
            value=case_data_135_JABF400200.get("kanpu_tsuchibi_to", ""))
        # 還付請求日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_txtKanpuSeikyuuHiJi_textboxInput",
            value=case_data_135_JABF400200.get("kanpu_seikyubi_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_txtKanpuSeikyuuHiItaru_textboxInput",
            value=case_data_135_JABF400200.get("kanpu_seikyubi_to", ""))
        # 還付支払日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_txtKanpuShiharaibiJi_textboxInput",
            value=case_data_135_JABF400200.get("kanpu_shiharaibi_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_txtKanpuShiharaibiItaru_textboxInput",
            value=case_data_135_JABF400200.get("kanpu_shiharaibi_to", ""))
        # 行政区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400200_selWrGyoseiku_select",
            text=case_data_135_JABF400200.get("gyoseiku", ""))

        self.screen_shot("（還付（充当）還付振込管理）検索条件入力 画面_135")

        # 136 （還付（充当）還付振込管理）検索条件入力 画面: 「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JABF400200_WrCmnBtn05_button")

        # 137 還付振込管理 画面: 還付通知日：「全選択」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JABF400300_btnALLSentaku_button")
        self.screen_shot("還付振込管理 画面_137")

        # 138 還付振込管理 画面: 還付処理タブを押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="JABF400300", label="還付処理")
        self.screen_shot("還付振込管理 画面_138")

        # 139 還付振込管理 画面: 還付処理区分で「還付振込依頼データ作成」を選択
        # 還付処理区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400300_selKampuShoriKbn_select",
            text=case_data_139_JABF400300.get("kanpu_shori_kubun", ""))

        # 140 還付振込管理 画面: 入力できること
        # 還付支払日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JABF400300_txtFurikomibiIrai_textboxInput",
            value=case_data_140_JABF400300.get("kanpu_shiharaibi", ""))
        self.screen_shot("還付振込管理 画面_140")

        # 141 還付振込管理 画面: 「振込依頼データ作成」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JABF400300_btnFurikomiIraiDataSakusei_button")

        # 142 確認ダイアログ 画面:「No1」ボタン押下
        # 143 確認ダイアログ 画面:「ファイルを開く(O)」ボタン押下
        # 144 確認ダイアログ 画面:「×」ボタン押下
        self.alert_accept()
        self.file_download()
        self.save_kodomo_reports()
        self.screen_shot("確認ダイアログ 画面_142")
        self.click_button_by_label("閉じる")

        # 145 メインメニュー 画面: 「収納」ボタン押下
        # 146 メインメニュー 画面: 「還付帳票発行」ボタン押下
        # 147 メインメニュー 画面: 「還付振込帳票発行」ボタンをダブルクリック
        self._goto_menu_by_label(menu_level_1="収納", menu_level_2="還付帳票発行",
                                 menu_level_3="還付振込帳票発行")
        tab_index += 1

        # 148 （還付・充当）印刷指示 画面: 入力できること
        exec_params = [
            {
                "report_name": case_data_148_JAPF000200.get("chouhyou_mei_1", ""),
                "params_1": [
                    {"title": "発行場所", "value": case_data_148_JAPF000200.get("hakkou_basho_1", "")},
                    {"title": "トレイ", "value": case_data_148_JAPF000200.get("torei_1", "")},
                    {"title": "部数", "value": case_data_148_JAPF000200.get("busuu_1", "")}
                ],

                "params_2": [
                    {"title": "行政区", "value": case_data_148_JAPF000200.get("gyoseiku", "")},
                    {"title": "振込日", "value": case_data_148_JAPF000200.get("furikomibi", "")},
                ],
            },
        ]

        checked_reports_count = self.select_kodomo_report(report_param_list=exec_params, tab_index=tab_index,
                                                          screen_shot_name="（還付・充当）印刷指示 画面_148",
                                                          screen_id="JAPF000200")

        # 149 （還付・充当）印刷指示 画面:「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JAPF000200_printBtn_button")

        # 150 確認ダイアログ 画面:「はい」ボタン押下
        self.alert_accept()

        # 151 ファイルダウンロード 画面: 「No1」ボタン押下
        # 152 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        self.print_kodomo_reports(limit_wait_count=20, time_span_sec=4, tab_index=tab_index,
                                  screen_shot_name="ファイルダウンロード 画面_152", count_report=checked_reports_count)

        # 153 印刷指示画面 画面: 「閉じる」ボタン押下
        self.click_button_by_label("閉じる")
