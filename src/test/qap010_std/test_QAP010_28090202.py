from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28090202(KodomoSiteTestCaseBase):
    """TestQAP010_28090202"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28090202(self):
        case_data_051_QAZF100001 = self.test_data["051_QAZF100001"]
        case_data_052_QAZF100002 = self.test_data["052_QAZF100002"]
        case_data_053_QAPF100300 = self.test_data["053_QAPF100300"]
        case_data_055_QAPF221000 = self.test_data["055_QAPF221000"]
        tab_index = 0

        # 48 メインメニュー 画面:「子ども子育て表示」
        self.do_login_new_tab()

        # 49 メインメニュー 画面: メインメニューから「認可外申請管理」をクリック
        # 50 メインメニュー 画面:「認可外申請検索」をダブルクリック
        self._goto_menu_by_label(menu_level_1="認可外申請管理", menu_level_2="認可外申請検索", is_new_tab=True)
        tab_index += 1

        # 51 認可外申請検索 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
                value=case_data_051_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
                text=case_data_051_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
                value=case_data_051_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
                text=case_data_051_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
                value=case_data_051_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
                value=case_data_051_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
                text=case_data_051_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
                text=case_data_051_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
                value=case_data_051_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
                value=case_data_051_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
                value=case_data_051_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
                value=case_data_051_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
                value=case_data_051_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
                text=case_data_051_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
                value=case_data_051_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
                value=case_data_051_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("検索条件入力 画面_51")

        # 52 認可外申請検索 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_052_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code = atena_code, tab_index = tab_index)

        self.click_button_by_label("1")

        # 53 世帯台帳 画面: 対象児童の「No」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_053_QAPF100300.get("kodomo_atena_code", ""))
        self.screen_shot("利用者申請管理 画面_53")

        # 54 利用者申請管理 画面:「追加」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF221000_btnAddChg_button")

        # 55 利用者申請管理 画面: 変更内容を入力
        # 申請年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF221000_txtShinnseinengappi_textboxInput",
                value=case_data_055_QAPF221000.get("shinsei_nengetsu_bi", ""))
        # 申請理由
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF221000_selShinnseiriyuu_select",
                text=case_data_055_QAPF221000.get("shinsei_riyuu", ""))
        # 保育必要性
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF221000_selHoikuHituyousei_select",
                text=case_data_055_QAPF221000.get("hoiku_hitsuyousei", ""))
        # 事由発生年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF221000_txtZiyuuHasseiNengappi_textboxInput",
                value=case_data_055_QAPF221000.get("jiyuu_hassei_nengetsu_bi", ""))
        # 備考
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF221000_txtBikou_textarea",
                value=case_data_055_QAPF221000.get("bikou", ""))
        # 認定開始
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF221000_txtNinteiKaishi_textboxInput",
                value=case_data_055_QAPF221000.get("nintei_kaishi", ""))
        # 認定終了
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF221000_txtNinteiShuuryou_textboxInput",
                value=case_data_055_QAPF221000.get("nintei_shuuryou", ""))
        # 所管区
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF221000_selWrGyoseikuShoKanku_select",
                text=case_data_055_QAPF221000.get("shokatsu_ku", ""))
        # 決定年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF221000_txtKetteinengappi_textboxInput",
                value=case_data_055_QAPF221000.get("kettei_nengetsu_bi", ""))
        # 決定結果
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF221000_selKetteikekka_select",
                text=case_data_055_QAPF221000.get("kettei_kekka", ""))
        # 施設コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_ZZZ000000_txtShisetuCd2_1_1_textboxInput",
                value=case_data_055_QAPF221000.get("shisetsu_code", ""))
        # 利用区分
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyouKbn_1_3_select",
                text=case_data_055_QAPF221000.get("riyou_kubun", ""))
        # 利用開始日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyouKaishibi_1_4_textboxInput",
                value=case_data_055_QAPF221000.get("riyou_kaishi_bi", ""))
        # 利用終了日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyouShuuryoubi_1_5_textboxInput",
                value=case_data_055_QAPF221000.get("riyou_shuuryou_bi", ""))
        # 支払方法
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_ZZZ000000_selShiharaiHouhou_1_6_select",
                text=case_data_055_QAPF221000.get("shiharai_houhou", ""))
        # 代理
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_ZZZ000000_chkDaihyouShisetu_1_8chk0",
                value=case_data_055_QAPF221000.get("dairi", ""))
        self.screen_shot("利用者申請管理 画面_55")

        # 56 利用者申請管理 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF221000_regbtn_button")

        # 57 利用者申請管理 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF221000_msg_span", msg="登録しました。")
        self.screen_shot("利用者申請管理 画面_57")
