
USE WRG$$JICHITAI_CODE$$QA

DELETE 
FROM QPDI補足給付申請算定結果
WHERE [自治体C] = '$$JICHITAI_CODE$$'
    AND [業務C] = '$$業務C$$'
    AND [児童_宛名C] = '$$児童_宛名C$$'

INSERT INTO QPDI補足給付申請算定結果
(
 [データ区分],
 [自治体C],
 [業務C] ,
 [所管区コード] ,
 [所管区] ,
 [支所コード] ,
 [支所] ,
 [代理施設コード] ,
 [代理施設名] ,
 [利用年月] ,
 [世帯台帳番号] ,
 [児童_宛名C] ,
 [保護者1_宛名C] ,
 [保護者2_宛名C] ,
 [利用区分分類] ,
 [決定結果C] ,
 [決定結果] ,
 [強制修正F] ,
 [支払方法C] ,
 [支払方法] ,
 [利用区分C] ,
 [利用区分] ,
 [枝番] ,
 [施設コード] ,
 [施設名称] ,
 [請求番号] ,
 [請求枝番] ,
 [児童_氏名漢字] ,
 [児童_氏名カナ] ,
 [児童_性別] ,
 [児童_生年月日] ,
 [児童_満年齢] ,
 [児童_クラス年齢] ,
 [児童_住民でなくなった日] ,
 [施設種類C] ,
 [施設種類] ,
 [管理者_名称カナ] ,
 [管理者_氏名漢字] ,
 [事業所_郵便番号] ,
 [事業所_住所] ,
 [事業所_方書] ,
 [満年齢基準日] ,
 [クラス年齢基準日] ,
 [納付額] ,
 [限度額] ,
 [補足給付額] ,
 [利用日数] ,
 [開所日数] ,
 [入園料年額] ,
 [入園料月額] ,
 [年度内在園月数] ,
 [決定年月日] ,
 [請求年月日] ,
 [却下理由C] ,
 [却下理由] ,
 [戻入額] ,
 [戻入日] ,
 [振込年月日] ,
 [履歴番号] ,
 [申請理由C] ,
 [申請理由] ,
 [認定期間開始] ,
 [認定期間終了] ,
 [保育必要性C] ,
 [事由発生年月日] ,
 [限度額1] ,
 [限度額2] ,
 [利用期間開始] ,
 [利用期間終了] ,
 [履歴番号_新支給認定] ,
 [認定者番号_新支給認定] ,
 [認定期間_開始_新支給認定] ,
 [認定期間_終了_新支給認定] ,
 [認定区分C_新支給認定] ,
 [認定区分_新支給認定] ,
 [履歴番号_旧支給認定] ,
 [認定者番号_旧支給認定] ,
 [認定期間_開始_旧支給認定] ,
 [認定期間_終了_旧支給認定] ,
 [認定区分C_旧支給認定] ,
 [認定区分_旧支給認定] ,
 [申請番号] ,
 [申請履歴番号] ,
 [所得判定年月日] ,
 [所得判定結果_詳細C] ,
 [所得判定結果_詳細] ,
 [所得判定結果C] ,
 [所得判定結果] ,
 [免除F] ,
 [免除] ,
 [理由C] ,
 [理由] ,
 [市副食免除対象者F] ,
 [市副食免除対象者] ,
 [認定開始月] ,
 [均等割] ,
 [所得割] ,
 [課税状況C] ,
 [課税状況] ,
 [世帯課税区分C] ,
 [認定月_開始] ,
 [認定月_終了] ,
 [里子里親該当] ,
 [金融機関コード] ,
 [金融機関名] ,
 [金融機関名カナ] ,
 [支店コード] ,
 [支店名] ,
 [支店名カナ] ,
 [口座名義人カナ] ,
 [口座名義人漢字] ,
 [口座番号] ,
 [預金種別] ,
 [預金種別名称] ,
 [日額単価] ,
 [上限額] ,
 [上限額_日割り] ,
 [日割計算区分] ,
 [結果区分] ,
 [算定対象フラグ] ,
 [通知書出力フラグ] ,
 [締めフラグ] ,
 [幼稚園途中入退所該当F] ,
 [月途中認定開始終了F] ,
 [限度額_MAX] ,
 [限度額_使用済] ,
 [限度額_日割後] ,
 [補足給付額_算定] ,
 [納付額_入園料合算後] ,
 [今回支給額] ,
 [今回請求額] ,
 [支払済額] ,
 [振込予定日_算定処理] ,
 [決定年月日_算定処理] ,
 [利用年月開始_算定処理] ,
 [利用年月終了_算定処理] ,
 [支払方法区分C] ,
 [汎用項目1] ,
 [汎用項目2] ,
 [汎用項目3] ,
 [汎用項目4] ,
 [汎用項目5] ,
 [汎用項目6] ,
 [汎用項目7] ,
 [汎用項目8] ,
 [汎用項目9] ,
 [汎用項目10] ,
 [汎用日付1] ,
 [汎用日付2] ,
 [汎用日付3] ,
 [汎用日付4] ,
 [汎用日付5] ,
 [汎用日付6] ,
 [汎用日付7] ,
 [汎用日付8] ,
 [汎用日付9] ,
 [汎用日付10] ,
 [汎用テキスト1] ,
 [汎用テキスト2] ,
 [汎用テキスト3] ,
 [汎用テキスト4] ,
 [汎用テキスト5] ,
 [削除F] ,
 [登録日時] ,
 [更新日時] ,
 [登録職員ID] ,
 [更新職員ID] ,
 [更新プログラム名]
)
VALUES
('1', '99101', 'QAP010', '10000', 'テスト一区', '', '', '11001', '未移行幼稚園１２３', '202405', '15341', '101200000101665', '101200000101662', '101200000101663', '4', '02', '算定済み', '0', '01', '利用者（償還）', '01', '補足給付', '1', '11001', '未移行幼稚園１２３', '200000', '1', '検証　あゆみ', 'ｹﾝｼｮｳ ｱﾕﾐ', '女', '20210101', '3', '3', '99999999', '100', '未移行幼稚園', '', '', '123-4567', '子ども県子ども市テスト１丁目外字①', '', '20240527', '20240331', '2000000', '4700', '4700', '0', '0', '0', '0', '0', '20240527', '20240526', '', '', '0', '00000000', '20240527', '8238', '01', '新規', '202405  ', '202405  ', '', '20240527', '0', '0', '20240527', '20250331', '0', '', '00000000', '00000000', '', '', '0', '', '00000000', '00000000', '', '', '100', '1', '20240510', '2', '里子・里親', '1', '免除対象', '1', '該当', '', '', '0', '非該当', '000000', '0', '0', '', '', '', '000000', '000000', '1', '9900', 'ゆうちょ銀行', 'ﾕｳﾁｮﾕｳﾁｮ', '002', 'テス０３', 'ﾃｽ03', 'ｶﾜｻｷｸﾝ', '川崎君', '1110021', '1', '普通', '0', '4700', '4700', '0', '正常', '1', '1', '0', '1', '0', '4700', '0', '0', '4700', '0', '0', '0', '0', '00000000', '20240527', '202405', '202405', '', '', '', '', '', '', '', '', '', '', '', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '', '', '', '', '', '0', '2024-05-27 17:27:09.023', '2024-05-27 20:05:44.303', '1100', '1100', 'QPNBN05900'),
('1', '99101', 'QAP010', '10000', 'テスト一区', '', '', '11001', '未移行幼稚園１２３', '202405', '15341', '101200000101665', '101200000101662', '101200000101663', '4', '02', '算定済み', '0', '01', '利用者（償還）', '02', '補足給付', '1', '11001', '未移行幼稚園１２３', '200001', '1', '検証　あゆみ', 'ｹﾝｼｮｳ ｱﾕﾐ', '女', '20210101', '3', '3', '99999999', '999', '未移行幼稚園', '', '', '123-4567', '子ども県子ども市テスト１丁目外字①', '', '20240527', '20240331', '2000000', '0', '0', '0', '0', '0', '0', '0', '20240527', '20240526', '', '', '0', '00000000', '20240527', '8238', '01', '新規', '202405  ', '202405  ', '', '20240527', '0', '0', '20240527', '20250331', '0', '', '00000000', '00000000', '', '', '0', '', '00000000', '00000000', '', '', '100', '1', '20240510', '2', '里子・里親', '1', '免除対象', '1', '該当', '', '', '0', '非該当', '000000', '0', '0', '', '', '', '000000', '000000', '1', '9900', 'ゆうちょ銀行', 'ﾕｳﾁｮﾕｳﾁｮ', '002', 'テス０３', 'ﾃｽ03', 'ｶﾜｻｷｸﾝ', '川崎君', '1110022', '1', '普通', '0', '4700', '4700', '0', '正常', '1', '1', '0', '1', '0', '0', '0', '0', '0', '0', '0', '0', '0', '00000000', '20240527', '202405', '202405', '', '', '', '', '', '', '', '', '', '', '', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '', '', '', '', '', '0', '2024-05-27 17:27:09.023', '2024-05-27 20:05:44.303', '1100', '1100', 'QPNBN05900'),
('1', '99101', 'QAP010', '10000', 'テスト一区', '', '', '11001', '未移行幼稚園１２３', '202405', '15341', '101200000101665', '101200000101662', '101200000101663', '4', '02', '算定済み', '0', '01', '利用者（償還）', '03', '補足給付', '1', '11001', '未移行幼稚園１２３', '200002', '1', '検証　あゆみ', 'ｹﾝｼｮｳ ｱﾕﾐ', '女', '20210101', '3', '3', '99999999', '998', '未移行幼稚園', '', '', '123-4567', '子ども県子ども市テスト１丁目外字①', '', '20240527', '20240331', '2000000', '0', '0', '0', '0', '0', '0', '0', '20240527', '20240526', '', '', '0', '00000000', '20240527', '8238', '01', '新規', '202405  ', '202405  ', '', '20240527', '0', '0', '20240527', '20250331', '0', '', '00000000', '00000000', '', '', '0', '', '00000000', '00000000', '', '', '100', '1', '20240510', '2', '里子・里親', '1', '免除対象', '1', '該当', '', '', '0', '非該当', '000000', '0', '0', '', '', '', '000000', '000000', '1', '9900', 'ゆうちょ銀行', 'ﾕｳﾁｮﾕｳﾁｮ', '002', 'テス０３', 'ﾃｽ03', 'ｶﾜｻｷｸﾝ', '川崎君', '1110023', '1', '普通', '0', '4700', '4700', '0', '正常', '1', '1', '0', '1', '0', '0', '0', '0', '0', '0', '0', '0', '0', '00000000', '20240527', '202405', '202405', '', '', '', '', '', '', '', '', '', '', '', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '', '', '', '', '', '0', '2024-05-27 17:27:09.023', '2024-05-27 20:05:44.303', '1100', '1100', 'QPNBN05900'),
('1', '99101', 'QAP010', '10000', 'テスト一区', '', '', '11001', '未移行幼稚園１２３', '202405', '15341', '101200000101665', '101200000101662', '101200000101663', '4', '02', '算定済み', '0', '01', '利用者（償還）', '04', '補足給付', '1', '11001', '未移行幼稚園１２３', '200003', '1', '検証　あゆみ', 'ｹﾝｼｮｳ ｱﾕﾐ', '女', '20210101', '3', '3', '99999999', '997', '未移行幼稚園', '', '', '123-4567', '子ども県子ども市テスト１丁目外字①', '', '20240527', '20240331', '2000000', '0', '0', '0', '0', '0', '0', '0', '20240527', '20240526', '', '', '0', '00000000', '20240527', '8238', '01', '新規', '202405  ', '202405  ', '', '20240527', '0', '0', '20240527', '20250331', '0', '', '00000000', '00000000', '', '', '0', '', '00000000', '00000000', '', '', '100', '1', '20240510', '2', '里子・里親', '1', '免除対象', '1', '該当', '', '', '0', '非該当', '000000', '0', '0', '', '', '', '000000', '000000', '1', '9900', 'ゆうちょ銀行', 'ﾕｳﾁｮﾕｳﾁｮ', '002', 'テス０３', 'ﾃｽ03', 'ｶﾜｻｷｸﾝ', '川崎君', '1110024', '1', '普通', '0', '4700', '4700', '0', '正常', '1', '1', '0', '1', '0', '0', '0', '0', '0', '0', '0', '0', '0', '00000000', '20240527', '202405', '202405', '', '', '', '', '', '', '', '', '', '', '', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '', '', '', '', '', '0', '2024-05-27 17:27:09.023', '2024-05-27 20:05:44.303', '1100', '1100', 'QPNBN05900'),
('1', '99101', 'QAP010', '10000', 'テスト一区', '', '', '11001', '未移行幼稚園１２３', '202405', '15341', '101200000101665', '101200000101662', '101200000101663', '4', '02', '算定済み', '0', '01', '利用者（償還）', '27', '補足給付', '1', '11001', '未移行幼稚園１２３', '200004', '1', '検証　あゆみ', 'ｹﾝｼｮｳ ｱﾕﾐ', '女', '20210101', '3', '3', '99999999', '996', '未移行幼稚園', '', '', '123-4567', '子ども県子ども市テスト１丁目外字①', '', '20240527', '20240331', '2000000', '0', '0', '0', '0', '0', '0', '0', '20240527', '20240526', '', '', '0', '00000000', '20240527', '8238', '01', '新規', '202405  ', '202405  ', '', '20240527', '0', '0', '20240527', '20250331', '0', '', '00000000', '00000000', '', '', '0', '', '00000000', '00000000', '', '', '100', '1', '20240510', '2', '里子・里親', '1', '免除対象', '1', '該当', '', '', '0', '非該当', '000000', '0', '0', '', '', '', '000000', '000000', '1', '9900', 'ゆうちょ銀行', 'ﾕｳﾁｮﾕｳﾁｮ', '002', 'テス０３', 'ﾃｽ03', 'ｶﾜｻｷｸﾝ', '川崎君', '1110025', '1', '普通', '0', '4700', '4700', '0', '正常', '1', '1', '0', '1', '0', '0', '0', '0', '0', '0', '0', '0', '0', '00000000', '20240527', '202405', '202405', '', '', '', '', '', '', '', '', '', '', '', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '', '', '', '', '', '0', '2024-05-27 17:27:09.023', '2024-05-27 20:05:44.303', '1100', '1100', 'QPNBN05900'),
('1', '99101', 'QAP010', '10000', 'テスト一区', '', '', '11001', '未移行幼稚園１２３', '202405', '15341', '101200000101665', '101200000101662', '101200000101663', '4', '02', '算定済み', '0', '01', '利用者（償還）', '50', '補足給付', '1', '11001', '未移行幼稚園１２３', '200005', '1', '検証　あゆみ', 'ｹﾝｼｮｳ ｱﾕﾐ', '女', '20210101', '3', '3', '99999999', '100', '未移行幼稚園', '', '', '123-4567', '子ども県子ども市テスト１丁目外字①', '', '20240527', '20240331', '2000000', '0', '0', '0', '0', '0', '0', '0', '20240527', '20240526', '', '', '0', '00000000', '20240527', '8238', '01', '新規', '202405  ', '202405  ', '', '20240527', '0', '0', '20240527', '20250331', '0', '', '00000000', '00000000', '', '', '0', '', '00000000', '00000000', '', '', '100', '1', '20240510', '2', '里子・里親', '1', '免除対象', '1', '該当', '', '', '0', '非該当', '000000', '0', '0', '', '', '', '000000', '000000', '1', '9900', 'ゆうちょ銀行', 'ﾕｳﾁｮﾕｳﾁｮ', '002', 'テス０３', 'ﾃｽ03', 'ｶﾜｻｷｸﾝ', '川崎君', '1110026', '1', '普通', '0', '4700', '4700', '0', '正常', '1', '1', '0', '1', '0', '0', '0', '0', '0', '0', '0', '0', '0', '00000000', '20240527', '202405', '202405', '', '', '', '', '', '', '', '', '', '', '', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '', '', '', '', '', '0', '2024-05-27 17:27:09.023', '2024-05-27 20:05:44.303', '1100', '1100', 'QPNBN05900')


DELETE 
FROM QPDI認可外全銀F管理
WHERE [自治体C] = '$$JICHITAI_CODE$$'
    AND [業務C] = '$$業務C$$'
    AND [施設種類C] >= '994'
	AND [施設種類C] <= '999'

INSERT INTO QPDI認可外全銀F管理
(
 [自治体C],
 [業務C] ,
 [所管区コード] ,
 [支所コード] ,
 [出力分類] ,
 [利用区分C] ,
 [施設種類C] ,
 [支払方法C] ,
 [有効期間開始日] ,
 [有効期間終了日] ,
 [削除フラグ] ,
 [更新日時] ,
 [更新職員ID] ,
 [更新プログラム名]
)
VALUES
('99101', 'QAP010', '00000', '000', '3', '02', '999', '01', '20140401', '99991231', '0', '2024-05-27 17:27:09.023', '1100', 'INES_SETUP'),
('99101', 'QAP010', '00000', '000', '4', '03', '998', '01', '20140401', '99991231', '0', '2024-05-27 17:27:09.023', '1100', 'INES_SETUP'),
('99101', 'QAP010', '00000', '000', '5', '04', '997', '01', '20140401', '99991231', '0', '2024-05-27 17:27:09.023', '1100', 'INES_SETUP'),
('99101', 'QAP010', '00000', '000', '6', '27', '996', '01', '20140401', '99991231', '0', '2024-05-27 17:27:09.023', '1100', 'INES_SETUP'),
('99101', 'QAP010', '00000', '000', '1', '02', '995', '01', '20140401', '99991231', '0', '2024-05-27 17:27:09.023', '1100', 'INES_SETUP'),
('99101', 'QAP010', '00000', '000', '2', '02', '994', '01', '20140401', '99991231', '0', '2024-05-27 17:27:09.023', '1100', 'INES_SETUP')