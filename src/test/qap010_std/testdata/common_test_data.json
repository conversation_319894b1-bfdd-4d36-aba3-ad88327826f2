{"sql_params": {"QAP010_ATENACODE01": "110100000100607", "QAP010_ATENACODE02": "110100000100606", "QAP010_ATENACODE03": "110100000100608", "QAP010_ATENACODE04": "110100000100613"}, "case_qap001_atena_code1": "110100000100607", "case_qap001_atena_code2": "00120000", "case_qap001_atena_code3": "00120000", "case_qap001_atena_code4": "00120000", "case_qap001_atena_code5": "00120000", "case_qap001_atena_code6": "00120000", "case_qap001_atena_code7": "00120000", "case_qap001_atena_code8": "00120000", "case_qap001_atena_code9": "00120000", "case_qap001_atena_code10": "00120000", "case_qap001_atena_code11": "00120000", "test_qap010_28010101": {"case_qap001_kana_shimei": "ｹﾝｼｮｳﾀﾞｲｺ", "case_qap001_seinengappi": "19760127", "QAP010_28010101_ATENACODE01": "110100000100108", "QAP010_28010101_ATENACODE02": "110100000100109", "QAP010_28010101_ATENACODE03": "110100000100110", "QAP010_28010101_ATENACODE04": "110100000100111", "case_qap001_sentakubotan": "tab01_ZZZ000000_btnJIDoIchiranRemban_1_1_button", "case_qap001_shokanku": "", "case_qap001_jigyousho_bangou": "19890122", "case_qap001_shisho": "", "case_qap001_taishoubi": "20180401", "case_qap001_narabi_jun": "児童_生年月日(降順),児童_氏名カナ", "case_qap001_shinseibi_kaishi": "20200301", "case_qap001_shikyu_ninteishou_bangou": "", "case_qap001_shinsei_shubetsu": "新規", "case_qap001_shinsei_jiyuu": "年次募集", "case_qap001_narabijun": "申請日,児童_生年月日(降順),児童_氏名カナ,児童_宛名コード", "batch_click_count": "3600", "batch_wait_seconds": "20", "一号認定取込データファイル": "一号認定取込データ.csv", "GyomuNM": "子ども子育て支援", "SubSystemNM": "支給認定", "ShoriNM_1": "支給認定_一号データ取込", "ShoriNM_2": "支給認定申請一覧表出力処理"}, "test_qap010_28010102": {"case_qap001_shikyuu_kettei_kaishibi": "20200301", "case_qap001_shikyuu_ninteishou_bangou": "", "case_qap001_shinsei_jiyuu": "年次募集", "case_qap001_nintei_kubun": "１号認定子ども", "case_qap001_narabijun": "認定区分, 児童_生年月日（降順）, 児童_氏名カナ", "case_qap001_saihakkou_kubun": "有", "case_qap001_yubin_kunai_tokubetsu_umu": "有", "case_qap001_shikyuu_kettei_kaishibi1": "20200301", "case_qap001_shikyuu_ninteishou_bangou1": "", "case_qap001_saihakkou_kubun1": "有", "case_qap001_shinsei_jiyuu1": "", "case_qap001_chushutsu_kubun": "取下", "case_qap001_nintei_kubun1": "", "case_qap001_narabijun1": "認定区分, 児童_生年月日（降順）, 児童_氏名カナ", "case_qap001_yubin_kunai_tokubetsu_umu1": "有", "case_qap001_shikyuu_kettei_kaishibi2": "20200301", "case_qap001_shikyuu_kettei_shuryoubi": "", "case_qap001_jigyousho_bangou": "", "case_qap001_shikyuu_ninteishou_bangou2": "", "case_qap001_class_nenrei_sanshutsu_kijun_nendo": "令和02年", "case_qap001_shinsei_jiyuu2": "年次募集", "case_qap001_nintei_kubun2": "１号認定子ども", "case_qap001_narabijun2": "生年月日（降順）, 児童カナ氏名, 児童宛名", "case_qap001_saihakkou_kubun2": "有", "case_qap001_yubin_kunai_tokubetsu_umu2": "有", "case_qap001_shinseibi_kaishi": "20200301", "case_qap001_narabijun3": "申請日, 児童_生年月日(降順), 児童カナ氏名, 児童宛名コード", "case_qap001_enki_tourokubi_kaishi": "20150301", "case_qap001_shinseibi_kaishi1": "20150301", "case_qap001_jidou_atena_code": "", "case_qap001_saihakkou_kubun3": "有", "case_qap001_narabijun4": "児童_生年月日（降順）, 児童_氏名カナ, 児童_宛名コード", "case_qap001_shikyuu_kettei_kaishibi3": "20200301", "case_qap001_shikyuu_ninteishou_bangou3": "", "case_qap001_saihakkou_kubun4": "有", "case_qap001_shinsei_jiyuu3": "年次募集", "case_qap001_nintei_kubun3": "１号認定子ども", "case_qap001_narabijun5": "認定区分, 児童_生年月日（降順）, 児童_氏名カナ", "case_qap001_yubin_kunai_tokubetsu_umu3": "有", "batch_click_count": "3600", "batch_wait_seconds": "20", "GyomuNM": "子ども子育て支援", "SubSystemNM": "支給認定", "ShoriNM_1": "支給認定証_通知書_一覧出力処理", "ShoriNM_2": "支給認定証取下(却下）通知書_一覧出力処理", "ShoriNM_3": "支給認定証_交付通知書_一覧(代理申請)出力処理", "ShoriNM_4": "支給認定未処理一覧出力処理", "ShoriNM_5": "支給認定証_交付延期通知書_一覧出力処理", "ShoriNM_6": "受給証明書出力処理"}, "test_qap010_28010201": {"case_qap001_shikyuu_kettei_kaishibi": "20220301", "case_qap001_shisetsu_code": "", "case_qap001_shisetsu_shurui": "", "case_qap001_narabi_jun": "認定区分、生年月日(降順)、児童カナ氏名、児童宛名コード", "case_qap001_jidou_seinengappi_kaishi": "20200301", "case_qap001_shussanji_nintei_kanou_nissuu": "0", "case_qap001_kyuushokuji_nintei_kanou_nissuu": "0", "case_qap001_shikyuu_nintei_bangou_haibanumu": "有", "case_qap001_narabijun": "生年月日(降順)、児童カナ氏名、児童宛名コード", "case_qap001_shisetsu_code1": "", "case_qap001_jidou_atena_code": "", "case_qap001_nyuusho_joutai_kubun": " 全て", "case_qap001_yuubin_kunai_tokubetsu_umu": "有", "case_qap001_narabi_jun1": "入所状態、施設コード、児童生年月日(降順)、児童カナ名称、児童宛名コード", "case_qap001_shikyuu_kettei_kaishibi1": "20200301", "case_qap001_shikyuu_ninteishou_bangou": "", "case_qap001_jidou_atena_code1": "", "case_qap001_shisetsu_code2": "", "case_qap001_shinsei_shubetsu": "全て", "case_qap001_nyuusho_joutai_kubun1": "全て", "case_qap001_nintei_kubun": "２号認定子ども", "case_qap001_narabijun2": "入所状態、施設コード、生年月日(降順)、児童カナ氏名、児童宛名コード", "case_qap001_saihakkou_kubun": "有", "case_qap001_yuubin_kunai_tokubetsu_umu1": "有", "batch_click_count": "3600", "batch_wait_seconds": "20", "GyomuNM": "子ども子育て支援", "SubSystemNM": "支給認定", "ShoriNM_1": "認定区分変更一覧表（３号→２号）出力処理", "ShoriNM_2": "３号→２号切替処理", "ShoriNM_3": "支給認定変更決定通知書_一覧出力処理"}, "test_qap010_28010202": {"case_qap001_kana_shimei": "ｹﾝｼｮｳﾀﾞｲｺ", "case_qap001_seinengappi": "19760127", "QAP010_28010202_ATENACODE01": "110100000100108", "case_qap001_shikyu_nintei_kubun": "３号", "batch_click_count": "3600", "batch_wait_seconds": "20"}, "test_qap010_28010203": {"case_qap001_shisetsu_code": "", "case_qap001_jidou_atena_code": "", "batch_click_count": "3600", "batch_wait_seconds": "20", "GyomuNM": "子ども子育て支援", "SubSystemNM": "支給認定", "ShoriNM_1": "３号→２号切替処理", "ShoriNM_2": "支給認定変更決定通知書_一覧出力処理"}, "test_qap010_28010204": {"case_qap001_shikyuu_kettei_kaishibi": "20200301", "case_qap001_shikyuu_ninteishou_bangou": "", "case_qap001_shisetsu_code": "", "case_qap001_daiichi_shisetsu_code": "", "case_qap001_shokanku": "", "case_qap001_shinsei_jiyuu": "年次募集", "case_qap001_nintei_kubun": "１号認定子ども", "case_qap001_narabijun": "認定区分, 児童_生年月日（降順）, 児童_氏名カナ", "case_qap001_saihakkou_kubun": "有", "case_qap001_shisetsude_kaipage": "有", "case_qap001_nyuusho_joutai_kubun": "入所", "case_qap001_shikyuu_kettei_kaishibi1": "20200301", "case_qap001_shikyuu_kettei_shuryoubi": "", "case_qap001_shikyuu_ninteishou_bangou1": "", "case_qap001_class_nenrei_sanshutsu_kijun_nendo": "令和02年", "case_qap001_shokanku1": "", "case_qap001_shinsei_jiyuu1": "年次募集", "case_qap001_nintei_kubun1": "１号認定子ども", "case_qap001_narabijun1": "生年月日（降順）, 児童カナ氏名, 児童宛名", "case_qap001_saihakkou_kubun1": "有", "batch_click_count": "3600", "batch_wait_seconds": "20", "GyomuNM": "子ども子育て支援", "SubSystemNM": "支給認定", "ShoriNM_1": "支給認定証_通知書_一覧出力処理", "ShoriNM_2": "支給認定証_交付通知書_一覧(代理申請)出力処理"}, "test_qap010_28010301": {"case_qap001_kana_shimei": "ｹﾝｼｮｳﾀﾞｲｺ", "case_qap001_seinengappi": "19760127", "QAP010_28010301_ATENACODE01": "110100000100108", "case_qap001_torikeshi_riyuu": "第19条第1項第3号非該当", "case_qap001_ninteikikan_kaishi": "20250713"}, "test_qap010_28010302": {"case_qap001_torikeshi_nenngappi_kaishi": "20200301", "case_qap001_shikyuu_ninteishou_bangou": "", "case_qap001_shinsei_jiyuu": "年次募集", "case_qap001_nintei_kubun": "２号認定子ども", "case_qap001_narabi_jun": "認定区分,児童_生年月日(降順),児童_氏名カナ", "case_qap001_saihakkou_kubun": "有", "case_qap001_yuubinkunai_tokubetsuumu": "有", "batch_click_count": "3600", "batch_wait_seconds": "20", "GyomuNM": "子ども子育て支援", "SubSystemNM": "支給認定", "ShoriNM_1": "教育・保育給付認定取消通知書"}, "test_qap010_28010401": {"case_qap001_kijun_bi": "20240420", "case_qap001_shutsuryoku_kubun": "支給認定", "case_qap001_shitei_nengappi": "20240420", "case_qap001_narabi_jun": "児童_生年月日（降順）,児童_氏名,児童_宛名C", "case_qap001_saihakkou_kubun": "有", "case_qap001_shutsuryoku_kubun1": "施設等利用給付認定", "case_qap001_shitei_nengappi1": "20240530", "case_qap001_narabi_jun1": "世帯台帳番号,児童_生年月日（降順）,児童_宛名C", "case_qap001_saihakkou_kubun1": "有", "case_qap001_shisho": "", "case_qap001_setai_daichou_bangou": "", "case_qap001_shisetsu_shurui": "", "case_qap001_shisetsu_code": "", "case_qap001_jutaku_kubun": "", "case_qap001_saihakkou_kubun2": "有", "case_qap001_narabi_jun2": "生年月日, 児童宛名C", "case_qap001_vercode": "0000012275", "case_qap001_shokanku": "", "case_qap001_shutsuryoku_kubun2": "支給認定分", "case_qap001_narabi_jun3": "生年月日(降順)、児童カナ、児童住民C", "case_qap001_taishou_nendo": "2022", "case_qap001_yubin_kunai_tokubetsu_umu": "有", "case_qap001_shokanku1": "", "case_qap001_shutsuryoku_kubun3": "施設等利用給付認定", "batch_click_count": "3600", "batch_wait_seconds": "20", "GyomuNM": "子ども子育て支援", "SubSystemNM": "入所", "ShoriNM_1": "現況処理(世帯別)", "ShoriNM_2": "入所継続届出力処理", "ShoriNM_3": "現況届未提出者督促状_一覧出力処理"}, "test_qap010_28010403": {"case_qap001_atena_code": "110100000100111", "QAP010_28010403_ATENACODE01": "110100000100111", "batch_click_count": "3600", "batch_wait_seconds": "20"}, "test_QAP010_28020102": {"case_qap013_kana_shimei": "ｹﾝｼｮｳﾃﾂﾛｳ", "case_qap013_seinengappi": "19890227", "case_qap013_sentakubotan": "tab01_ZZZ000000_btnJIDoIchiranRemban_1_1_button", "case_qap013_kiboushisetu": "100", "case_qap013_kiboukikannsyuuryou": "20250331", "case_qap013_moushikomikiboukikannkaishi": "20250401", "case_qap013_moushikomikiboukikannsyuuryou": "20280331", "case_qap013_kizyunbi": "20250401", "QAP010_28020102_ATENACODE01": "110100000101452", "QAP010_28020102_ATENACODE02": "110100000101453", "QAP010_28020102_ATENACODE03": "110100000101454", "QAP010_28020102_ATENACODE04": "110100000101455", "batch_click_count": "3600", "batch_wait_seconds": "20", "GyomuNM": "子ども子育て支援", "SubSystemNM_1": "選考", "SubSystemNM_2": "入所", "ShoriNM_1": "入所申込一覧出力処理", "ShoriNM_2": "一括入所申込処理"}, "test_qap010_28020201": {"case_qap001_senkodata_mei": "標準化シナリオテスト", "case_qap001_syorikubun": "仮処理", "case_qap001_moushikomi_start": "", "case_qap001_yotei_date": "20230501", "case_qap001_kijyun_date": "20230501", "空き人数反映用ファイル": "99101_00000_QAP010_QP5BR00520_84_1_QP5BN01900_1_空き人数反映用.csv", "batch_click_count": "3600", "batch_wait_seconds": "20"}, "test_qap010_28020202": {"QAP010_28020202_ATENACODE01": "110100000100613", "case_qap001_kijyun_date_start": "20240701", "case_qap001_kijyun_date_end": "20240701", "case_qap001_syorikubun": "仮処理", "case_qap001_senkodata_mei": "標準化シナリオテスト", "case_qap001_moushikomi_start": "", "case_qap001_yotei_date": "20240701", "case_qap001_kijyun_date": "20240701"}, "test_qap010_28020203": {"QAP010_28020203_ATENACODE01": "110100000100613"}, "test_qap010_28020204": {"case_qap001_taisyou_nenngetu_1": "201507", "case_qap001_taisyou_nenngetu_2": "201504", "case_qap001_taisyou_nenngetu_3": "202405", "case_qap001_shinsei_date_start": ""}, "TestQAP010_28050101": {"sql_params": {"自治体C": "99101", "業務C": "QAP010", "事業者番号": "852963457", "事業所番号": "852963458", "施設コード": "000000004", "事業開始年月": "202812"}, "事業者_異動年月日": "20240522", "事業者_異動年月日2": "20240522", "事業者_異動内容": "新規", "事業者_決定年月日": "20281201", "事業者_審査結果": "承認", "事業者_法人等カナ名称": "ﾃｽﾄｶﾅﾏﾐﾑﾒﾒ", "事業者_法人等名称": "あいうえお", "事業者_郵便番号（前３桁）": "102", "事業者_郵便番号（後４桁）": "0072", "事業者_住所": "東京都千代田区飯田橋", "事業者_電話番号": "03-1111-2222", "事業者_法人等種別": "社会福祉法人", "事業者_法人等所在地（県）": "北海道", "事業者_法人等所在地（市区町村）": "札幌市", "事業者_設立年月日": "20240822", "事業者_代表者カナ氏名": "ﾔﾕﾑﾖﾒﾍﾈﾐﾐ", "事業者_代表者氏名": "代表者氏名", "事業者_代表者職名": "オーナー", "事業者_就任年月日": "20281201", "事業所_異動年月日": "20240522", "事業所_異動内容": "新規", "事業所_決定年月日": "20281201", "事業所_審査結果": "承認", "事業所_施設コード": "000000004", "事業所_所在区": "テスト一区", "事業所_認可区分": "認可", "事業所_認可年月日": "20281201", "事業所_認定年月日": "20281201", "事業所_施設カナ名称": "ﾏﾏﾐﾐﾞﾝﾝ", "事業所_施設名称": "テスト用施設", "事業所_郵便番号（前３桁）": "100", "事業所_郵便番号（後４桁）": "0000", "事業所_住所": "東京都千代田区", "事業所_電話番号": "03-1111-2222", "事業所_施設種類": "認定こども園－幼保連携型", "事業所_施設所在地（県）": "北海道", "事業所_施設所在地（市区町村）": "札幌市", "事業所_事業開始年月日": "20281201", "事業所_認可定員": "9999", "事業所_１号認定": "88", "事業所_２号認定": "77", "事業所_３号認定": "66", "事業所_地域枠": "733", "事業所_適用有無": "無し", "事業所_みなし確認適用の有無": "有り"}, "batch_click_count": 3600, "batch_wait_seconds": 20, "importDataFolder": ".\\test\\qap010_std\\testdata\\importData\\", "TestQAP010_28030101": {"sql_params": {"業務C": "QAP010", "選考日_AtenaCD_Yes": "20240401", "選考日_AtenaCD_No": "20240401", "宛名コード_Yes": "101200000101242", "宛名コード_No": "101200000102127"}, "AtenaCD_Yes": "101200000101242", "AtenaCD_No": "101200000102127", "SenkoYMD": "20240401", "KetteiYMD": "20240402", "NyushoYMD": "20240601", "SenkoKekka_Yes": "承諾", "SenkoKekka_No": "不承諾", "KetteiShisetsu": "（保）Ｄ５１テスト事業所１３あ"}, "TestQAP010_28030102": {"GyomuNM": "子ども子育て支援", "SubSystemNM": "入所", "ShoriNM_1": "入退所異動処理", "ShoriNM_2": "入所不承諾通知書　出力", "NyushoYMD_From": "20200401", "NyushoYMD_To": "20241231", "TaisyouYM": "202404", "再発行区分": "有"}, "TestQAP010_28040101": {"sql_params": {"自治体C": "99101", "業務C": "QAP010", "児童_宛名C_1": "101200000100063", "児童_宛名C_2": "101200000100027", "児童_宛名C_取下": "101200000100035", "児童_宛名C_処分延期": "101200000100112", "児童_宛名C_代理": "101200000100068"}, "スケジュール_業務名_1": "子ども子育て支援", "スケジュール_サブシステム名_1": "支給認定", "スケジュール_処理名_1": "支給認定証_通知書_一覧出力処理", "再発行区分": "有", "納品物_業務名_1": "子ども子育て支援", "納品物_サブシステム名_1": "支給認定", "納品物_処理名_1": "支給認定証_通知書_一覧出力処理", "スケジュール_業務名_2": "子ども子育て支援", "スケジュール_サブシステム名_2": "支給認定", "スケジュール_処理名_2": "支給認定証取下(却下）通知書_一覧出力処理", "納品物_業務名_2": "子ども子育て支援", "納品物_サブシステム名_2": "支給認定", "納品物_処理名_2": "支給認定証取下(却下）通知書_一覧出力処理", "スケジュール_業務名_3": "子ども子育て支援", "スケジュール_サブシステム名_3": "支給認定", "スケジュール_処理名_3": "支給認定証_交付通知書_一覧(代理申請)出力処理", "納品物_業務名_3": "子ども子育て支援", "納品物_サブシステム名_3": "支給認定", "納品物_処理名_3": "支給認定証_交付通知書_一覧(代理申請)出力処理", "スケジュール_業務名_4": "子ども子育て支援", "スケジュール_サブシステム名_4": "支給認定", "スケジュール_処理名_4": "支給認定証_交付延期通知書_一覧出力処理", "納品物_業務名_4": "子ども子育て支援", "納品物_サブシステム名_4": "支給認定", "納品物_処理名_4": "支給認定証_交付延期通知書_一覧出力処理"}, "TestQAP010_28040102": {"sql_params": {"自治体C": "99101", "業務C": "QAP010", "児童_宛名C": "101200000102524"}, "所管区": "テスト行政区", "代理申請施設": "02241001", "認定・却下決定日": "20250520", "認定結果": "承認", "支給認定区分": "１号", "認定期間（開始）": "20240520", "認定期間（終了）": "20240520", "保育の必要性(事由)": "就労", "時間区分(保育必要量)": "教育標準時間", "保育の必要性(続柄)": "父"}, "TestQAP010_28040103": {"スケジュール_業務名_1": "子ども子育て支援", "スケジュール_サブシステム名_1": "支給認定", "スケジュール_処理名_1": "支給認定証_通知書_一覧出力処理", "納品物_業務名_1": "子ども子育て支援", "納品物_サブシステム名_1": "支給認定", "納品物_処理名_1": "支給認定証_通知書_一覧出力処理", "スケジュール_業務名_2": "子ども子育て支援", "スケジュール_サブシステム名_2": "支給認定", "スケジュール_処理名_2": "支給認定証_交付通知書_一覧(代理申請)出力処理", "納品物_業務名_2": "子ども子育て支援", "納品物_サブシステム名_2": "支給認定", "納品物_処理名_2": "支給認定証_交付通知書_一覧(代理申請)出力処理", "児童_宛名C": "101200000102524"}, "TestQAP010_28030201": {"sql_params": {"業務C": "QAP010", "児童_宛名C_退所": "101200000101234", "児童_宛名C_一括転園": "101200000100625", "児童_宛名C_一括退所": "101200000100009"}, "AtenaCD": "101200000101234", "ShinseiYMD": "20240501", "KetteiYMD": "20240501", "TaishoYMD": "20240630", "GyomuNM": "子ども子育て支援", "SubSystemNM": "入所", "ShoriNM_1": "一括退所処理", "ShoriNM_2": "卒園処理", "ShoriNM_3": "一括転園処理", "基準日": "20240501", "抽出区分": "入所希望期間終了", "対象年度": "2024", "旧施設コード": "1", "新施設コード": "100001", "申請年月日": "20240301", "決定年月日": "20240301", "退所年月日": "20240302", "AtenaCD_一括退所": "101200000100009", "AtenaCD_卒園": "110100000101602", "AtenaCD_一括転園": "101200000100625"}, "TestQAP010_28030202": {"GyomuNM": "子ども子育て支援", "SubSystemNM": "入所", "ShoriNM": "入退所異動処理", "対象年月": "202403", "再発行区分": "有", "出力対象": "退所"}, "TestQAP010_28030301": {"sql_params": {"自治体C": "99101", "業務C": "QAP010", "児童_宛名C": "101200000100595", "保護者1_変更前": "101200000100593", "保護者2_変更前": "101200000100592", "納付義務者1_変更前": "101200000100593", "納付義務者2_変更前": "101200000100592", "一括抽出_児童_宛名C": "101200000102552", "契約新規_児童_宛名C": "101200000100595", "退所_児童_宛名C": "101200000102299", "３号⇒２号_児童_宛名C": "101200000102553"}, "AtenaCD": "101200000100595", "GyomuNM": "子ども子育て支援", "SubSystemNM": "請求審査", "ShoriNM": "契約情報一括作成処理", "保護者１氏名_変更後": "101200000100592:ﾌｸｼ ﾅﾅﾛｳ", "保護者２氏名_変更後": "", "納義務者１氏名_変更後": "101200000100592:ﾌｸｼ ﾅﾅﾛｳ", "納義務者２氏名_変更後": "", "年度": "令和06年", "認定開始年月": "202406", "合算区分": "要", "入力区分": "連携", "契約期間開始": "20240401", "契約期間終了": "20260331", "認定区分": "２号", "保育必要量": "保育標準時間", "一括抽出_対象年月日開始": "20220601", "一括抽出_対象年月日終了": "20240601", "一括抽出_対象生年月日開始日": "20200531", "一括抽出_対象生年月日終了日": "20240601", "契約新規_対象年月日開始": "20220604", "契約新規_対象年月日終了": "20240604", "契約新規_対象生年月日開始日": "20200201", "契約新規_対象生年月日終了日": "20240604", "退所_対象年月日開始": "20170101", "退所_対象年月日終了": "20241231", "退所_対象生年月日開始日": "20170101", "退所_対象生年月日終了日": "20241231", "３号⇒２号_対象年月日開始": "20170101", "３号⇒２号_対象年月日終了": "20241231", "３号⇒２号_対象生年月日開始日": "20170101", "３号⇒２号_対象生年月日終了日": "20241231", "支給認定差異_対象年月日開始": "20170101", "支給認定差異_対象年月日終了": "20241231", "支給認定差異_対象生年月日開始日": "20170101", "支給認定差異_対象生年月日終了日": "20241231", "契約新規更新": "更新あり", "退所更新": "更新あり", "３号⇒２号更新": "更新あり", "支給認定差異更新": "更新あり", "宛名コード_契約新規": "101200000100595", "宛名コード_退所": "101200000102299", "宛名コード_３号⇒２号": "101200000102553", "宛名コード_支給認定差異": "101200000100625"}, "TestQAP010_28030302": {"sql_params": {"自治体C": "99101", "業務C": "QAP010", "履歴番号": "79855", "対象年度": "2024", "児童_宛名C": "101200000100625"}, "AtenaCD": "101200000100625", "GyomuNM": "子ども子育て支援", "SubSystemNM": "賦課", "ShoriNM_1": "月次賦課処理", "ShoriNM_2": "年次賦課処理", "認定年月開始": "202406", "認定年月終了": "202503", "減免・調整種別": "徴収金額指定減免", "金額指定": "1000", "科目": "延長", "対象年月": "202406", "対象年度_月次": "2024", "基準年月": "202406", "対象年度_年次": "2024"}, "TestQAP010_28030303": {"sql_params": {"対象年度": "2024", "児童_宛名C": "101200000102677"}, "GyomuNM": "子ども子育て支援", "SubSystemNM_1": "入所", "SubSystemNM_2": "賦課", "ShoriNM_1": "保育料決定通知書_一覧出力処理", "ShoriNM_2": "保育所徴収金減免決定(却下)通知書出力処理", "ShoriNM_3": "月次賦課処理", "ShoriNM_4": "保育料無償のお知らせ出力", "対象児童_保育料決定": "現在入所中の児童", "対象施設コード": "0325", "対象年月_保育料決定": "202408", "再発行区分": "有", "対象年月_徴収金減免": "202408", "対象年月_月次賦課": "202408", "対象児童_保育料無償": "現在入所中の児童", "対象年月_保育料無償": "202408"}, "TestQAP010_28030401": {"GyomuNM": "子ども子育て支援", "SubSystemNM": "入所", "ShoriNM": "入所者名簿出力処理", "基準日_開始": "20240101", "基準日_終了": "20241231"}, "TestQAP010_28030402": {"sql_params": {"業務C": "QAP010", "児童_宛名C_1": "101200000102347", "児童_宛名C_2": "101200000102348", "児童_宛名C_3": "101200000102398", "児童_宛名C_4": "101200000102399", "年度_1": "2024", "年度_2": "2023"}, "AtenaCD": "101200000102348", "GyomuNM": "子ども子育て支援", "SubSystemNM": "共通", "ShoriNM_1": "税異動リスト出力処理", "ShoriNM_2": "税異動確認処理", "ShoriNM_3": "税一括更新　世帯課税認定一括追加", "ShoriNM_4": "税一括更新　税額変更反映", "年度": "令和06年", "申請年月日": "20240901", "決定年月日": "20240901", "認定開始年月": "202409", "入力区分": "連携", "処理コメント": "テスト", "対象期間開始": "20240401", "対象期間終了": "20250331", "開始連番": "抽出:20170913/2回目:支所確認", "終了連番": "抽出:20170913/2回目:支所確認", "対象年月_text_1": "202409", "対象年月_text_2": "202504", "対象年月_text_3": "202609", "対象年月_text_4": "202709", "対象年月_text_5": "202404", "対象年月_text_6": "202309"}, "TestQAP010_28030403": {"sql_params": {"業務C": "QAP010", "対象年度": "2024", "児童_宛名C": "101200000102348"}, "AtenaCD": "101200000102348", "GyomuNM": "子ども子育て支援", "SubSystemNM": "賦課", "ShoriNM": "月次賦課処理", "認定年月開始": "202409", "認定年月終了": "202503", "減免・調整種別": "徴収金額指定減免", "金額指定": "1000", "科目": "延長", "対象年月": "202405", "対象年度": "2024", "基準年月": "202405"}, "TestQAP010_28030404": {"GyomuNM": "子ども子育て支援", "SubSystemNM": "入所", "ShoriNM": "保育所徴収金減免決定(却下)通知書出力処理", "対象年月": "202406", "再発行区分": "有"}, "TestQAP010_28090202": {"sql_params": {"自治体C": "99101", "業務C": "QAP010", "住民コード": "102200000102675", "保護者1_宛名C": "800100000217171", "保護者2_宛名C": "800100000217172", "保護者認定_認定年月": "202802", "新２号児童_宛名C": "101200000102495"}, "保護者認定_申請年月日": "20240405", "保護者認定_決定年月日": "20240405", "変更理由": "その他による", "支給認定_申請年月日": "20240405", "支給認定_受付年月日": "20240405", "支給認定_利用希望期間開始": "20240405", "支給認定_申請種別": "職権修正", "支給認定_申請事由": "年次募集", "支給認定_所管区": "テスト行政区", "支給認定_認定・却下年月日": "20240405", "支給認定_認定結果": "承認", "支給認定_支給認定区分": "新２号", "支給認定_保育の必要性（事由）": "疾病・障がい", "保育の必要性（続柄）": "母", "利用者申請_申請年月日": "20280101", "利用者申請_事由発生年月日": "20280101", "利用者申請_認定開始": "20280101", "利用者申請_認定終了": "20280101", "利用者申請_決定年月日": "20280101", "利用者申請_申請理由": "年齢到達", "利用者申請_決定結果": "決定"}, "TestQAP010_28070101": {"sql_params": {"自治体C": "99101", "業務C": "QAP010", "対象年度": "2024", "児童_宛名C1": "101200000102299", "児童_宛名C2": "101200000101212", "児童_宛名C3": "101200000102642", "保育必要量C": "2", "状態区分C": "1", "変更前状態区分C": "", "取下日": "00000000", "取下理由C": "", "取下更新区分C": ""}, "業務名": "子ども子育て支援", "サブシステム名": "賦課", "処理名": "月次賦課処理", "所管区": "", "支所": "", "開始年月": "202004", "終了年月": "202205", "チェック項目設定": "全項目", "並び順": "世帯台帳番号、児童_宛名C、対象年月、影響する科目", "対象年月": "202406", "対象年度": "2024", "基準年月": "202406"}, "TestQAP010_28070102": {"業務名": "子ども子育て支援", "サブシステム名": "賦課", "処理名": "月次賦課処理", "調定者一覧_データ種別": "賦課対象", "調定者一覧_科目": "", "調定者一覧_所管区": "", "調定者一覧_支所": "", "調定者一覧_収納方法": "", "調定者一覧_出力対象": "全件", "調定者一覧_対象年月開始": "20240401", "調定者一覧_対象年月終了": "20240501", "調定額集計表_データ種別": "賦課対象", "調定額集計表_科目": "", "調定額集計表_所管区": "", "調定額集計表_支所": "", "調定額集計表_施設種類": "", "調定額集計表_出力対象": "", "調定額集計表_対象年度": "2024", "調定額集計表（総括）_データ種別": "賦課対象", "調定額集計表（総括）_科目": "", "調定額集計表（総括）_所管区": "", "調定額集計表（総括）_支所": "", "調定額集計表（総括）_施設種類": "", "調定額集計表（総括）_出力対象": "", "調定額集計表（総括）_対象年度": "2024"}, "TestQAP010_28070103": {"児童宛名C": "101200000101212", "取下年月日": "20241231", "更新区分": "取下げ登録・変更", "理由": "その他", "業務名": "子ども子育て支援", "サブシステム名": "賦課", "処理名": "月次賦課処理", "対象年月": "202405", "対象年度": "2024", "賦課_対象年度": "令和03年", "基準年月": "202405"}, "TestQAP010_28070104": {"児童宛名C": "101200000102642", "保育必要量": "保育標準時間", "業務名": "子ども子育て支援", "サブシステム名": "賦課", "処理名": "月次賦課処理", "対象年月": "202405", "対象年度": "2024", "基準年月": "202405", "賦課年度": "令和06年"}, "TestQAP010_28070105": {"sql_params": {"対象年度": "2024"}, "業務名": "子ども子育て支援", "サブシステム名": "賦課", "処理名": "月次賦課処理", "対象年月": "202405", "チェック前回納付書発行用情報": "削除しない"}, "TestQAP010_28030501": {"宛名コード": "101200000102524", "処理年度": "令和06年", "賦課年度": "令和06年", "科目": "副食費2号"}, "TestQAP010_28030502": {"業務名_1": "子ども子育て支援", "サブシステム名_1": "賦課", "処理名_1": "月次賦課処理", "賦課_対象年月": "202403", "賦課_対象年度": "2024", "賦課_基準年月": "202403", "業務名_2": "子ども子育て支援", "サブシステム名_2": "入所", "処理名_2": "食材料費徴収者確認リスト出力", "食材料費徴収者_対象年月開始": "202405", "食材料費徴収者_対象年月終了": "202405", "食材料費徴収者_出力対象": "副食費", "食材料費徴収者_出力対象者": "全員", "業務名_3": "子ども子育て支援", "サブシステム名_3": "入所", "処理名_3": "食材料費減免対象者リスト出力", "食材料費減免対象者_対象年月開始": "202405", "食材料費減免対象者_対象年月終了": "202405", "食材料費減免対象者_対象施設": "徴収対象", "食材料費減免対象者_出力対象": "副食費"}, "TestQAP010_28030503": {"sql_params": {"業務C": "QAP010"}, "業務名_1": "子ども子育て支援", "サブシステム名_1": "入所", "処理名_1": "食材料費徴収免除(取消)通知書出力_新", "食材料費徴収免除(取消)_対象年月": "202405", "食材料費徴収免除(取消)_出力帳票_1": "免除", "食材料費徴収免除(取消)_出力帳票_2": "取消", "食材料費徴収免除(取消)_児童宛名コード": "101200000102652", "再発行区分": "有"}, "TestQAP010_28130101": {"sql_params": {"自治体C": "99101", "業務C": "QAP010", "児童_宛名C": "101200000102524"}, "年度": "令和03年", "サービス区分": "延長", "申請年月日": "20240529", "申請種別": "新規", "決定年月日": "20240529", "結果": "承諾", "認定年月(開始)": "20210501", "認定年月(終了)": "20210501", "サービス種類": "1時間当たり", "希望実施期間(開始)": "20210501", "希望実施期間(終了)": "20210501", "補足情報１": "補足情報１", "補足情報２": "補足情報２", "補足情報３": "補足情報３", "補足情報４": "補足情報４"}, "TestQAP010_28130102": {"業務名": "子ども子育て支援", "サブシステム名": "入所", "処理名": "延長保育利用承諾通知書_一覧出力処理", "対象年度": "2024", "対象年月開始": "20240301", "対象年月終了": "20241201", "再発行区分": "有"}, "TestQAP010_28130103": {"児童_宛名C": "101200000102524", "処理年度": "令和06年", "サービス区分": "延長", "業務名": "子ども子育て支援", "サブシステム名": "入所", "処理名": "特別保育実績入力CSV取込", "出力_サービス区分": "延長", "出力_実績年月": "20240501", "取込_サービス区分": "延長", "取込_実績年月": "20200301", "取込_児童チェック": "支給認定証番号＋カナ氏名＋生年月日", "対象ファイル": "99101_00000_QAP010_QP2BR09800_202_1_QP2BN09850_1_特別保育実績取込.csv"}, "TestQAP010_28130104": {"業務名": "子ども子育て支援", "サブシステム名": "賦課", "処理名": "月次賦課処理", "整合性チェック_所管区": "", "整合性チェック_支所": "", "整合性チェック_開始年月": "20200401", "整合性チェック_終了年月": "20200501", "整合性チェック_チェック項目設定": "全項目", "整合性チェック_並び順": "世帯台帳番号、児童_宛名C、対象年月、影響する科目", "月次賦課計算_対象年月": "20240501", "月次賦課計算_対象年度": "20240501", "月次賦課計算_基準年月": "20240501", "宛名コード": "101200000101212", "賦課年度": "令和06年", "賦課年月": "20240501"}, "TestQAP010_28130105": {"業務名_1": "子ども子育て支援", "サブシステム名_1": "入所", "処理名_1": "延長保育料決定通知書_一覧出力処理", "一覧出力_サービス区分": "延長", "一覧出力_対象年度": "2024", "一覧出力_対象年月開始": "20240401", "一覧出力_対象年月終了": "20240601", "一覧出力_発行年月日": "20240530", "施設コード": "201901", "児童宛名コード": "101200000101821", "業務名_2": "子ども子育て支援", "サブシステム名_2": "入所", "処理名_2": "延長保育料決定（変更）通知", "延長保育料決定（変更）_前回実施日": "20240401", "延長保育料決定（変更）_基準日": "20240601", "再発行区分": "有", "延長保育料決定（変更）_文書番号": "000123"}, "TestQAP010_28130106": {"sql_params": {"児童_宛名C": "101200000102524", "自治体C": "99101", "業務C": "QAP010", "サービス区分C": "2", "対象年度": "2021", "種類C": "3"}, "児童_宛名C": "101200000102524", "年度": "令和03年", "サービス区分": "延長", "申請年月日": "20240529", "申請種別": "変更", "追加_申請種別": "実施解除", "決定年月日": "20240529", "結果": "承諾", "認定年月(開始)": "20210501", "認定年月(終了)": "20210501", "サービス種類": "種類③", "希望実施期間(開始)": "20210501", "希望実施期間(終了)": "20210501", "補足情報１": "補足情報１", "補足情報２": "補足情報２", "補足情報３": "補足情報３", "補足情報４": "補足情報４", "賦課年度": "令和06年", "賦課年月": "202405"}, "TestQAP010_28130107": {"業務名": "子ども子育て支援", "サブシステム名": "入所", "処理名": "延長保育実施解除通知書_一覧出力処理", "サービス区分": "延長", "対象年度": "2024", "対象年月開始": "202404", "対象年月終了": "202503", "発行年月日": "20240630", "再発行区分": "有"}, "TestQAP010_28120101": {"業務名": "子ども子育て支援", "サブシステム名": "認可外申請管理", "処理名": "施設等利用費支弁基礎資料作成処理", "利用年月開始": "20210501", "利用年月終了": "20210601", "対象月数": "3"}, "TestQAP010_28110101": {"sql_params": {"児童_宛名C": "101200000102106"}, "児童_宛名C": "101200000102106", "Ketteinengappi": "20240531", "ShisetuCd2_1_1": "20190206", "RiyouKaishibi_1_4": "20240530", "RiyouShuuryoubi": "20250530", "ShinseiYMD": "20240530", "UketsukeYMD": "20240530", "NinteiKetteiDay": "20240530", "NinteiKikanStart": "20240601", "GyomuNM": "子ども子育て支援", "SubSystemNM": "認可外支払管理", "ShoriNM": "補助金申請一括取込", "補助金申請ファイル": "99101_00000_QAP010_QPNBR00600_104_1_QPNBN01000_1_補助金申請対象者一覧.csv"}, "TestQAP010_28100101": {"sql_params": {"自治体C": "99101", "業務C": "QAP010", "施設C": "281001020"}, "case_qap010_txtTaishoKikanStartYM": "令和06年5月", "case_qap010_selIdoJiyu": "新規", "case_qap010_lblShisetsuCD": "281001020", "case_qap010_txtJigyoStartYMD": "20240501", "case_qap010_txtShisetsuKanaNM": "ﾜﾗﾔﾏﾊ", "case_qap010_txtShisetsuNM": "テスト施設事業所", "case_qap010_txtShisetsuPostNoOya": "123", "case_qap010_txtShisetsuPostNoKo": "0001", "case_qap010_txtShisetsuJusho": "福祉県福祉市テスト１丁目あい", "case_qap010_txtShisetsuTEL": "12345678901", "case_qap010_selShisetsuShurui": "未移行幼稚園", "case_qap010_selShisetsuShozaichiTodofuken": "北海道", "case_qap010_selShisetsuShozaichiShikutyoson": "札幌市", "case_qap010_txtHojinKanaNM": "ﾜﾗﾔﾏﾊ", "case_qap010_txtHojinNM": "法人テスト", "case_qap010_selHojinShubetsu": "社会福祉法人", "case_qap010_txtHojinPostNoOya": "123", "case_qap010_txtHojinPostNoKo": "0002", "case_qap010_txtHojinJusho": "福祉県福祉市テスト", "case_qap010_txtHojinTEL": "12345678901", "case_qap010_txtSetsuritsuYMD": "20240513", "case_qap010_selHojinShokatsuchoTodofuken": "北海道", "case_qap010_selHojinShokatsuchoShikuchoson": "札幌市", "case_qap010_txtDaihyoshaKanaShimei": "ﾜﾗﾔﾏﾊ", "case_qap010_txtDaihyoshaShimei": "テスト", "case_qap010_txtDaihyoshaShokuNM": "テスト職名", "case_qap010_selKeieiShutai": "公立（市立）", "case_qap010_JigyoshoShokanku": "テスト一区"}, "TestQAP010_28090401": {"業務名_1": "子ども子育て支援", "サブシステム名_1": "入所", "処理名_1": "現況処理", "現況届提出対象者_発行年月日": "20240528", "現況届提出対象者_基準日": "20230501", "現況届提出対象者_出力区分": "入所申込", "現況届提出対象者_指定年月日": "20230501", "現況届提出対象者_並び順": "児童_生年月日（降順）,児童_氏名,児童_宛名C", "現況届提出対象者_再発行区分": "有", "保育所入所_発行年月日": "20240528", "業務名_2": "子ども子育て支援", "サブシステム名_2": "入所", "処理名_2": "現況処理(世帯別)", "世帯別_発行年月日": "20240528", "世帯別_基準日": "20230501", "世帯別_出力区分": "入所申込", "世帯別_指定年月日": "20230501", "世帯別_並び順": "世帯台帳番号,児童_生年月日（降順）,児童_宛名C", "世帯別_再発行区分": "有", "(世帯別)保育所入所_発行年月日": "20240528", "業務名_3": "子ども子育て支援", "サブシステム名_3": "入所", "処理名_3": "入所継続届出力処理", "入所継続対象者抽出_基準日": "20220301", "入所継続対象者抽出_世帯台帳番号": "", "入所継続対象者抽出_施設種類": "", "入所継続対象者抽出_施設コード": "", "入所継続対象者抽出_発行年月日": "20240528", "入所継続対象者抽出_受託区分": "入所継続届出力処理", "入所継続対象者抽出_再発行区分": "有", "入所継続対象者抽出_並び順": "生年月日, 児童宛名C", "入所継続届出力_発行年月日": "20240528", "消込区分": "バーコード", "提出年月日": "20240131", "現況種類": "", "バーコード": "0000045725", "業務名_4": "子ども子育て支援", "サブシステム名_4": "入所", "処理名_4": "現況届未提出者督促状_一覧出力処理", "現況届未提出者一覧出力_所管区": "", "現況届未提出者一覧出力_出力区分": "支給認定分", "現況届未提出者一覧出力_対象年度": "2024", "現況届未提出者一覧出力_提出期限": "20240528", "現況届未提出者一覧出力_並び順": "生年月日(降順)、児童カナ、児童住民C", "現況届未提出者一覧出力_郵便区内特別有無": "無", "現況届未提出者督促状_一覧出力処理_所管区": "", "現況届未提出者督促状_一覧出力処理_発行年月日": "20240528", "現況届未提出者督促状_一覧出力処理_出力区分": "支給認定分"}, "TestQAP010_28090403": {"宛名コード": "101200000101212", "状態区分": "保留"}, "TestQAP010_28110102": {"sql_params": {"児童_宛名C": "101200000101215", "口座番号": "0123456"}, "AtenaCD": "101200000101215", "KinyuuKikanMei": "0001", "ShitenCode": "001"}, "TestQAP010_28110103": {"sql_params": {"業務C": "QAP010", "児童_宛名C1": "101200000101822", "児童_宛名C2": "101200000100589"}, "GyomuNM": "子ども子育て支援", "SubSystemNM": "認可外支払管理", "ShoriNM_1": "補助金一括算定処理", "ShoriNM_2": "補助金支払通知書出力処理", "利用年月開始": "202401", "再処理フラグ": "再処理"}, "TestQAP010_28110104": {"GyomuNM": "子ども子育て支援", "SubSystemNM": "認可外支払管理", "ShoriNM": "補助金支払振込データ作成"}, "TestQAP010_28110105": {"sql_params": {"業務C": "QAP010", "児童_宛名C": "101200000100589", "利用年月": "202405", "納付額": "2000000"}, "AtenaCD": "101200000100589", "GyomuNM": "子ども子育て支援", "SubSystemNM": "認可外支払管理", "ShoriNM": "補助金支払締め・解除処理", "利用年月": "202405", "納付額": "2000000", "利用施設": "000000003　Ｄ５１テスト事業所１３あ　預かり保育", "支払方法": "償還（利用者）", "利用日数": "30"}, "TestQAP010_28110106": {"sql_params": {"業務C": "QAP010", "児童_宛名C_代理": "101200000101822"}, "GyomuNM": "子ども子育て支援", "SubSystemNM": "認可外支払管理", "ShoriNM_1": "補助金支払通知書出力処理", "ShoriNM_2": "補助金支払却下通知書出力処理", "再発行区分": "有"}, "TestQAP010_28090103": {"sql_params": {"自治体C": "99101", "業務C": "QAP010", "児童_宛名C_1": "800100000001350", "児童_宛名C_2": "101200000101744"}, "認定・却下年月日": "20240531", "認定結果": "承認", "支給認定区分": "新３号", "保育の必要性（事由）": "疾病・障がい", "保育の必要性（続柄）": "母", "認定期間（開始）": "20240520", "認定期間（終了）": "20250331", "決定年月日": "20240901", "決定結果": "決定", "スケジュール_業務名_1": "子ども子育て支援", "スケジュール_サブシステム名_1": "認可外申請管理", "スケジュール_処理名_1": "認可外申請データ取込処理", "納品物_業務名_1": "子ども子育て支援", "納品物_サブシステム名_1": "認可外申請管理", "納品物_処理名_1": "認可外申請データ取込処理", "ワーニングデータ登録区分": "登録する", "決定結果更新区分": "決定で作成する", "並び順": "取込年月日、生年月日(降順)、児童カナ氏名、児童宛名", "対象ファイル": "28090103認可外申請.csv"}, "TestQAP010_28090104": {"業務名_1": "子ども子育て支援", "サブシステム名_1": "認可外申請管理", "処理名_1": "施設等利用給付認定通知書", "施設等利用給付決定開始日": "20200101", "支給認定番号": "", "発行年月日": "20250101", "児童宛名コード": "", "認定区分": "", "代理施設有無": "", "並び順": "認定区分, 児童_生年月日（降順）, 児童_氏名カナ", "再発行区分": "有", "郵便区内特別有無": "無", "業務名_2": "子ども子育て支援", "サブシステム名_2": "認可外申請管理", "処理名_2": "施設等利用給付認定申請却下通知書", "却下_施設等利用給付決定開始日": "20190101", "却下_支給認定番号": "", "却下_発行年月日": "20250101", "却下_児童宛名コード": "", "却下_認定区分": "", "却下_代理施設有無": "", "却下_並び順": "認定区分, 児童_生年月日（降順）, 児童_氏名カナ", "却下_再発行区分": "有", "却下_郵便区内特別有無": "無", "業務名_3": "子ども子育て支援", "サブシステム名_3": "認可外申請管理", "処理名_3": "（みなし認定）施設等利用給付認定通知書", "みなし認定_施設等利用給付決定開始日": "20200101", "みなし認定_支給認定番号": "", "みなし認定_発行年月日": "20250101", "みなし認定_児童宛名コード": "", "みなし認定_認定区分": "", "みなし認定_代理施設有無": "", "みなし認定_並び順": "認定区分, 児童_生年月日（降順）, 児童_氏名カナ", "みなし認定_再発行区分": "有", "みなし認定_郵便区内特別有無": "無"}, "TestQAP010_28140101": {"sql_params": {"業務C": "QAP010", "利用年月": "202406", "児童_宛名C": "101200000101665", "児童_宛名C_アプロード用": "110100000100263", "口座番号": "1110012"}, "AtenaCD": "101200000101665", "GyomuNM": "子ども子育て支援", "SubSystemNM": "認可外副食費", "ShoriNM_1": "補足給付申請対象者データ出力", "ShoriNM_2": "補足給付申請対象者データ取込", "保育必要性": "なし", "決定結果": "決定", "所管区": "テスト一区", "施設コード": "11001", "利用区分": "幼稚園未移行", "支払方法": "代理（施設）", "利用施設": "11001　未移行幼稚園１２３　幼稚園未移行", "納付額": "2000000", "利用日数": "30", "業務名_口座": "子ども", "用途_口座": "認可外", "銀行コード": "0001", "支店コード": "002", "口座番号": "1110012", "有効期間開始日": "20240526", "申請年月日": "20240526", "申請理由": "新規", "認定期間開始": "202406", "認定期間終了": "202412", "利用施設_補足給付副食費": "11001", "ファイルパス": "補足給付副食費申請対象者一覧.csv", "更新区分": "更新する"}, "TestQAP010_28140102": {"sql_params": {"業務C": "QAP010", "利用年月": "202405", "児童_宛名C_代理": "101200000101475", "児童_宛名C_償還": "101200000100771"}, "AtenaCD": "101200000101475", "GyomuNM": "子ども子育て支援", "SubSystemNM_1": "認可外副食費", "SubSystemNM_2": "補足給付支払処理", "ShoriNM_1": "副食費軽減リスト出力処理", "ShoriNM_2": "補足給付一括算定処理", "ShoriNM_3": "補足給付支払通知書出力処理", "ShoriNM_4": "補足給付所得判定結果反映", "ShoriNM_5": "補足給付支払申請一括取込", "ShoriNM_6": "補足給付免除（取消）通知書", "ShoriNM_7": "補足給付承諾（不承諾）通知書", "対象年月開始": "202406", "対象年月終了": "202406", "再発行区分": "有", "再処理フラグ": "再処理", "利用年月開始": "202405", "利用年月終了": "202407", "対象年月": "202407", "ファイルパス": "補足給付支払取込対象者一覧.csv"}, "TestQAP010_28140103": {"sql_params": {"業務C": "QAP010", "児童_宛名C": "101200000101665"}, "AtenaCD": "101200000101665", "GyomuNM": "子ども子育て支援", "SubSystemNM": "補足給付支払処理", "ShoriNM_1": "補足給付支払振込データ作成", "ShoriNM_2": "補足給付支払締め・解除処理", "ShoriNM_3": "補足給付額集計表出力", "保育必要性": "なし", "利用年月開始": "202405", "利用年月終了": "202503"}, "TestQAP010_28090302": {"住民コード": "101200000101744", "取消年月日": "20290901", "取消理由": "その他事由", "申請年月日": "20240901", "事由発生年月日": "20240901", "認定終了": "20250331", "決定年月日": "20290901", "申請理由": "世帯課税区分変更", "決定結果": "取下", "取下年月日": "20240901", "業務名": "子ども子育て支援", "サブシステム名": "認可外申請管理", "処理名": "施設等利用給付認定取消通知書", "施設等利用給付取消開始日": "20240527", "施設等利用給付取消終了日": "20290901", "支給認定番号": "", "発行年月日": "20290901", "児童宛名コード": "", "認定区分": "", "代理施設有無": "", "並び順": "認定区分, 児童_生年月日（降順）, 児童_氏名カナ", "再発行区分": "有", "郵便区内特別有無": "無"}, "test_qap010_28070201": {"qap010_28070201_txtJuminCD": "110100000101682", "qap010_28070201_txtGinkoCD": "0005", "qap010_28070201_txtShitenCD": "001", "qap010_28070201_selYokinKamoku": "普通", "qap010_28070201_txtKozaNo": "1234567", "qap010_28070201_txtKozaMeigiKanaShimei": "ｺｳｻﾞﾃｽﾄ", "qap010_28070201_txtYukoKikanFrom": "R06.06.01", "qap010_28070201_txtJidoKensakuAtenaCD": "110100000101682", "qap010_28070201_txtGinkoCD2": "0006", "qap010_28070201_txtShitenCD2": "001", "qap010_28070201_selYokinKamoku2": "普通", "qap010_28070201_txtKozaNo2": "2345678", "qap010_28070201_txtKozaMeigiKanaShimei2": "ｺｳｻﾞﾃｽﾄ2", "qap010_28070201_txtYukoKikanFrom2": "R06.06.02", "qap010_28070201_kamoku": "tab01_ZEAF002200_JAABN03000_GyomuCDchk21", "qap010_28070201_KouzakaishiNendoEN": "令和06年6月1日", "qap010_28070201_KouzakaishiNendoST": "令和07年6月1日", "qap010_28070201_Noukigen": "令和06年6月30日", "qap010_28070201_Hassoubi": "令和06年6月13日"}, "test_qap010_28070301": {"qap010_28070301_GyomuCD1": "子ども", "qap010_28070301_KozaGyomukijunBi1": "2023/06/30", "qap010_28070301_kamoku": "tab01_ZEAF002200_gyomuCodechk21", "qap010_28070301_cyouteuhasseiNenndouKaishi": "令和05年", "qap010_28070301_cyouteuhasseiNenndouSyuuryou": "令和05年", "qap010_28070301_kazeikonnkyoNenndouKaishi": "令和05年", "qap010_28070301_kazeikonnkyoNenndouuSyuuryou": "令和05年", "qap010_28070301_noukigennKaishi": "令和05年6月1日", "qap010_28070301_noukigennShuryo": "令和06年6月1日", "qap010_28070301_hassouBi": "令和06年6月13日", "qap010_28070301_reigatuKb": "全件（口振対象を除く）", "qap010_28070301_txtJuminCD": "101200000100622", "qap010_28070301_txtCVSShiyoKigen": "令和07年6月1日"}, "test_qap010_28070702": {"qap010_28070702_groupingKBN": "保育", "qap010_28070702_JABBN00300kanpuTuutiBi": "令和05年6月1日", "qap010_28070702_JABBN00300shishutuKetteiBi": "令和05年6月1日", "qap010_28070702_groupingKBN2": "保育", "qap010_28070702_selKamoku": "保育", "qap010_28070702_selChoteiHasseiNendoji": "令和05年度", "qap010_28070702_selChoteiHasseiNendoitaru": "令和06年度", "qap010_28070702_txtTsuchishoNoji": "000032801001", "qap010_28070702_txtTsuchishoNoitaru": "000032801001", "qap010_28070702_selShinkiKampuNendo": "令和06年度", "qap010_28070702_txtKampuTsuchibi": "令和06年6月1日", "qap010_28070702_txtShishutsuKetteibi": "令和06年6月1日", "qap010_28070702_txtKampuSeikyubi": "令和06年6月1日", "qap010_28070702_txtJutoMeisaiJutoShoribi": "R06.06.04", "qap010_28070702_txtJutoMeisaiFukagakuJutoGaku": "2000", "qap010_28070702_groupingKBN3": "保育", "qap010_28070702_selKamoku2": "保育", "qap010_28070702_selChoteiHasseiNendoji2": "令和05年度", "qap010_28070702_selChoteiHasseiNendoitaru2": "令和06年度", "qap010_28070702_txtTsuchishoNoji2": "000032801001", "qap010_28070702_txtTsuchishoNoitaru2": "000032801001", "qap010_28070702_selShinkiKampuNendo2": "令和06年度", "qap010_28070702_txtKampuTsuchibi2": "令和06年6月2日", "qap010_28070702_txtShishutsuKetteibi2": "令和06年6月2日", "qap010_28070702_txtKampuSeikyubi2": "令和06年6月2日", "qap010_28070702_txtJutoMeisaiJutoShoribi2": "R06.06.03", "qap010_28070702_txtJutoMeisaiFukagakuJutoGaku2": "1000", "qap010_28070702_txtGinkoNMKana": "ﾃ", "qap010_28070702_txtShitenNMKana": "ﾃ"}, "test_qap010_28070703": {"qap010_28070703_selGyoseiku": "テストX区", "qap010_28070703_selKamoku": "保育", "qap010_28070703_selKampuJotaiKbn": "一部充当", "qap010_28070703_selKampuNendoStart": "令和05年度", "qap010_28070703_selKampuNendoEnd": "令和06年度", "qap010_28070703_txtKampuTsuchibiStart": "令和01年5月1日", "qap010_28070703_txtKampuTsuchibiEnd": "令和05年5月1日", "qap010_28070703_selKampuHakkoTaishoKbn": "未発行", "qap010_28070703_txtJuminCD": "100100000100062"}, "test_qap010_28070704": {"qap010_28070704_atena_code": "100100000100062", "qap010_28070704_ginko": "ﾃｽﾄ", "qap010_28070704_shiten": "ﾃｽﾄ"}, "test_qap010_28070705": {"qap010_28070705_gyomu": "保育", "qap010_28070705_kamoku": "tab01_ZEAF002200_gyomuCode_JAABR20900chk21", "qap010_28070705_kazeiKonkyoYKaishi": "令和05年", "qap010_28070705_kazeiKonkyoYShuryo": "令和06年", "qap010_28070705_choteiHasseiYKaishi": "令和05年", "qap010_28070705_choteiHasseiYShuryo": "令和06年", "qap010_28070705_kibetuKaishi": "1", "qap010_28070705_kibetsuShuryo": "2", "qap010_28070705_nouKigenKaishi": "令和06年1月1日", "qap010_28070705_nouKigenshuryo": "令和07年1月1日", "qap010_28070705_hikiotoshiD": "令和05年", "qap010_28070705_nofusixyokubun": "なし", "qap010_28070705_minoushitei": "全件"}, "test_qap010_28070706": {"qap010_28070706_gyomu": "保育", "qap010_28070706_kamoku": "tab01_ZEAF002200_JAABN07300_gyoumuCodechk21", "qap010_28070706_cyoutehasseiNendo": "令和05年", "qap010_28070706_kazeikonkyoNendo": "令和05年"}, "test_qap010_28070707": {"qap010_28070707_gyomu": "保育", "qap010_28070707_minoshitei": "全件", "qap010_28070707_kamoku": "tab01_ZEAF002200_gyomuCodechk21", "qap010_28070707_jikoyoteiStart": "令和06年5月1日", "qap010_28070707_jikoyoteiEnd": "令和06年12月1日", "qap010_28070707_kessonbi": "令和06年5月1日"}, "test_qap010_28070708": {"qap010_28070708_groupingKBN1": "保育", "qap010_28070708_minoshitei": "全件", "qap010_28070708_kamoku": "tab01_ZEAF002200_gyomuCodechk21", "qap010_28070708_jikouYoteibiKaishi": "令和06年6月1日", "qap010_28070708_jikouYoteibiShuryo": "令和06年6月30日", "qap010_28070708_kessonbi": "令和06年6月10日", "qap010_28070708_groupingKBN2": "保育", "qap010_28070708_txtJuminCD": "101200000100622", "qap010_28070708_lblFunoKessonShoribi": "令和06年6月1日"}, "test_qap010_28070709": {"qap010_28070709_groupingKBN": "保育", "qap010_28070709_txtJuminCD": "101200000100622", "qap010_28070709_txtKariTorokubi": "令和06年6月13日", "qap010_28070709_txtYakusokubi": "令和06年6月13日", "qap010_28070709_txtEntaikinKeisanDay": "令和06年6月13日", "qap010_28070709_txtStartYM": "令和06年6月"}, "test_qap010_28070803": {"qap010_28070708_QApara0200": "令和05年", "qap010_28070708_QApara0014": "令和05年5月", "qap010_28070708_QApara1117": "令和05年5月", "qap010_28070803_txtJuminCD": "100100000100035", "qap010_28070803_button": "tab01_ZZZ000000_btnNo_1_1_button", "qap010_28070803_txtJidoKensakuAtenaCD": "101200000100027", "qap010_28070803_selFukaYear": "令和05年", "qap010_28070803_selFukaNendo": "令和05年5月", "qap010_28070803_selTaisyoYear": "令和05年", "qap010_28070803_selTaisyoYM": "令和05年5月", "qap010_28070803_txtJuminCD2": "100100000100035", "qap010_28070803_button2": "tab01_ZZZ000000_btnNo_1_1_button"}}