from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28090401(KodomoSiteTestCaseBase):
    """TestQAP010_28090401"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28090401(self):
        case_data_006 = self.test_data["006_ZEAF000400"]
        case_data_007 = self.test_data["007_ZEAF000400"]
        case_data_009 = self.test_data["009_ZEAF000400"]
        case_data_019 = self.test_data["019_ZEAF000400"]
        case_data_020 = self.test_data["020_ZEAF000400"]
        case_data_032 = self.test_data["032_ZEAF000400"]
        case_data_033 = self.test_data["033_ZEAF000400"]
        case_data_035 = self.test_data["035_ZEAF000400"]
        case_data_045 = self.test_data["045_ZEAF000400"]
        case_data_046 = self.test_data["046_ZEAF000400"]

        tab_index = 0
        self.do_login_new_tab()

        # 3 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 4 メインメニュー 画面: 「即時実行」クリック
        # 5 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=True)
        tab_index += 1
       
        # 6 スケジュール個別追加 画面: 業務名：子ども子育て支援 , サブシステム名：入所 , 処理名：現況処理
        # 7 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(
            gyomuNM=case_data_006.get("gyoumu_mei", ""),
            subSystemNM=case_data_006.get("sabushisutemu_mei", ""),
            shoriNM=case_data_006.get("shori_mei", ""),
            tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_7")

        # 8 スケジュール個別追加 画面: 「現況届提出対象者データ作成」の「No」ボタン押下
        self.click_batch_job_button_by_label(case_data_007.get("batch_job_001", ""), tab_index=tab_index)

        # 9 実行指示 画面: パラメータを入力
        # 所管区
        # 発行年月日
        # 基準日
        # 出力区分
        # 指定年月日
        # 並び順
        # 郵便区内特別有無
        # 再発行区分
        params = [
            {"title": "所管区", "type": "select", "value": case_data_009.get("shokan_ku", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_009.get("hakkou_nengappi", "")},
            {"title": "基準日", "type": "text", "value": case_data_009.get("kijun_bi", "")},
            {"title": "出力区分", "type": "select", "value": case_data_009.get("shutsuryoku_kubun", "")},
            {"title": "指定年月日", "type": "text", "value": case_data_009.get("shitei_nengappi", "")},
            {"title": "並び順", "type": "select", "value": case_data_009.get("narabijun", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_009.get("yuubin_ku_nai_tokubetsu_umu", "")},
            {"title": "再発行区分", "type": "select", "value": case_data_009.get("saihakkou_kubun", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_9")

        # 10 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 11 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=180)
        self.screen_shot("実行管理 画面_11")

        # 12 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 13 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_13")

        # 14 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 15 ファイルダウンロード 画面:「No1」ボタン押下
        # 16 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 17 帳票（PDF） 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 18 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_18")

        # 19 スケジュール個別追加 画面: 「保育所入所(支給認定)現況届出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(case_data_019.get("batch_job_002", ""), tab_index=tab_index)

        # 20 実行指示 画面: パラメータを入力
        # 所管区
        # 発行年月日
        params = [
            {"title": "所管区", "type": "select", "value": case_data_020.get("shokan_ku", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_020.get("hakkou_nengappi", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_20")

        # 21 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 22 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=180)
        self.screen_shot("実行管理 画面_22")

        # 23 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 24 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_24")

        # 25 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 26 ファイルダウンロード 画面:「No1」ボタン押下
        # 27 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 28 帳票（PDF） 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 29 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 30 メインメニュー 画面: 「即時実行」クリック
        # 31 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加")
        tab_index += 1

        # 32 スケジュール個別追加 画面: 業務名：子ども子育て支援 , サブシステム名：入所 , 処理名：現況処理(世帯別)
        # 33 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(
            gyomuNM=case_data_032.get("gyoumu_mei", ""),
            subSystemNM=case_data_032.get("sabushisutemu_mei", ""),
            shoriNM=case_data_032.get("shori_mei", ""),
            tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_33")

        # 34 スケジュール個別追加 画面: 「現況届提出対象者データ作成(世帯別)」の「No」ボタン押下
        self.click_batch_job_button_by_label(case_data_033.get("batch_job_003", ""), tab_index=tab_index)

        # 35 実行指示 画面: パラメータを入力
        # 所管区
        # 発行年月日
        # 基準日
        # 出力区分
        # 指定年月日
        # 並び順
        # 郵便区内特別有無
        # 再発行区分
        params = [
            {"title": "所管区", "type": "select", "value": case_data_035.get("shokan_ku", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_035.get("hakkou_nengappi", "")},
            {"title": "基準日", "type": "text", "value": case_data_035.get("kijun_bi", "")},
            {"title": "出力区分", "type": "select", "value": case_data_035.get("shutsuryoku_kubun", "")},
            {"title": "指定年月日", "type": "text", "value": case_data_035.get("shitei_nengappi", "")},
            {"title": "並び順", "type": "select", "value": case_data_035.get("narabijun", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_035.get("yuubin_ku_nai_tokubetsu_umu", "")},
            {"title": "再発行区分", "type": "select", "value": case_data_035.get("saihakkou_kubun", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_35")

        # 36 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 37 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=180)
        self.screen_shot("実行管理 画面_37")

        # 38 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 39 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_39")

        # 40 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 41 ファイルダウンロード 画面:「No1」ボタン押下
        # 42 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 43 帳票（PDF） 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 44 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_44")

        # 45 スケジュール個別追加 画面: 「保育所入所(支給認定)現況届出力(世帯別)」の「No」ボタン押下
        self.click_batch_job_button_by_label(case_data_045.get("batch_job_004", ""), tab_index=tab_index)

        # 46 実行指示 画面: パラメータを入力
        # 所管区
        # 発行年月日
        params = [
            {"title": "所管区", "type": "select", "value": case_data_046.get("shokan_ku", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_046.get("hakkou_nengappi", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_46")

        # 47 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 48 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=180)
        self.screen_shot("実行管理 画面_48")

        # 49 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 50 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_50")

        # 51 納品物管理 画面: 納品物「施設等利用給付認定申請取消通知書」の「ダウンロード」ボタン押下
        # 52 ファイルダウンロード 画面:「No1」ボタン押下
        # 53 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 54 帳票（PDF）画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
