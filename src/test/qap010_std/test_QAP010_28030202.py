from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28030202(KodomoSiteTestCaseBase):
    """TestQAP010_28030202"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28030202(self):
        """各種帳票作成"""

        case_data_021_ZEAF000400 = self.test_data["021_ZEAF000400"]
        case_data_023_ZEAF000400 = self.test_data["023_ZEAF000400"]
        case_data_024_ZEAF002200 = self.test_data["024_ZEAF002200"]
        case_data_034_ZEAF000400 = self.test_data["034_ZEAF000400"]
        case_data_040_ZEAF000400 = self.test_data["040_ZEAF000400"]
        case_data_042_ZEAF000400 = self.test_data["042_ZEAF000400"]
        case_data_043_ZEAF002200 = self.test_data["043_ZEAF002200"]
        case_data_053_ZEAF000400 = self.test_data["053_ZEAF000400"]
        tab_index = 0

        # 17 メインメニュー 画面:「子ども子育て表示」
        self.do_login_new_tab()

        # 18 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 19 メインメニュー 画面:「即時実行」クリック
        # 20 メインメニュー 画面:「スケジュール個別追加」をダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=True)
        tab_index += 1
        
        # 21 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：入所 <処理名>: 一括退所処理
        # 22 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_021_ZEAF000400.get("gyoumu_mei"),
                                        subSystemNM=case_data_021_ZEAF000400.get("sabushisutemu_mei"),
                                        shoriNM=case_data_021_ZEAF000400.get("shori_mei"), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_22")
        
        # 23 スケジュール個別追加 画面:「一括退所対象者 抽出」の「No」ボタン押下
        self.click_batch_job_button_by_label(case_data_023_ZEAF000400.get("batch_job_001"), tab_index)
        
        # 24 実行指示 画面: パラメータを入力
        params = [
            {"title": "基準日", "type": "text", "value": case_data_024_ZEAF002200.get("kijunbi", "")},
            {"title": "抽出区分", "type": "select", "value": case_data_024_ZEAF002200.get("chuushutsu_kubun", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_24")

        # 25 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)
        
        # 26 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_26")

        # 27 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")
        
        # 28 実行管理 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_28")

        # 29 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 30 ファイルダウンロード 画面:「No1」ボタン押下
        # 31 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 32 ファイル 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
        
        # 33 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_33")

        # 34 納品物管理 画面:「一括退所対象者 更新」の「No」ボタン押下
        self.click_batch_job_button_by_label(case_data_034_ZEAF000400.get("batch_job_002"), tab_index)
        
        # 35 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)
        
        # 36 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_36")

        # 37 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 38 メインメニュー 画面:「即時実行」クリック
        # 39 メインメニュー 画面:「スケジュール個別追加」をダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加")
        tab_index += 1
        
        # 40 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：入所 <処理名>: 卒園処理
        # 41 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_040_ZEAF000400.get("gyoumu_mei"),
                                        subSystemNM=case_data_040_ZEAF000400.get("sabushisutemu_mei"),
                                        shoriNM=case_data_040_ZEAF000400.get("shori_mei"), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_41")
        
        # 42 スケジュール個別追加 画面:「卒園対象者抽出」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_042_ZEAF000400.get("batch_job_003"), tab_index=tab_index)

        # 43 実行指示 画面: パラメータを入力
        params = [
            {"title": "対象年度", "type": "text", "value": case_data_043_ZEAF002200.get("taishou_nendo", "")},
            {"title": "卒園条件", "type": "select", "value": case_data_043_ZEAF002200.get("sotsuen_jouken", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_43")

        # 44 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)
        
        # 45 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_45")

        # 46 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 47 実行管理 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_47")

        # 48 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 49 ファイルダウンロード 画面:「No1」ボタン押下
        # 50 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 51 ファイル 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
        
        # 52 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_52")

        # 53 納品物管理 画面:「卒園対象者更新」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_053_ZEAF000400.get("batch_job_004"), tab_index=tab_index)

        # 54 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)
        
        # 55 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_55")
