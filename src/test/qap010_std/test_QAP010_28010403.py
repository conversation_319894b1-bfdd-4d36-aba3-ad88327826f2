from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28010403(KodomoSiteTestCaseBase):
    """TestQAP010_28010403"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28010403(self):
        tab_index = 0
        case_data_102_ZEAF000400 = self.test_data["102_ZEAF000400"]
        case_data_104_ZEAF000400 = self.test_data["104_ZEAF000400"]
        case_data_105_ZEAF002200 = self.test_data["105_ZEAF002200"]
        case_data_115_ZEAF000400 = self.test_data["115_ZEAF000400"]
        case_data_116_ZEAF002200 = self.test_data["116_ZEAF002200"]

        # 98 メインメニュー 画面:「子ども子育て表示」
        self.do_login_new_tab()

        # 99 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 100 メインメニュー 画面: 「即時実行」クリック
        # 101 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=True)
        tab_index += 1

        # 102 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：入所 <処理名>：現況届未提出者督促状_一覧出力処理
        # 103 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_102_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_102_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_102_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_103")

        # 104 スケジュール個別追加 画面: 「現況届未提出者一覧出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_104_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 105 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_105_ZEAF002200.get("shokanku", "")},
            {"title": "出力区分", "type": "select", "value": case_data_105_ZEAF002200.get("shutsuryoku_kubun", "")},
            {"title": "対象年度", "type": "text", "value": case_data_105_ZEAF002200.get("taishou_nendo", "")},
            {"title": "提出期限", "type": "text", "value": case_data_105_ZEAF002200.get("teishutsu_kigen", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_105_ZEAF002200.get("hakkou_nengappi", "")},
            {"title": "並び順", "type": "select", "value": case_data_105_ZEAF002200.get("narabijun", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_105_ZEAF002200.get("yuubin_kunai_tokubetsu_umu", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_105")

        # 106 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 107 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_107")

        # 108 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 109 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_109")

        # 110 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 111 ファイルダウンロード 画面: 「No1」ボタン押下
        # 112 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 113 帳票（PDF） 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 114 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_114")

        # 115 スケジュール個別追加 画面: 「督促状（現況届提出について）出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_115_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 116 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_116_ZEAF002200.get("shokanku", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_116_ZEAF002200.get("hakkou_nengappi", "")},
            {"title": "出力区分", "type": "select", "value": case_data_116_ZEAF002200.get("shutsuryoku_kubun", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_116")

        # 117 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 118 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_118")

        # 119 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 120 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_120")

        # 121 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 122 ファイルダウンロード 画面: 「No1」ボタン押下
        # 123 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 124 帳票（PDF） 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
