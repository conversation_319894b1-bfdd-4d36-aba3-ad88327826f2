from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28070602(KodomoSiteTestCaseBase):
    """TestQAP010_28070602"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28070602(self):
        case_data_072_ZEAF000400 = self.test_data["072_ZEAF000400"]
        case_data_074_ZEAF000400 = self.test_data["074_ZEAF000400"]
        case_data_075_ZEAF002200 = self.test_data["075_ZEAF002200"]
        case_data_079_ZEAF000400 = self.test_data["079_ZEAF000400"]
        case_data_081_ZEAF000400 = self.test_data["081_ZEAF000400"]
        case_data_082_ZEAF002200 = self.test_data["082_ZEAF002200"]
        case_data_086_ZEAF000400 = self.test_data["086_ZEAF000400"]
        case_data_088_ZEAF000400 = self.test_data["088_ZEAF000400"]
        case_data_089_ZEAF002200 = self.test_data["089_ZEAF002200"]
        case_data_094_ZEAF002700 = self.test_data["094_ZEAF002700"]
        case_data_098_ZEAF002700 = self.test_data["098_ZEAF002700"]
        case_data_102_ZEAF002700 = self.test_data["102_ZEAF002700"]
        case_data_107_ZEAF000400 = self.test_data["107_ZEAF000400"]
        tab_index = 0

        # 68 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 69 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 70 メインメニュー 画面:「即時実行」クリック
        # 71 メインメニュー 画面:「スケジュール個別追加」をダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=True)
        tab_index += 1

        # 72 スケジュール個別追加 画面: <業務名>：宛名情報管理 <サブシステム名>：宛名 <処理名>: 宛名・口座バッチマスタ作成処理
        # 73 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_072_ZEAF000400.get("gyoumu_mei"),
                                        subSystemNM=case_data_072_ZEAF000400.get("sabushisutemu_mei"),
                                        shoriNM=case_data_072_ZEAF000400.get("shori_mei"), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_73")

        # 74 スケジュール個別追加 画面:「宛名・口座バッチマスタ作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_074_ZEAF000400.get("batch_job_001"),
                                             tab_index=tab_index)

        # 75 実行指示 画面: パラメータを入力
        params = [
            {"title": "業務名１", "type": "select", "value": case_data_075_ZEAF002200.get("gyomu_mei_1", "")},
            {"title": "業務別基準日1", "type": "text",
             "value": case_data_075_ZEAF002200.get("gyomu_betsu_kijunbi_1", "")},
            {"title": "口座基準日1", "type": "text", "value": case_data_075_ZEAF002200.get("koza_kijunbi_1", "")},
            {"title": "業務名2", "type": "select", "value": case_data_075_ZEAF002200.get("gyomu_mei_2", "")},
            {"title": "業務別基準日2", "type": "text",
             "value": case_data_075_ZEAF002200.get("gyomu_betsu_kijunbi_2", "")},
            {"title": "口座基準日2", "type": "text", "value": case_data_075_ZEAF002200.get("koza_kijunbi_2", "")},
            {"title": "業務名3", "type": "select", "value": case_data_075_ZEAF002200.get("gyomu_mei_3", "")},
            {"title": "業務別基準日3", "type": "text",
             "value": case_data_075_ZEAF002200.get("gyomu_betsu_kijunbi_3", "")},
            {"title": "口座基準日3", "type": "text", "value": case_data_075_ZEAF002200.get("koza_kijunbi_3", "")},
            {"title": "業務名4", "type": "select", "value": case_data_075_ZEAF002200.get("gyomu_mei_4", "")},
            {"title": "業務別基準日4", "type": "text",
             "value": case_data_075_ZEAF002200.get("gyomu_betsu_kijunbi_4", "")},
            {"title": "口座基準日4", "type": "text", "value": case_data_075_ZEAF002200.get("koza_kijunbi_4", "")},
            {"title": "業務名5", "type": "select", "value": case_data_075_ZEAF002200.get("gyomu_mei_5", "")},
            {"title": "業務別基準日5", "type": "text",
             "value": case_data_075_ZEAF002200.get("gyomu_betsu_kijunbi_5", "")},
            {"title": "口座基準日5", "type": "text", "value": case_data_075_ZEAF002200.get("koza_kijunbi_5", "")},
            {"title": "業務名6", "type": "select", "value": case_data_075_ZEAF002200.get("gyomu_mei_6", "")},
            {"title": "業務別基準日6", "type": "text",
             "value": case_data_075_ZEAF002200.get("gyomu_betsu_kijunbi_6", "")},
            {"title": "口座基準日6", "type": "text", "value": case_data_075_ZEAF002200.get("koza_kijunbi_6", "")},
            {"title": "業務名7", "type": "select", "value": case_data_075_ZEAF002200.get("gyomu_mei_7", "")},
            {"title": "業務別基準日7", "type": "text",
             "value": case_data_075_ZEAF002200.get("gyomu_betsu_kijunbi_7", "")},
            {"title": "口座基準日7", "type": "text", "value": case_data_075_ZEAF002200.get("koza_kijunbi_7", "")},
            {"title": "業務名8", "type": "select", "value": case_data_075_ZEAF002200.get("gyomu_mei_8", "")},
            {"title": "業務別基準日8", "type": "text",
             "value": case_data_075_ZEAF002200.get("gyomu_betsu_kijunbi_8", "")},
            {"title": "口座基準日8", "type": "text", "value": case_data_075_ZEAF002200.get("koza_kijunbi_8", "")},
            {"title": "業務名9", "type": "select", "value": case_data_075_ZEAF002200.get("gyomu_mei_9", "")},
            {"title": "業務別基準日9", "type": "text",
             "value": case_data_075_ZEAF002200.get("gyomu_betsu_kijunbi_9", "")},
            {"title": "口座基準日9", "type": "text", "value": case_data_075_ZEAF002200.get("koza_kijunbi_9", "")},
            {"title": "業務名10", "type": "select", "value": case_data_075_ZEAF002200.get("gyomu_mei_10", "")},
            {"title": "業務別基準日10", "type": "text",
             "value": case_data_075_ZEAF002200.get("gyomu_betsu_kijunbi_10", "")},
            {"title": "口座基準日10", "type": "text", "value": case_data_075_ZEAF002200.get("koza_kijunbi_10", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_75")

        # 76 実行指示 画面:「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 77 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=180, time_span_sec=20)
        self.screen_shot("実行管理 画面_77")

        # 78 実行管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002300", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_78")

        # 79 スケジュール個別追加 画面: <業務名>：収納・滞納 <サブシステム名>：マスタ作成 <処理名>: 収納マスタ＆収納用宛名口座マスタ作成処理（統合版）
        # 80 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_079_ZEAF000400.get("gyoumu_mei"),
                                        subSystemNM=case_data_079_ZEAF000400.get("sabushisutemu_mei"),
                                        shoriNM=case_data_079_ZEAF000400.get("shori_mei"), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_80")

        # 81 スケジュール個別追加 画面:「収納マスタ＆収納用宛名口座マスタ作成本処理(統合版)」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_081_ZEAF000400.get("batch_job_002"),
                                             tab_index=tab_index)

        # 82 実行指示 画面: パラメータを入力
        params = [
            {"title": "未納指定", "type": "select", "value": case_data_082_ZEAF002200.get("mino_shitei", "")},
            {"title": "科目", "type": "checkbox", "value": case_data_082_ZEAF002200.get("kamoku_chk").values()},
            {"title": "調定発生年度開始", "type": "select",
             "value": case_data_082_ZEAF002200.get("chotei_hassei_nendo_kaishi", "")},
            {"title": "調定発生年度終了", "type": "select",
             "value": case_data_082_ZEAF002200.get("chotei_hassei_nendo_shuryo", "")},
            {"title": "課税根拠年度開始", "type": "select",
             "value": case_data_082_ZEAF002200.get("kazei_konkyo_nendo_kaishi", "")},
            {"title": "課税根拠年度終了", "type": "select",
             "value": case_data_082_ZEAF002200.get("kazei_konkyo_nendo_shuryo", "")},
            {"title": "期別開始", "type": "text", "value": case_data_082_ZEAF002200.get("kibetsu_kaishi", "")},
            {"title": "期別終了", "type": "text", "value": case_data_082_ZEAF002200.get("kibetsu_shuryo", "")},
            {"title": "納期限開始", "type": "text", "value": case_data_082_ZEAF002200.get("nokigen_kaishi", "")},
            {"title": "納期限終了", "type": "text", "value": case_data_082_ZEAF002200.get("nokigen_shuryo", "")},
            {"title": "除外納付区分", "type": "text", "value": case_data_082_ZEAF002200.get("jogai_nofu_kubun", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_82")

        # 83 実行指示 画面:「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 84 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=180, time_span_sec=20)
        self.screen_shot("実行管理 画面_84")

        # 85 実行管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002300", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_85")

        # 86 スケジュール個別追加 画面: <業務名>：収納・滞納 <サブシステム名>：随時 <処理名>: 催告書作成処理
        # 87 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_086_ZEAF000400.get("gyoumu_mei"),
                                        subSystemNM=case_data_086_ZEAF000400.get("sabushisutemu_mei"),
                                        shoriNM=case_data_086_ZEAF000400.get("shori_mei"), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_87")

        # 88 スケジュール個別追加 画面:「催告書作成本処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_088_ZEAF000400.get("batch_job_003"),
                                             tab_index=tab_index)

        # 89 実行指示 画面: パラメータを入力
        params = [
            {"title": "科目", "type": "checkbox", "value": case_data_082_ZEAF002200.get("kamoku_chk").values()},
            {"title": "調定発生年度（開始）", "type": "select",
             "value": case_data_089_ZEAF002200.get("chotei_hassei_nendo_start", "")},
            {"title": "調定発生年度（終了）", "type": "select",
             "value": case_data_089_ZEAF002200.get("chotei_hassei_nendo_end", "")},
            {"title": "納期限（開始）", "type": "text", "value": case_data_089_ZEAF002200.get("noukigen_start", "")},
            {"title": "納期限（終了）", "type": "text", "value": case_data_089_ZEAF002200.get("noukigen_end", "")},
            {"title": "発送日", "type": "text", "value": case_data_089_ZEAF002200.get("hassoubi", "")},
            {"title": "支払期限", "type": "text", "value": case_data_089_ZEAF002200.get("shiharai_kigen", "")},
            {"title": "延滞金計算日", "type": "text", "value": case_data_089_ZEAF002200.get("entaikin_keisanbi", "")},
            {"title": "収納現在日", "type": "text", "value": case_data_089_ZEAF002200.get("shunou_genzai_date", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_89")

        # 90 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 91 実行指示 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=180, time_span_sec=20)
        self.screen_shot("実行管理 画面_91")

        # 92 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 93 実行管理 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_93")

        # 94 納品物管理 画面: 納品物「JACP200500_催告書.pdf」の「ダウンロード」ボタン押下
        # 95 ファイルダウンロード 画面:「No1」ボタン押下
        # 96 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 97 ファイル 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_094_ZEAF002700.get("report_name", ""))

        # 98 納品物管理 画面: 納品物「JACP201100_催告書発付一覧表.pdf」の「ダウンロード」ボタン押下
        # 99 ファイルダウンロード 画面:「No1」ボタン押下
        # 100 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 101 ファイル 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_098_ZEAF002700.get("report_name", ""))

        # 102 納品物管理 画面: 納品物「JACP201500_催告書発付不能一覧表.pdf」の「ダウンロード」ボタン押下
        # 103 ファイルダウンロード 画面:「No1」ボタン押下
        # 104 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 105 ファイル 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_102_ZEAF002700.get("report_name", ""))

        # 106 実行管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_106")

        # 107 スケジュール個別追加 画面:「催告書作成後処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_107_ZEAF000400.get("batch_job_004"),
                                             tab_index=tab_index)

        # 108 実行指示 画面:「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 109 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=200, time_span_sec=20)
        self.screen_shot("実行管理 画面_109")
