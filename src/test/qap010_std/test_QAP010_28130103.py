from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28130103(KodomoSiteTestCaseBase):
    """TestQAP010_28130103"""

    def setUp(self):
        super().setUp()

    # 延長保育利用実績内容を一括で取込できることを確認する。
    def test_QAP010_28130103(self):
        """延長保育利用実績一括取込"""
        tab_index = 1
        case_data_200_QAPF105800 = self.test_data["200_QAPF105800"]
        case_data_202_QAPF100100 = self.test_data["202_QAPF100100"]
        case_data_206_QAPF101900 = self.test_data["206_QAPF101900"]
        case_data_211_QAPF101900 = self.test_data["211_QAPF101900"]
        case_data_222_ZEAF000400 = self.test_data["222_ZEAF000400"]
        case_data_224_ZEAF000400 = self.test_data["224_ZEAF000400"]
        case_data_225_ZEAF002200 = self.test_data["225_ZEAF002200"]
        case_data_239_ZEAF000400 = self.test_data["239_ZEAF000400"]
        case_data_240_ZEAF002200 = self.test_data["240_ZEAF002200"]

        # 1, 2 メインメニュー画面: 表示
        self.do_login_new_tab()

        # 197 メインメニュー 画面: メインメニューから「子ども子育て支援」をクリックする 
        # 198 メインメニュー 画面: 「入所管理」をクリック 
        # 199 メインメニュー 画面: 「児童検索」をダブルクリック 
        self._goto_menu_by_label(menu_level_1="子ども子育て支援",
                                 menu_level_2="入所管理",
                                 menu_level_3="児童検索",
                                 is_new_tab=True)

        # 200 検索条件入力 画面: 検索条件を入力  
        # 検索条件1 : 児童検索
        # 保護者カナ氏名
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_txtJidoKensakuHogoshaKanaShimei_textboxInput",
            value=case_data_200_QAPF105800.get("hogosha_kana_shimei", ""))
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selJidoKensakuHogoshaKanaShimeiOption_select",
            text=case_data_200_QAPF105800.get("hogosha_kana_shimei_option", ""))

        # 保護者氏名
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_txtJidoKensakuHogoshaKanjiShimei_textboxInput",
            value=case_data_200_QAPF105800.get("hogosha_kanji_shimei", ""))
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selJidoKensakuHogoshaKanjiShimeiOption_select",
            text=case_data_200_QAPF105800.get("hogosha_kanji_shimei_option", ""))

        # 児童カナ氏名
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_txtJidoKensakuJidoKanaShimei_textboxInput",
            value=case_data_200_QAPF105800.get("jido_kana_shimei", ""))
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selJidoKensakuJidoKanaShimeiOption_select",
            text=case_data_200_QAPF105800.get("jido_kana_shimei_option", ""))

        # 児童氏名
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_txtJidoKensakuJidoKanjiShimei_textboxInput",
            value=case_data_200_QAPF105800.get("jido_kanji_shimei", ""))
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selJidoKensakuJidoKanjiShimeiOption_select",
            text=case_data_200_QAPF105800.get("jido_kanji_shimei_option", ""))

        # 生年月日
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_txtJidoKensakuSeinengappiStart_textboxInput",
            value=case_data_200_QAPF105800.get("seinengappi_start", ""))
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_txtJidoKensakuSeinengappiEnd_textboxInput",
            value=case_data_200_QAPF105800.get("seinengappi_end", ""))
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selAimaiBirth1_select",
            text=case_data_200_QAPF105800.get("seinengappi_option", ""))

        # 性別
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selJidoKensakuSeibetsu_select",
            text=case_data_200_QAPF105800.get("seibetsu", ""))

        # 宛名コード
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_txtJidoKensakuAtenaCD_textboxInput",
            value=case_data_200_QAPF105800.get("atena_code", ""))

        # 状態区分
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selJidoKensakuJotaiKbn_select",
            text=case_data_200_QAPF105800.get("jotai_kbn", ""))

        # 所管区
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selWrGyoseiku_JidoKensakuShokanKu_select",
            text=case_data_200_QAPF105800.get("shokan_ku", ""))

        # 入所番号
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_txtJidoKensakuNyushoNo_textboxInput",
            value=case_data_200_QAPF105800.get("nyusho_no", ""))

        # 入所申込番号
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_txtJidoKensakuMoushikomiNo_textboxInput",
            value=case_data_200_QAPF105800.get("moushikomi_no", ""))

        # 検索条件2 : 施設検索（児童検索用）
        # 施設コード
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_txtShisetsuKensakuJidoKensakuShisetsuCD_textboxInput",
            value=case_data_200_QAPF105800.get("shisetsu_code", ""))

        # 施設カナ名称
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_txtShisetsuKensakuJidoKensakuShisetsuKanaNM_textboxInput",
            value=case_data_200_QAPF105800.get("shisetsu_kana_name", ""))
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selShisetsuKensakuJidoKensakuShisetsuKanaNMOption_select",
            text=case_data_200_QAPF105800.get("shisetsu_kana_name_option", ""))

        # 施設種類
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selShisetsuKensakuJidoKensakuShisetsuShurui_select",
            text=case_data_200_QAPF105800.get("shisetsu_shurui", ""))

        # 所在区
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selWrGyoseiku_ShisetsuKensakuJidoKensakuShozaiKu_select",
            text=case_data_200_QAPF105800.get("shozai_ku", ""))

        # 検索条件3 : 認定情報検索
        # 認定者番号
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_txtNinteiINFOKensakuNinteiBango_textboxInput",
            value=case_data_200_QAPF105800.get("nintei_bango", ""))

        # 認定区分
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selNinteiINFOKensakuNinteiKbn_select",
            text=case_data_200_QAPF105800.get("nintei_kbn", ""))

        # 必要性事由
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selNinteiINFOKensakuHitsuyoseiJiyu_select",
            text=case_data_200_QAPF105800.get("hitsuyosei_jiyu", ""))

        # 保育必要量
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selNinteiINFOKensakuHoikuHitsuyoryo_select",
            text=case_data_200_QAPF105800.get("hoiku_hitsuyoryo", ""))

        # 優先利用
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selNinteiINFOKensakuYusenRiyo_select",
            text=case_data_200_QAPF105800.get("yusen_riyo", ""))

        # 決定年月日
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selNinteiINFOKensakuKetteiDay_select",
            text=case_data_200_QAPF105800.get("kettei_day", ""))

        # 負担区分決定年月日
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selNinteiINFOKensakuFutanKbnKetteiDay_select",
            text=case_data_200_QAPF105800.get("futan_kbn_kettei_day", ""))

        # 検索条件4 : 年度検索
        # 年度
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selNendoKensakuNendo_select",
            text=case_data_200_QAPF105800.get("nendo", ""))

        # クラス年齢
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_selNendoKensakuClassAge_select",
            text=case_data_200_QAPF105800.get("class_age", ""))

        # 入所番号
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_txtNendoKensakuNyushoNo_textboxInput",
            value=case_data_200_QAPF105800.get("nendo_nyusho_no", ""))

        # 並び替え順序
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF105800_sort_sel_0_select",
            text=case_data_200_QAPF105800.get("sort_order", ""))
        self.auto_screen_shot(step_index=200)

        # 201 検索条件入力 画面: 「検索」ボタン押下
        self.click_by_id(f"tab{tab_index:02d}_QAPF105800_WrCmnBtn05_button")

        # 202 児童台帳 画面: 「入所管理」タブをクリック  
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAPF100100_pages1"):
            atena_code = case_data_202_QAPF100100.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code=atena_code,tab_index=tab_index,col_number=7)
        self.change_tab_by_label(tab_id=tab_index,
                                 screen_id="QAPF103500",
                                 label="入所管理")
        self.auto_screen_shot(step_index=202)

        # 203 児童台帳 画面: 「特別保育」ボタンをクリック 
        self.click_by_id(idstr=f"tab{tab_index:02d}_QAPF103500_btnTokubetsuHoiku_button")

        # 204 特別保育実績管理 画面: 「特別保育申請」ボタンをクリック  
        self.click_by_id(idstr=f"tab{tab_index:02d}_QAPF105900_btnTokubetsuHoikuShinsei_button")
        self.auto_screen_shot(step_index=204)

        # 205 特別保育申請管理 画面: 「修正」ボタンをクリック
        self.click_by_id(idstr=f"tab{tab_index:02d}_QAPF101900_btnEditChg_button")

        # 206 特別保育申請管理 画面: "認定年月終了：
        #     延長利用期間終了：
        #     を修正"  
        # 認定年月開始
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_txtNinteiYMStart_textboxInput",
            value=case_data_206_QAPF101900.get("nintei_ym_start", ""))

        # 認定年月終了
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_txtNinteiYMEnd_textboxInput",
            value=case_data_206_QAPF101900.get("nintei_ym_end", ""))

        # 延長利用期間開始
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_txtKiboJisshiKikanStart_textboxInput",
            value=case_data_206_QAPF101900.get("kibo_jisshi_kikan_start", ""))

        # 延長利用期間終了
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_txtKiboJisshiKikanEnd_textboxInput",
            value=case_data_206_QAPF101900.get("kibo_jisshi_kikan_end", ""))
        self.auto_screen_shot(step_index=206)

        # 207 特別保育申請管理 画面: 「入力チェック」ボタンをクリック  
        self.click_by_id(idstr=f"tab{tab_index:02d}_QAPF101900_checkbtn_button")
        self.assert_message_area(msg_span_id=f"tab{tab_index:02d}_QAPF101900_msg_span",
                                 msg="入力チェックが完了しました。")

        # 208 特別保育申請管理 画面: 「登録」ボタンをクリック
        self.click_by_id(idstr=f"tab{tab_index:02d}_QAPF101900_regbtn_button")

        # 209 特別保育申請管理 画面: 「はい」ボタンをクリック
        self.alert_accept()
        self.assert_message_area(msg_span_id=f"tab{tab_index:02d}_QAPF101900_msg_span", msg="更新しました。")
        self.auto_screen_shot(step_index=209)

        # 210 特別保育申請管理 画面: 「追加」ボタンをクリック
        self.click_by_id(idstr=f"tab{tab_index:02d}_QAPF101900_btnAddChg_button")

        # 211 特別保育申請管理 画面:  
        # 年度
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_selTokubetsuHoikuShinseiNendo_select",
            text=case_data_211_QAPF101900.get("nendo", ""))

        # サービス区分
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_selServiceKbn_select",
            text=case_data_211_QAPF101900.get("service_kbn", ""))

        # 申請年月日
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_txtShinseiYMD_textboxInput",
            value=case_data_211_QAPF101900.get("shinsei_ymd", ""))

        # 申請種別
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_selShinseiShubetsu_select",
            text=case_data_211_QAPF101900.get("shinsei_shubetsu", ""))

        # 決定年月日
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_txtKetteiYMD_textboxInput",
            value=case_data_211_QAPF101900.get("kettei_ymd", ""))

        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_selKekka_select",
            text=case_data_211_QAPF101900.get("kekka", ""))

        # 認定年月
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_txtNinteiYMStart_textboxInput",
            value=case_data_211_QAPF101900.get("nintei_ym_start", ""))

        # 認定年月終了
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_txtNinteiYMEnd_textboxInput",
            value=case_data_211_QAPF101900.get("nintei_ym_end", ""))

        # サービス種類
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_selShurui_select",
            text=case_data_211_QAPF101900.get("shurui", ""))

        # 延長利用期間開始
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_txtKiboJisshiKikanStart_textboxInput",
            value=case_data_211_QAPF101900.get("kibo_jisshi_kikan_start", ""))

        # 延長利用期間終了
        self.form_input_by_id(
            idstr=f"tab{tab_index:02d}_QAPF101900_txtKiboJisshiKikanEnd_textboxInput",
            value=case_data_211_QAPF101900.get("kibo_jisshi_kikan_end", ""))
        self.auto_screen_shot(step_index=211)

        # 212 特別保育申請管理 画面: 「入力チェック」ボタンをクリック  
        self.click_by_id(idstr=f"tab{tab_index:02d}_QAPF101900_checkbtn_button")
        self.assert_message_area(msg_span_id=f"tab{tab_index:02d}_QAPF101900_msg_span",
                                 msg="入力チェックが完了しました。")

        # 213 特別保育申請管理 画面: 「登録」ボタンをクリック
        self.click_by_id(idstr=f"tab{tab_index:02d}_QAPF101900_regbtn_button")

        # 214 特別保育申請管理 画面: 「はい」ボタンをクリック
        self.alert_accept()
        self.assert_message_area(msg_span_id=f"tab{tab_index:02d}_QAPF101900_msg_span", msg="登録しました。")
        self.auto_screen_shot(step_index=214)

        # 215 特別保育申請管理 画面: パンくず「児童台帳」を押下  
        self.click_breadcrumb_by_label(tab_index=tab_index,
                                       label="児童台帳",
                                       screen_id="QAPF101900")
        self.auto_screen_shot(step_index=215)

        # 216 児童台帳 画面: 「賦課」ボタンをクリック
        self.click_by_id(idstr=f"tab{tab_index:02d}_QAPF103500_btnFuka_button")

        # 217 児童賦課情報 画面: 「賦課計算」ボタンをクリック 
        self.click_by_id(idstr=f"tab{tab_index:02d}_QAPF107000_btnFukakeisan_button")

        # 218 児童賦課情報 画面: 「はい」ボタンをクリック
        self.alert_accept()
        self.auto_screen_shot(step_index=218)

        # 219 メインメニュー 画面: メインメニューから「バッチ管理」クリック 
        # 220 メインメニュー 画面: 「即時実行」クリック  
        # 221 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック  
        self._goto_menu_by_label(menu_level_1="バッチ管理",
                                 menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=False)

        tab_index += 1

        # 222 スケジュール個別追加 画面: "業務名：子ども子育て支援
        #     サブシステム名：入所
        #     処理名：延長保育実施解除通知書_一覧出力処理" 
        # 223 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_222_ZEAF000400.get("gyoumu_mei"),
                                        subSystemNM=case_data_222_ZEAF000400.get("sabu_shisutemu_mei"),
                                        shoriNM=case_data_222_ZEAF000400.get("shori_mei"),
                                        tab_index=tab_index,
                                        )
        self.auto_screen_shot(step_index=223)

        # 224 スケジュール個別追加 画面: 「延長保育実施解除対象者抽出」の「No」ボタン押下  
        job_label = case_data_224_ZEAF000400.get("batch_job_001", "")
        self.click_batch_job_button_by_label(job_label=job_label, tab_index=tab_index)

        # 225 実行指示 画面: パラメータを入力 
        params = [
            {"title": "所管区", "type": "select", "value": case_data_225_ZEAF002200.get("shokan_ku")},
            {"title": "サービス区分", "type": "select", "value": case_data_225_ZEAF002200.get("sabisu_kbn")},
            {"title": "対象年度", "type": "text", "value": case_data_225_ZEAF002200.get("taishou_nendo")},
            {"title": "対象年月開始", "type": "text", "value": case_data_225_ZEAF002200.get("taishou_nengappi_start")},
            {"title": "対象年月終了", "type": "text", "value": case_data_225_ZEAF002200.get("taishou_nengappi_end")},
            {"title": "施設種類", "type": "select", "value": case_data_225_ZEAF002200.get("shisetsu_shurui")},
            {"title": "施設コード", "type": "text", "value": case_data_225_ZEAF002200.get("shisetsu_code")},
            {"title": "児童宛名コード", "type": "text", "value": case_data_225_ZEAF002200.get("jidou_atena_code")},
            {"title": "所在", "type": "select", "value": case_data_225_ZEAF002200.get("shozai_ku")},
            {"title": "入所形態", "type": "select", "value": case_data_225_ZEAF002200.get("nyusho_kbn")},
            {"title": "発行年月日", "type": "text", "value": case_data_225_ZEAF002200.get("hakko_nengapi")},
            {"title": "並び順", "type": "select", "value": case_data_225_ZEAF002200.get("narabi_jun")},
            {"title": "再発行区分", "type": "select", "value": case_data_225_ZEAF002200.get("saihakkou_kubun")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_225_ZEAF002200.get("youbin_kunai_tokubetsu_umu")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.auto_screen_shot(step_index=225)

        # 226 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 227 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=1000, time_span_sec=3)
        self.auto_screen_shot(step_index=227)

        # 228 結果確認 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label(label="1")
        self.auto_screen_shot(step_index=228)

        # 229 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id(f"tab{tab_index:02d}_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.auto_screen_shot(step_index=229)

        # 230 納品物管理 画面: 納品物「延長保育実施解除対象者一覧」の「ダウンロード」ボタン押下 
        # 231 納品物管理 画面: 「No1」ボタン押下  
        # 232 納品物管理 画面: 「ファイルを開く(O)」ボタン押下 
        # 233 納品物管理 画面: 「×」ボタン押下 
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
        self.auto_screen_shot(step_index=231)

        # 234 納品物管理 画面: 納品物「延長保育実施解除対象者一覧.CSV」の「ダウンロード」ボタン押下
        # 235 納品物管理 画面: 「No1」ボタン押下  
        # 236 納品物管理 画面: 「ファイルを開く(O)」ボタン押下 
        # 237 納品物管理 画面: 「×」ボタン押下 
        self.auto_screen_shot(step_index=235)

        # 238 スケジュール個別追加 画面: パンくず「スケジュール個別追加」を押下  
        self.click_breadcrumb_by_label(tab_index=tab_index,
                                       label="スケジュール個別追加",
                                       screen_id="ZEAF002700")
        self.auto_screen_shot(step_index=238)

        # 239 スケジュール個別追加 画面: 「延長保育実施解除通知書出力」の「No」ボタン押下  
        job_label = case_data_239_ZEAF000400.get("batch_job_002", "")
        self.click_batch_job_button_by_label(job_label=job_label, tab_index=tab_index)

        # 240 実行指示 画面: パラメータを入力 
        params = [
            {"title": "所管区", "type": "select", "value": case_data_240_ZEAF002200.get("shokan_ku")},
            {"title": "施設種類", "type": "select", "value": case_data_240_ZEAF002200.get("shisetsu_shurui")},
            {"title": "施設コード", "type": "text", "value": case_data_240_ZEAF002200.get("shisetsu_code")},
            {"title": "児童宛名コード", "type": "text", "value": case_data_240_ZEAF002200.get("jidou_atena_code")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.auto_screen_shot(step_index=240)

        # 241 実行指示 画面: 「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 242 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=1000, time_span_sec=3)
        self.auto_screen_shot(step_index=242)

        # 243 結果確認 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label(label="1")
        self.auto_screen_shot(step_index=243)

        # 244 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id(f"tab{tab_index:02d}_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.auto_screen_shot(step_index=244)

        # 245 納品物管理 画面: 納品物「延長保育実施解除通知書」の「ダウンロード」ボタン押下
        # 246 納品物管理 画面: 「No1」ボタン押下  
        # 247 納品物管理 画面: 「ファイルを開く(O)」ボタン押下 
        # 248 納品物管理 画面: 「×」ボタン押下 
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
        self.auto_screen_shot(step_index=246)
