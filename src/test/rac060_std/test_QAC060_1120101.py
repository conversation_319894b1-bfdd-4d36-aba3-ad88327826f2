import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC060_1120101(FukushiSiteTestCaseBase):
    """TestQAC060_1120101"""

    def setUp(self):
        case_data = self.test_data["TestQAC060_1120101"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC060", "DELETE_ATENA_CODE": case_data.get("atena_code", ""),"DELETE_ATENA_CODE2": case_data.get("atena_code2", ""),
                      "TARGET_NENDO":case_data.get("nendo", ""),"TARGET_SOUSYOTOKU":case_data.get("soushotoku", "")}
        self.exec_sqlfile("Test_QAC060_1120101.sql", params=sql_params)
        super().setUp()

    # 認定申請した住民およびその他必要な情報の登録ができることを確認する。
    def test_QAC060_1120101(self):
        """申請情報登録"""

        case_data = self.test_data["TestQAC060_1120101"]
        atena_code = case_data.get("atena_code", "")
        shinsei_shubetsu = case_data.get("shinsei_shubetsu", "")
        shinsei_riyuu = case_data.get("shinsei_riyuu", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.screen_shot("個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「特別児童扶養手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC060")

        # 8 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_8")

        # 9 特別児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 特別児童扶養手当資格管理画面: 申請種別「認定請求」選択申請理由「新規」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinsei_shubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinsei_riyuu)
        self.screen_shot("特別児童扶養手当資格管理画面_10")

        # 11 特別児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 特別児童扶養手当資格管理画面: 申請日「20230401」担当所管区「第一区」選択誓約有無「チェック」
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id(idstr="SeiyakuumuChkBox", value='1')
        self.screen_shot("特別児童扶養手当資格管理画面_12")

        # 13 特別児童扶養手当資格管理画面: 「児童追加」ボタン押下
        self.click_button_by_label("児童追加")

        # 14 世帯員検索画面: 表示
        self.screen_shot("世帯員検索画面_14")

        # 15 世帯員検索画面: 世帯員一覧「2」ボタン押下
        self.click_button_by_label("2")

        # 16 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_16")

        # 17 支給対象児童入力画面: 「手帳情報」ボタン押下
        self.click_button_by_label("手帳情報")

        # 18 手帳情報画面: 表示
        self.screen_shot("手帳情報画面_18")

        # 19 手帳情報画面: 「戻る」ボタン押下
        self.return_click()

        # 20 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_20")

        # 21 支給対象児童入力画面: 続柄「長男」を選択児童該当日「20230501」児童該当事由「児童増（身体障害者手帳）」選択児童同居別居区分「同居」チェック児童総合障害等級「１級」選択福祉行政報告例用障害分類「外部障害」選択児童障害分類「上肢の機能障害」選択児童有期認定年月「20241031」児童障害等級「１級」選択児童診断書様式「身体障害者手帳」選択
        self.form_input_by_id(idstr="CmbZokugara", text="長男")
        self.form_input_by_id(idstr="TxtGaitouYMD", value="20230501")
        self.form_input_by_id(idstr="CmbGaitouJiyu", text="児童増（身障手帳）")
        self.form_input_by_id(idstr="DoukyoBekkyoRBtn1", value="1")
        self.form_input_by_id(idstr="CmbShougaiToukyu", text="１級")
        self.form_input_by_id(idstr="CmbShougaiBunrui", text="外部障害")
        self.form_input_by_id(idstr="CmbByomei1", text="上肢の機能障害")
        self.form_input_by_id(idstr="TxtYuukiNinteiYMD1", value="20241031")
        self.form_input_by_id(idstr="CmbJidoShougaiToukyu1", text="1級")
        self.form_input_by_id(idstr="CmbJidoShindanshoYoshiki1", text="身体障害者手帳")
        self.screen_shot("支給対象児童入力画面_21")

        # 22 支給対象児童入力画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 23 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_23")

        # 24 特別児童扶養手当資格管理画面: 「福祉世帯情報」ボタン押下
        self.click_button_by_label("福祉世帯情報")

        # 25 福祉世帯情報画面: 表示
        self.screen_shot("福祉世帯情報画面_25")

        # 26 福祉世帯情報画面: 1に対して本人から見た続柄「本人」選択受給者との関係「本人」該当日「20230501」
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230501")

        # 27 福祉世帯情報画面: 2に対して本人から見た続柄「子」選択受給者との関係「対象児童」該当日「20230501」
        self.form_input_by_id(idstr="HoninCmb_2", text="子")
        self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20230501")
        self.screen_shot("福祉世帯情報画面_27")

        # 28 福祉世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 29 特別児童扶養手当資格管理画面: 「所得情報」ボタン押下
        self.click_button_by_label("所得情報")

        # 30 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_30")

        # 31 住民税世帯情報画面: 対象者世帯一覧「1」ボタン押下
        self.click_button_by_label("1")

        # 32 住民税個人情報画面: 表示
        self.screen_shot("住民税個人情報画面_32")

        # 33 住民税個人情報画面: 「戻る」ボタン押下
        self.return_click()

        # 34 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_34")

        # 35 住民税世帯情報画面: 「戻る」ボタン押下
        self.return_click()

        # 36 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_36")

        # 37 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_37")

        # 38 特別児童扶養手当資格管理画面: 「メモ情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("メモ情報")

        # 39 メモ情報画面: 表示
        self.screen_shot("メモ情報画面_39")

        # 40 メモ情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 41 メモ情報画面: 内容「あいうえおテスト」
        self.form_input_by_id(idstr="TxtNaiyo", value="あいうえおテスト")
        self.screen_shot("メモ情報画面_41")

        # 42 メモ情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 43 メモ情報画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("メモ情報画面_43")

        # 44 メモ情報画面: 「戻る」ボタン押下
        self.return_click()

        # 45 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_45")

        # 46 特別児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("提出書類管理")

        # 47 提出書類管理: 表示
        self.screen_shot("提出書類管理_47")

        # 48 提出書類管理: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 49 提出書類管理: 認定診断書にチェック
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value="1")
        self.screen_shot("提出書類管理_49")

        # 50 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 51 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_51")

        # 52 特別児童扶養手当資格管理画面: 「住所管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("住所管理")

        # 53 住所管理画面: 表示
        self.screen_shot("住所管理画面_53")

        # 54 住所管理画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 55 住所管理画面: 「住記情報索引」ボタン押下
        self.click_button_by_label("住記情報索引")
        self.screen_shot("住所管理画面_55")

        # 56 住所管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 57 住所管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_base_header("登録しました。")
        self.screen_shot("住所管理画面_57")

        # 58 住所管理画面: 「戻る」ボタン押下
        self.return_click()

        # 59 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_59")

        # 60 特別児童扶養手当資格管理画面: 「連絡先管理」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("連絡先管理")

        # 61 連絡先管理: 表示
        self.screen_shot("連絡先管理_61")

        # 62 連絡先管理: 本人連絡先の「追加」ボタン押下
        self.click_button_by_label("追加")

        # 63 連絡先管理: 優先順位「携帯電話番号」選択公開/非公開「公開」チェック自宅電話番号「************」携帯電話番号「090-1234-5678」
        self.form_input_by_id(idstr="CmbYusenTEL", text="携帯電話番号")
        self.form_input_by_id(idstr="kokai0", value="1")
        self.form_input_by_id(idstr="TxtTelJitaku", value="************")
        self.form_input_by_id(idstr="TxtTelKeitai", value="090-1234-5678")
        self.screen_shot("連絡先管理_63")

        # 64 連絡先管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 65 連絡先管理: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("連絡先管理_65")

        # 66 連絡先管理: 「戻る」ボタン押下
        self.return_click()

        # 67 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_67")

        # 68 特別児童扶養手当資格管理画面: 「口座情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("口座情報")

        # 69 口座情報画面: 表示
        self.screen_shot("口座情報画面_69")

        # 70 口座情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 71 口座情報画面: 有効期間開始「20230501」金融機関コード「0001」支店コード「001」口座種別「普通」選択口座番号「1234567」公開/非公開「公開」チェック
        self.entry_kouza_info(start_ymd="20230501", ginko_code="0001", shiten_code="001", kouza_shubetsu_text="普通", kouza_bango="1234567")
        self.screen_shot("口座情報画面_71")

        # 72 口座情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 73 口座情報画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("口座情報画面_73")

        # 74 口座情報画面: 「戻る」ボタン押下
        self.return_click()

        # 75 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_75")

        # 76 特別児童扶養手当資格管理画面: 開始年月「202305」
        self.form_input_by_id(idstr="TxtKaitei", value="202305")
        self.screen_shot("特別児童扶養手当資格管理画面_76")

        # 77 特別児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 78 特別児童扶養手当資格管理画面: 「所得判定詳細情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("所得判定詳細情報")

        # 79 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_79")

        # 80 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 81 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_81")

        # 82 特別児童扶養手当資格管理画面: 「住記情報」ボタン押下
        self.click_button_by_label("住記情報")

        # 83 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_83")

        # 84 世帯一覧画面: 世帯一覧「1」Noボタン押下
        self.click_button_by_label("1")

        # 85 住記情報: 表示
        self.screen_shot("住記情報_85")

        # 86 住記情報: 「戻る」ボタン押下
        self.return_click()

        # 87 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_87")

        # 88 世帯一覧画面: 「戻る」ボタン押下
        self.return_click()

        # 89 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_89")

        # # 90 特別児童扶養手当資格管理画面: 「介護保険情報」ボタン押下
        self.open_common_buttons_area()
        self.find_common_buttons_open_button().click()
        can_shintatsu_button = self.click_button_by_label("介護保険情報")
        if (can_shintatsu_button):
            self.click_button_by_label("介護保険情報")  # not sure because of the ID not found

            # # 91 介護保険情報画面: 表示
            self.screen_shot("介護保険情報画面_91")

            # # 92 介護保険情報画面: 「戻る」ボタン押下
            self.return_click()

        # # 93 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_93")

        # # 94 特別児童扶養手当資格管理画面: 「生活保護情報」ボタン押下
        self.open_common_buttons_area()
        self.find_common_buttons_open_button().click()
        can_shintatsu_button = self.click_button_by_label("生活保護情報")
        if (can_shintatsu_button):
            self.click_button_by_label("生活保護情報")  # not sure because of the ID not found

            # # 95 生活保護情報画面: 表示
            self.screen_shot("生活保護情報画面_95")

            # # 96 生活保護情報画面: 「戻る」ボタン押下
            self.return_click()

        # # 97 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_97")

        # # 98 特別児童扶養手当資格管理画面: 「保険情報」ボタン押下
        self.open_common_buttons_area()
        self.find_common_buttons_open_button().click()
        can_shintatsu_button = self.click_button_by_label("保険情報")
        if (can_shintatsu_button):
            self.click_button_by_label("保険情報")  # not sure because of the ID not found

            # # 99 保険情報一覧画面: 表示
            self.screen_shot("保険情報一覧画面_99")

            # # 100 保険情報一覧画面: 「戻る」ボタン押下
            self.return_click()

        # # 101 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_101")

        # # 102 特別児童扶養手当資格管理画面: 「年金情報登録」ボタン押下
        self.open_common_buttons_area()
        self.find_common_buttons_open_button().click()
        can_shintatsu_button = self.click_button_by_label("年金情報登録")
        if (can_shintatsu_button):
            self.click_button_by_label("年金情報登録")  # not sure because of the ID not found

            # # 103 年金情報画面: 表示
            self.screen_shot("年金情報画面_103")

            # # 104 年金情報画面: 「戻る」ボタン押下
            self.return_click()

        # # 105 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_105")

        # 106 特別児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 107 特別児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 108 特別児童扶養手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("特別児童扶養手当資格管理画面_108")
