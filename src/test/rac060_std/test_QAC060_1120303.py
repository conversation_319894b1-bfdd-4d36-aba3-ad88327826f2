import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC060_1120303(FukushiSiteTestCaseBase):
    """TestQAC060_1120303"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 所得状況届提出時の不足書類の督促通知が出力できることを確認する。（所得状況届未提出者も督促通知は出力される。）
    def test_QAC060_1120303(self):
        """不足書類督促通知の作成"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")  # not sure with self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：障害事業：特別児童扶養手当処理区分：年次処理処理分類：現況届未提出者督促処理
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="特別児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="年次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="現況届未提出者督促処理")
        self.screen_shot("バッチ起動画面_4")

        # 5 バッチ起動画面: 「現況届未提出督促対象者抽出処理」のNoボタン押下
        self.click_batch_job_button_by_label("現況届未提出督促対象者抽出処理")  # self.click_by_id("Sel1")

        # 6 バッチ起動画面: 現況年度「令和５年」選択発行年月日「20230702」提出期限年月日「20230731」出力順「証書番号順」選択
        params = [
            {"title": "現況年度", "type": "select", "value": "令和５年"},
            {"title": "選択発行年月日", "type": "text", "value": "20230702"},
            {"title": "提出期限年月日", "type": "text", "value": "20230731"},
            {"title": "出力順", "type": "select", "value": "証書番号順"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_6")

        # 7 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 8 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 9 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_9")

        # 10 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 11 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_11")

        # 12 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 13 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_13")

        # 14 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 15 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_15")

        # 16 ジョブ帳票履歴画面: 「現況届未提出督促者一覧」のNoボタン押下

        # 17 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 18 現況届未提出督促者一覧（PDF）: 表示
        self.screen_shot("現況届未提出督促者一覧（PDF）_19")

        # 19 現況届未提出督促者一覧（PDF）: ×ボタン押下でPDFを閉じる
        # TODO: I don't know which function to close the pdf tab

        # 20 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_20")

        # 21 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 22 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 23 バッチ起動画面: 「現況届督促状出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("現況届督促状出力処理")  # self.click_by_id("Sel2")

        # 24 バッチ起動画面: 出力区分「0」開始頁「000001」終了頁「000001」
        params = [
            {"title": "出力区分", "type": "text", "value": "0"},
            {"title": "開始頁", "type": "text", "value": "000001"},
            {"title": "終了頁", "type": "text", "value": "000001"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_24")

        # 25 バッチ起動画面: 「処理開始」ボタン押下
        self.exec_batch_job()

        # 26 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 27 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_27")

        # 28 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 29 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_29")

        # 30 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 31 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_31")

        # 32 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 33 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_33")

        # 34 ジョブ帳票履歴画面: 「特別児童扶養手当所得状況届督促通知書」のNoボタン押下

        # 35 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 36 特別児童扶養手当所得状況届督促通知書（PDF）: 表示
        self.screen_shot("特別児童扶養手当所得状況届督促通知PDF）_36")

        # 37 特別児童扶養手当所得状況届督促通知書（PDF）: ×ボタン押下でPDFを閉じる
        # TODO: I don't know which function to close the pdf tab

        # 38 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_38")
