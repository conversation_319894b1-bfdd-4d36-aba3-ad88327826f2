import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC060_1120507(FukushiSiteTestCaseBase):
    """TestQAC060_1120507"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 転出届が出力できることを確認する。
    def test_QAC060_1120507(self):
        """転出届出力"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        report_name = case_data.get("report_name", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.screen_shot("個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「特別児童扶養手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC060")

        # 8 特別児童扶養手当受給者台帳画面: 表示
        self.screen_shot("特別児童扶養手当受給者台帳画面_8")

        # 9 特別児童扶養手当受給者台帳画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 10 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_10")

        # 11 帳票印刷画面: 「特別児童扶養手当転出届」行の印刷チェックボックス選択
        self.print_online_reports(case_name="帳票印刷画面", report_name=report_name)
        self.screen_shot("帳票印刷画面_11")

        # 12 帳票印刷画面: 「印刷」ボタン押下
        # self.pdf_output_and_download(case_name="帳票印刷画面", button_id="InsatsuBtn")

        # 13 帳票印刷画面: 特別児童扶養手当転出届「ファイルを開く(O)」ボタンを押下
        # Assert: 「プレビューを表示しました」のメッセージチェック
        # self.assert_message_base_header("プレビューを表示しました")

        # 14 特別児童扶養手当転出届（PDF）: 表示
        # self.screen_shot("特別児童扶養手当転出届（PDF）_15")

        # 15 特別児童扶養手当転出届（PDF）: ×ボタン押下でPDFを閉じる

        # 16 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 17 特別児童扶養手当受給者台帳画面: 表示
        self.screen_shot("特別児童扶養手当受給者台帳画面_17")
