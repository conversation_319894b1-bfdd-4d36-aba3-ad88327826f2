import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC060_1120604(FukushiSiteTestCaseBase):
    """TestQAC060_1120604"""

    def setUp(self):
        case_data = self.test_data["TestQAC060_1120604"]
        super().setUp()

    # 関係書類提出書、額定通知書、証書、証書の交付について、証書受領印書  を出力できることを確認する。
    def test_QAC060_1120604(self):
        """額改定通知書等作成"""

        case_data = self.test_data["TestQAC060_1120604"]
        atena_code = case_data.get("atena_code", "")
        report_name_online_1 = case_data.get("report_name_online_1", "")
        report_name_online_2 = case_data.get("report_name_online_2", "")
        value_1 = case_data.get("value_1", "")
        value_3 = case_data.get("value_3", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC060")
        # 1 特別児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 資格管理画面: 表示
        self.screen_shot("資格管理画面_2")

        # 3 帳票印刷画面: 「特別児童扶養手当額改定通知書」行の印刷チェックボックス選択 | 「特別児童扶養手当額改定通知書」行の発行年月日チェックボックス選択
        # 「特別児童扶養手当額改定通知書」行の発行年月日「20230502」「特別児童扶養手当証書」行の印刷チェックボックス選択
        # 「特別児童扶養手当証書」行の発行年月日チェックボックス選択「特別児童扶養手当証書」行の発行年月日「20230502」
        exec_params = [
            {
                "report_name": report_name_online_1,
                "params":[
                    {"title": "発行年月日", "value":"20230502","is_no_print":"1"}
                ]
            },
            {
                "report_name": report_name_online_2,
                "params":[
                    {"title": "発行年月日", "value":"20230502","is_no_print":"1"}
                ]
            }
        ]
        self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_3")

        # 4 帳票印刷画面: 「印刷」ボタン押下

        # 5 帳票印刷画面: 特別児童扶養手当額改定通知書「ファイルを開く(O)」ボタンを押下
        # Assert: 「プレビューを表示しました」のメッセージチェック

        # 6 特別児童扶養手当額改定通知書（PDF）: 表示
        # self.screen_shot("特別児童扶養手当額改定通知書（PDF）_8")

        # 7 特別児童扶養手当額改定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 8 帳票印刷画面: 特別児童扶養手当証書「ファイルを開く(O)」ボタンを押下

        # 9 特別児童扶養手当証書（PDF）: 表示
        # self.screen_shot("特別児童扶養手当証書（PDF）_9")

        # 10 特別児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # # 11 帳票印刷画面: 「戻る」ボタン押下
        # self.return_click()

        # # 12 特別児童扶養手当資格管理画面: 表示
        # self.screen_shot("特別児童扶養手当資格管理画面_12")

        # 13 メインメニュー画面: 「バッチ起動」ボタン押下
        self.do_login()
        # self.click_button_by_label("バッチ起動")

        # # 14 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_14")

        # # 15 バッチ起動画面: 業務：障害事業：特別児童扶養手当処理区分：月次処理処理分類：受給者台帳
        # self.form_input_by_id(idstr="GyomuSelect", text="障害")
        # self.form_input_by_id(idstr="JigyoSelect", text="特別児童扶養手当")
        # self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        # self.form_input_by_id(idstr="ShoriBunruiSelect", text="受給者台帳")
        # self.screen_shot("バッチ起動画面_15")

        # # 16 バッチ起動画面: 「特別児童扶養手当関係書類提出書出力処理」のNoボタン押下
        # self.click_batch_job_button_by_label("特別児童扶養手当関係書類提出書出力処理")

        # # 17 バッチ起動画面: 開始申請日「20230501」終了申請日「20230501」
        # params = [
        #     {"title": "開始申請日", "type": "text", "value": "20230501"},
        #     {"title": "終了申請日", "type": "text", "value": "20230501"},
        # ]
        # self.set_job_params(params)
        # self.screen_shot("バッチ起動画面_17")

        # # 18 バッチ起動画面: 「処理開始」ボタン押下
        # exec_datetime = self.exec_batch_job()
        # self.assert_message_base_header("ジョブを起動しました")  # With Assert: 「ジョブを起動しました」のメッセージチェック

        # # 19 バッチ起動画面: 「実行履歴」ボタン押下
        # self.click_job_exec_log()

        # # 20 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_20")

        # # 21 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.wait_job_finished(120,20)
        # self.assert_job_normal_end(exec_datetime=exec_datetime)

        # # 22 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_22")

        # # 23 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        # self.click_report_log()

        # # 24 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_24")

        # # 25 バッチ帳票履歴: 「検索」ボタン押下
        # self.get_job_report_pdf(exec_datetime=exec_datetime)

        # # 26 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_26")

        # 27 バッチ帳票履歴: 「特別児童扶養手当関係書類提出書」のNoボタン押下

        # 28 バッチ帳票履歴: 「ファイルを開く」ボタン押下

        # 29 特別児童扶養手当関係書類提出書（PDF）: 表示
        # self.screen_shot("特別児童扶養手当関係書類提出書（PDF）_32")

        # 30 特別児童扶養手当関係書類提出書（PDF）: ×ボタン押下でPDFを閉じる

        # # 32 ジョブ帳票履歴画面: 「戻る」ボタン押下
        # self.return_click()

        # 33 メインメニュー画面: 「バッチ検索」ボタン押下
        self.click_button_by_label("バッチ検索")

        # 34 バッチ検索画面: ジョブID「RACJ4131」
        self.form_input_by_id(idstr="TxtJobID", value="RACJ4131")
        self.screen_shot("ジョブ検索画面_34")

        # 35 バッチ検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 36 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」宛名コード「」出力順「証書番号順」選択通知書区分「3」発行年月日「20230502」
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230502"},
            {"title": "終了決定日", "type": "text", "value": "20230502"},
            {"title": "宛名コード", "type": "text", "value": atena_code},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "通知書区分", "type": "text", "value": value_3},
            {"title": "発行年月日", "type": "text", "value": "20230502"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_36")

        # 37 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")  # With Assert: 「ジョブを起動しました」のメッセージチェック

        # 38 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 39 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_39")

        # 40 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 41 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_41")

        # 42 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 43 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_43")

        # 44 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 45 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_45")

        # 46 ジョブ帳票履歴画面: 「通知書対象者一覧」のNoボタン押下

        # 47 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 48 通知書対象者一覧（PDF）: 表示
        # self.screen_shot("通知書対象者一覧（PDF）_50")

        # 49 通知書対象者一覧（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("通知書対象者一覧（PDF）_51")

        # 50 ジョブ帳票履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 51 メインメニュー画面: 「バッチ検索」ボタン押下
        self.click_button_by_label("バッチ検索")

        # 52 バッチ検索画面: ジョブID「RACJ4132」
        self.form_input_by_id(idstr="TxtJobID", value="RACJ4132")
        self.screen_shot("ジョブ検索画面_52")

        # 53 バッチ起動画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 54 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」宛名コード「」出力順「証書番号順」選択通知書区分「1」発行年月日「20230502」
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230502"},
            {"title": "終了決定日", "type": "text", "value": "20230502"},
            {"title": "宛名コード", "type": "text", "value": atena_code},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "通知書区分", "type": "text", "value": value_1},
            {"title": "発行年月日", "type": "text", "value": "20230502"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_54")

        # 55 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")  # With Assert: 「ジョブを起動しました」のメッセージチェック

        # 56 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 57 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_57")

        # 58 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 59 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_59")

        # 60 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 61 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_61")

        # 62 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 63 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_63")

        # 64 ジョブ帳票履歴画面: 「通知書対象者一覧」のNoボタン押下

        # 65 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 66 通知書対象者一覧（PDF）: 表示
        # self.screen_shot("通知書対象者一覧（PDF）_68")

        # 67 通知書対象者一覧（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("通知書対象者一覧（PDF）_69")

        # 68 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 69 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 70 バッチ起動画面: 業務：障害事業：特別児童扶養手当処理区分：月次処理処理分類：通知書出力
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="特別児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="通知書出力")

        # 71 バッチ起動画面: 「額改定通知書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("額改定通知書出力処理")

        # 72 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」出力順「証書番号順」選択発行年月日「20230502」
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230502"},
            {"title": "終了決定日", "type": "text", "value": "20230502"},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "発行年月日", "type": "text", "value": "20230502"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_72")

        # 73 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")  # With Assert: 「ジョブを起動しました」のメッセージチェック

        # 74 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 75 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_75")

        # 76 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 77 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_77")

        # 78 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 79 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_79")

        # 80 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 81 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_81")

        # 82 ジョブ帳票履歴画面: 「特別児童扶養手当額改定通知書」のNoボタン押下

        # 83 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 84 特別児童扶養手当額改定通知書（PDF）: 表示
        # self.screen_shot("特別児童扶養手当額改定通知書（PDF）_86")

        # 85 特別児童扶養手当額改定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # # 86 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        # self.click_job_list()

        # # 87 バッチ起動画面: 「処理一覧」ボタン押下
        # self.click_job_exec_log_search()

        # # 88 バッチ起動画面: 業務：障害事業：特別児童扶養手当処理区分：月次処理処理分類：通知書出力
        # self.form_input_by_id(idstr="GyomuSelect", text="障害")
        # self.form_input_by_id(idstr="JigyoSelect", text="特別児童扶養手当")
        # self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        # self.form_input_by_id(idstr="ShoriBunruiSelect", text="通知書出力")

        # # 89 バッチ起動画面: 「証書出力処理」のNoボタン押下
        # self.click_batch_job_button_by_label("証書出力処理")

        # # 90 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」出力順「証書番号順」選択発行年月日「20230502」
        # params = [
        #     {"title": "開始決定日", "type": "text", "value": "20230502"},
        #     {"title": "終了決定日", "type": "text", "value": "20230502"},
        #     {"title": "出力順", "type": "select", "value": "証書番号順"},
        #     {"title": "発行年月日", "type": "text", "value": "20230502"}
        # ]
        # self.set_job_params(params)
        # self.screen_shot("バッチ起動画面_90")

        # # 91 バッチ起動画面: 「処理開始」ボタン押下
        # exec_datetime = self.exec_batch_job()
        # self.assert_message_base_header("ジョブを起動しました")  # With Assert: 「ジョブを起動しました」のメッセージチェック

        # # 92 バッチ起動画面: 「実行履歴」ボタン押下
        # self.click_job_exec_log()

        # # 93 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_93")

        # # 94 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.wait_job_finished(120,20)
        # self.assert_job_normal_end(exec_datetime=exec_datetime)

        # # 95 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_95")

        # # 96 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        # self.click_report_log()

        # # 97 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_97")

        # # 98 ジョブ帳票履歴画面: 「検索」ボタン押下
        # self.get_job_report_pdf(exec_datetime=exec_datetime)

        # # 99 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_99")

        # 100 ジョブ帳票履歴画面: 「特別児童扶養手当証書」のNoボタン押下

        # 101 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 102 特別児童扶養手当証書（PDF）: 表示
        # self.screen_shot("特別児童扶養手当証書（PDF）_104")

        # 103 特別児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # # 104 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        # self.click_job_list()

        # # 105 バッチ起動画面: 「処理一覧」ボタン押下
        # self.click_job_exec_log_search()

        # # 106 バッチ起動画面: 業務：障害事業：特別児童扶養手当処理区分：月次処理処理分類：通知書出力
        # self.form_input_by_id(idstr="GyomuSelect", text="障害")
        # self.form_input_by_id(idstr="JigyoSelect", text="特別児童扶養手当")
        # self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        # self.form_input_by_id(idstr="ShoriBunruiSelect", text="通知書出力")

        # # 107 バッチ起動画面: 「証書交付お知らせ出力処理」のNoボタン押下
        # self.click_batch_job_button_by_label("証書交付お知らせ出力処理")

        # # 108 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」宛名コード「」出力順「証書番号順」選択発行年月日「20230502」
        # params = [
        #     {"title": "開始決定日", "type": "text", "value": "20230502"},
        #     {"title": "終了決定日", "type": "text", "value": "20230502"},
        #     {"title": "宛名コード", "type": "text", "value": ""},
        #     {"title": "出力順", "type": "select", "value": "証書番号順"},
        #     {"title": "発行年月日", "type": "text", "value": "20230502"}
        # ]
        # self.set_job_params(params)
        # self.screen_shot("バッチ起動画面_108")

        # # 109 バッチ起動画面: 「処理開始」ボタン押下
        # exec_datetime = self.exec_batch_job()
        # self.assert_message_base_header("ジョブを起動しました")  # With Assert: 「ジョブを起動しました」のメッセージチェック

        # # 110 バッチ起動画面: 「実行履歴」ボタン押下
        # self.click_job_exec_log()

        # # 111 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_111")

        # # 112 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.wait_job_finished(120,20)
        # self.assert_job_normal_end(exec_datetime=exec_datetime)

        # # 113 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_113")

        # # 114 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        # self.click_report_log()

        # # 115 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_115")

        # # 116 ジョブ帳票履歴画面: 「検索」ボタン押下
        # self.get_job_report_pdf(exec_datetime=exec_datetime)

        # # 117 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_117")

        # 118 ジョブ帳票履歴画面: 「特別児童扶養手当証書の交付について」のNoボタン押下

        # 119 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 120 特別児童扶養手当証書の交付について（PDF）: 表示
        # self.screen_shot("特別児童扶養手当証書の交付について（PDF）_122")

        # 121 特別児童扶養手当証書の交付について（PDF）: ×ボタン押下でPDFを閉じる

        # # 122 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        # self.click_job_list()

        # # 123 バッチ起動画面: 「処理一覧」ボタン押下
        # self.click_job_exec_log_search()

        # # 124 バッチ起動画面: 業務：障害事業：特別児童扶養手当処理区分：月次処理処理分類：受給者台帳
        # self.form_input_by_id(idstr="GyomuSelect", text="障害")
        # self.form_input_by_id(idstr="JigyoSelect", text="特別児童扶養手当")
        # self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        # self.form_input_by_id(idstr="ShoriBunruiSelect", text="受給者台帳")

        # # 125 バッチ起動画面: 「特別児童扶養手当証書受領書出力処理」のNoボタン押下
        # self.click_batch_job_button_by_label("特別児童扶養手当証書受領書出力処理")

        # # 126 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」出力順「証書番号順」選択発行年月日「20230502」
        # params = [
        #     {"title": "開始決定日", "type": "text", "value": "20230502"},
        #     {"title": "終了決定日", "type": "text", "value": "20230502"},
        #     {"title": "出力順", "type": "select", "value": "証書番号順"},
        #     {"title": "発行年月日", "type": "text", "value": "20230502"}
        # ]
        # self.set_job_params(params)
        # self.screen_shot("バッチ起動画面_126")

        # # 127 バッチ起動画面: 「処理開始」ボタン押下
        # exec_datetime = self.exec_batch_job()
        # self.assert_message_base_header("ジョブを起動しました")  # With Assert: 「ジョブを起動しました」のメッセージチェック

        # # 128 バッチ起動画面: 「実行履歴」ボタン押下
        # self.click_job_exec_log()

        # # 129 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_129")

        # # 130 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.wait_job_finished(120,20)
        # self.assert_job_normal_end(exec_datetime=exec_datetime)

        # # 131 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_131")

        # # 132 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        # self.click_report_log()

        # # 133 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_133")

        # # 134 ジョブ帳票履歴画面: 「検索」ボタン押下
        # self.get_job_report_pdf(exec_datetime=exec_datetime)

        # # 135 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_135")

        # 136 ジョブ帳票履歴画面: 「特別児童扶養手当証書受領書」のNoボタン押下

        # 137 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 138 特別児童扶養手当証書受領書（PDF）: 表示
        # self.screen_shot("特別児童扶養手当証書受領書（PDF）_140")

        # 139 特別児童扶養手当証書受領書（PDF）: ×ボタン押下でPDFを閉じる

        # 140 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_140")
