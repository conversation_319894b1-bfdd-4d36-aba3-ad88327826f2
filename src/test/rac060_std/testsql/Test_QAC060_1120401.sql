/*
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC過払月額] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC過払情報] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC公的年金計算結果] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC差止履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC債権計画] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC債権計画月別] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC債権情報] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC債権返納] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC支払履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC支払履歴_児童数内訳] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
*/
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC資格履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
INSERT INTO WR$$JICHITAI_CODE$$QA.dbo.[QAC資格履歴]( [業務コード],[履歴番号],[履歴分類],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[申請年月日],[申請種別],[申請理由],[申請内容入力日],[進達年月日1],[進達判定年月日1],[進達結果1],[進達内容入力日1],[進達年月日2],[進達判定年月日2],[進達結果2],[進達内容入力日2],[決定年月日],[決定結果],[決定理由],[決定内容入力日],[業務固有コード1],[業務固有コード2],[業務固有コード3],[職権フラグ],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム] )
VALUES( '$$TARGET_GYOUMU_CODE$$',1575,0,'$$JICHITAI_CODE$$','00000','','$$DELETE_ATENA_CODE$$','20230110',1,1,'20240830','20230202','20230202',1,'20240830','00000000','00000000',0,'00000000','20230202',1,0,'20240830','1120370001','','','0','0','9501','9501','2024/08/30 21:37:16','2024/08/30 21:56:37','RACS005' )--//eor//--

DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC手当支給要件児童] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
INSERT INTO WR$$JICHITAI_CODE$$QA.dbo.[QAC手当支給要件児童]( [業務コード],[履歴番号],[児童宛名コード],[支給要件該当日1],[支給要件該当事由1],[支給要件該当日2],[支給要件該当事由2],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[児童生年月日],[算定対象児童内出生順],[支給要件非該当日1],[支給要件非該当事由1],[支給要件非該当日2],[支給要件非該当事由2],[同居別居の別],[別居区分],[監護の有無],[生計関係],[児童続柄],[児童変更フラグ],[支給要件区分],[支給要件発生日],[当初支給開始日],[減額開始年月],[養育開始日],[在学終了日],[再診日],[障害有無],[手当障害等級],[病名コード1],[有期認定日1],[審査区分1],[判定日1],[判定結果1],[病名コード2],[有期認定日2],[審査区分2],[判定日2],[判定結果2],[病名コード3],[有期認定日3],[審査区分3],[判定日3],[判定結果3],[未請求フラグ],[障害備考],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム] )
VALUES( '$$TARGET_GYOUMU_CODE$$',1575,'$$DELETE_ATENA_CODE2$$','20230201',2,'00000000',0,'$$JICHITAI_CODE$$','00000','','$$DELETE_ATENA_CODE$$','20200409',0,'99999999',0,'99999999',0,'0000000001','0000000000','0000000000','0000000000','0000000001','0','0000000000','00000000','00000000','000000','00000000','00000000','00000000','0',2,'0000000007','20240731','0000000000','00000000','0000000000','0000000000','00000000','0000000000','00000000','0000000000','0000000000','00000000','0000000000','00000000','0000000000','0','','0','9501','9501','2024/08/30 21:37:16','2024/08/30 21:37:16','RACS005' )--//eor//--

DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC児童備考] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
INSERT INTO WR$$JICHITAI_CODE$$QA.dbo.[QAC児童備考]( [業務コード],[履歴番号],[児童宛名コード],[支給要件該当日1],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[遺棄区分],[別居時期],[認知区分],[同居区分],[交際解消年月],[父宛名コード],[父名称],[父の状況],[父の状況終了年月日],[父障害コード],[父の生年月日],[母宛名コード],[母名称],[母の状況],[母の状況終了年月日],[母障害コード],[母の生年月日],[メモ欄],[備考1],[備考2],[備考3],[備考4],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム] )
VALUES( '$$TARGET_GYOUMU_CODE$$',1575,'$$DELETE_ATENA_CODE2$$','20230201','$$JICHITAI_CODE$$','00000','','$$DELETE_ATENA_CODE$$','0','000000','0','0','000000','00000000000','','0000000000','00000000','0000000000','00000000','00000000000','','0000000000','00000000','0000000000','00000000','','','','','','0','9501','9501','2024/08/30 21:37:16','2024/08/30 21:37:16','RACS005' )--//eor//--
/*
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC手当支給要件児童仮] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
*/
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC手当資格内容] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
INSERT INTO WR$$JICHITAI_CODE$$QA.dbo.[QAC手当資格内容]( [業務コード],[履歴番号],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[手当種別],[県費種別],[被用区分],[受給者区分],[支給区分],[所得判定対象者],[算定対象児童数],[算定対象児童数内訳1],[算定対象児童数内訳2],[算定対象児童数内訳3],[算定対象児童数内訳4],[算定対象児童数内訳5],[手当月額],[差引き額],[上乗せ額1],[上乗せ額2],[実支給月額],[手当月額内訳1],[手当月額内訳2],[手当月額内訳3],[手当月額内訳4],[手当月額内訳5],[開始_改定_終了],[当初支給開始日],[減額開始年月],[事由発生日],[証書記号],[証書返還チェック],[住登外区分],[世帯類型],[未支給_返還の別],[未支給請求者_債権者宛名コード],[未支給請求者の受給者との関係],[未支払手当支給決定結果],[進達時連絡項目],[災害特例該当],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム] )
VALUES( '$$TARGET_GYOUMU_CODE$$',1575,'$$JICHITAI_CODE$$','00000','','$$DELETE_ATENA_CODE$$','0000000000','0000000000','0000000000','0000000000','0000000011','0000000000',1,0,1,0,0,0,34900,0,0,0,34900,0,34900,0,0,0,'20230201','00000000','000000','00000000','','0','0','0000000000','0','000000000000000','0000000000','0000000000','0000000000','0','0','9501','9501','2024/08/30 21:37:16','2024/08/30 21:56:37','RACS005' )--//eor//--
/*
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC手当資格内容仮] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC手当障害審査情報] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC手当障害要件] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC住記行政欄データ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC障害者給付金] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC調整月額] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC適用除外] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC天引月別支払履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC天引月別支払履歴内訳] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC天引申請情報管理] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC天引申請情報児童] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC特記事項] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC汎用現況履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'

IF OBJECT_ID('[WR$$JICHITAI_CODE$$QA].dbo.[QAC被災状況]') IS NOT NULL
BEGIN
    DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC被災状況] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [本人宛名コード] = '$$DELETE_ATENA_CODE$$'
END

DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC不支給履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC別居監護申立申請情報] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ現況提出書類] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ提出書類内容] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ提出書類履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ所得税マスタ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ受給状況] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZメモ情報] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ福祉世帯] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [本人宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ帳票発行履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'

DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ連絡先] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ緊急連絡先] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ送付先マスタ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ居住地マスタ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ届出住所マスタ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ住所登録者マスタ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ口座マスタ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [固有コード] = '$$DELETE_ATENA_CODE$$' AND [区分] = '0000000001'
*/
