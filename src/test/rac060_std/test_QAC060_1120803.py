import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC060_1120803(FukushiSiteTestCaseBase):
    """TestQAC060_1120803"""

    def setUp(self):
        case_data = self.test_data["TestQAC060_1120803"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC060", "DELETE_ATENA_CODE": case_data.get("atena_code", ""),"DELETE_ATENA_CODE2": case_data.get("atena_code2", ""),
                      "TARGET_NENDO":case_data.get("nendo", ""),"TARGET_SOUSYOTOKU":case_data.get("soushotoku", "")}
        self.exec_sqlfile("Test_QAC060_1120803.sql", params=sql_params)
        super().setUp()

    # 在留期限到達者に対し、差止情報の一括登録が行えることを確認する。（差止通知書はシステム外）
    def test_QAC060_1120803(self):
        """在留期限到達者差止処理"""

        case_data = self.test_data["TestQAC060_1120803"]
        atena_code = case_data.get("atena_code", "")
        shinsei_shubetsu = case_data.get("shinsei_shubetsu")
        shinsei_riyuu = case_data.get("shinsei_riyuu")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「特別児童扶養手当」ボタン押下
        self.click_button_by_label("特別児童扶養手当")

        # 8 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_8")

        # 9 特別児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 特別児童扶養手当資格管理画面: 申請種別「認定請求」選択申請理由「新規」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinsei_shubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinsei_riyuu)

        # 11 特別児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 特別児童扶養手当資格管理画面: 申請日「20230110」担当所管区「第一区」選択誓約有無「チェック」
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230110")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        # self.form_input_by_id(idstr="SeiyakuumuChkBox", value=1)

        # 13 特別児童扶養手当資格管理画面: 「児童追加」ボタン押下
        self.click_button_by_label("児童追加")

        # 14 世帯員検索画面: 表示
        self.screen_shot("世帯員検索画面_14")

        # 15 世帯員検索画面: 世帯員一覧「2」ボタン押下
        self.click_by_id("Sel2")

        # 16 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_16")

        # 17 支給対象児童入力画面: 続柄「長男」を選択児童該当日「20230201」児童該当事由「児童増（身体障害者手帳）」選択児童同居別居区分「同居」チェック児童総合障害等級「２級」選択福祉行政報告例用障害分類「外部障害」選択児童障害分類「上肢の機能障害」選択児童有期認定年月「20250731」児童障害等級「２級」選択児童診断書様式「身体障害者手帳」選択
        self.form_input_by_id(idstr="CmbZokugara", text="長男")
        self.form_input_by_id(idstr="TxtGaitouYMD", value="20230201")
        self.form_input_by_id(idstr="CmbGaitouJiyu", text="児童増（身障手帳）")
        self.form_input_by_id(idstr="DoukyoBekkyoRBtn1", value=1)
        self.form_input_by_id(idstr="CmbShougaiToukyu", text="２級")
        self.form_input_by_id(idstr="CmbShougaiBunrui", text="外部障害")
        self.form_input_by_id(idstr="CmbByomei1", text="上肢の機能障害")
        self.form_input_by_id(idstr="TxtYuukiNinteiYMD1", value="20250731")
        self.form_input_by_id(idstr="CmbJidoShougaiToukyu1", text="2級")
        self.form_input_by_id(idstr="CmbJidoShindanshoYoshiki1", text="身体障害者手帳")

        # 18 支給対象児童入力画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 19 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_19")

        # 20 特別児童扶養手当資格管理画面: 「福祉世帯情報」ボタン押下
        self.click_button_by_label("福祉世帯情報")

        # 21 福祉世帯情報画面: 表示
        self.screen_shot("福祉世帯情報画面_21")

        # 22 福祉世帯情報画面: 1に対して本人から見た続柄「本人」選択受給者との関係「本人」該当日「20230201」
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230201")

        # 23 福祉世帯情報画面: 2に対して本人から見た続柄「子」選択受給者との関係「対象児童」該当日「20230201」
        self.form_input_by_id(idstr="HoninCmb_2", text="子")
        self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20230201")

        # 24 福祉世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 25 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_25")

        # 26 特別児童扶養手当資格管理画面: 開始年月「202302」
        self.form_input_by_id(idstr="TxtKaitei", value="202302")

        # 27 特別児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 28 特別児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 29 特別児童扶養手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("特別児童扶養手当資格管理画面_29")

        # 30 特別児童扶養手当資格管理画面: 「進達入力」ボタン押下
        can_shintatsu_button = self.click_button_by_label("市から県への進達入力")
        if (can_shintatsu_button):
            self.click_button_by_label("市から県への進達入力")

            # 31 特別児童扶養手当資格管理画面: 進達日「20230202」進達判定年月日「20230202」進達結果「該当」
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230202")

            # 32 特別児童扶養手当資格管理画面: 「月額計算」ボタン押下
            self.click_button_by_label("月額計算")

            # 33 特別児童扶養手当資格管理画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()

            # 34 特別児童扶養手当資格管理画面: 表示
            # Assert: 「登録しました」のメッセージチェック
            self.assert_message_area("登録しました")
            self.screen_shot("特別児童扶養手当資格管理画面_34")

            self.click_button_by_label("市から県への進達結果入力")
            self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230202")
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="該当")
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()

        # 35 特別児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 36 特別児童扶養手当資格管理画面: 判定日「20230202」判定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230202")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.form_input_by_id(idstr="TxtShoushoBango", value="0001120803")

        # 37 特別児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 38 特別児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 39 特別児童扶養手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("特別児童扶養手当資格管理画面_39")

        # 40 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_40")

        # # 41 メインメニュー画面: 「バッチ起動」ボタン押下
        # self.click_button_by_label("バッチ起動")

        # # 42 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_42")

        # # 43 バッチ起動画面: 業務：障害事業：特別児童扶養手当処理区分：年次処理処理分類：現況未提出者差止処理
        # self.form_input_by_id(idstr="GyomuSelect", text="障害")
        # self.form_input_by_id(idstr="JigyoSelect", text="特別児童扶養手当")
        # self.form_input_by_id(idstr="ShoriKubunSelect", text="年次処理")
        # self.form_input_by_id(idstr="ShoriBunruiSelect", text="現況未提出者差止処理")

        # # 44 バッチ起動画面: 「在留期限到達差止対象者抽出処理」のNoボタン押下
        # self.click_batch_job_button_by_label("在留期限到達差止対象者抽出処理")

        # # 45 バッチ起動画面: 在留基準年月日「20230731」発行年月日「20230702」
        # params = [
        #     {"title": "在留基準年月日", "type": "text", "value": "20230731"},
        #     {"title": "発行年月日", "type": "text", "value": "20230702"},
        # ]
        # self.set_job_params(params)
        # self.screen_shot("バッチ起動画面_45")

        # # 46 バッチ起動画面: 「処理開始」ボタン押下
        # exec_datetime = self.exec_batch_job()
        # self.assert_message_base_header("ジョブを起動しました")

        # # 47 バッチ起動画面: 「実行履歴」ボタン押下
        # self.click_job_exec_log()

        # # 48 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_48")

        # # 49 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.wait_job_finished(120,20)
        # self.assert_job_normal_end(exec_datetime=exec_datetime)

        # # 50 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_50")

        # # 51 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        # self.click_report_log()

        # # 52 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_52")

        # # 53 ジョブ帳票履歴画面: 「検索」ボタン押下
        # self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="在留期限到達差止対象者一覧")

        # # 54 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_54")

        # # 55 ジョブ帳票履歴画面: 「在留期限到達差止対象者一覧」のNoボタン押下

        # # 56 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # # 57 在留期限到達差止対象者一覧（PDF）: 表示
        # self.screen_shot("在留期限到達差止対象者一覧（PDF）_57")

        # # 58 在留期限到達差止対象者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # # 59 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_59")

        # # 60 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        # self.click_job_list()

        # # 61 バッチ起動画面: 「処理一覧」ボタン押下
        # self.click_job_exec_log_search()

        # # 62 バッチ起動画面: 業務：障害事業：特別児童扶養手当処理区分：年次処理処理分類：現況未提出者差止処理
        # self.form_input_by_id(idstr="GyomuSelect", text="障害")
        # self.form_input_by_id(idstr="JigyoSelect", text="特別児童扶養手当")
        # self.form_input_by_id(idstr="ShoriKubunSelect", text="年次処理")
        # self.form_input_by_id(idstr="ShoriBunruiSelect", text="現況未提出者差止処理")

        # # 63 バッチ起動画面: 「在留期限到達差止対象者更新処理」のNoボタン押下
        # self.click_batch_job_button_by_label("在留期限到達差止対象者更新処理")

        # # 64 バッチ起動画面: 「処理開始」ボタン押下
        # exec_datetime = self.exec_batch_job()
        # self.assert_message_base_header("ジョブを起動しました")

        # # 65 バッチ起動画面: 差止決定日「20230702」
        # params = [
        #     {"title": "差止決定日", "type": "text", "value": "20230702"},
        # ]
        # self.set_job_params(params)

        # # 66 バッチ起動画面: 「処理開始」ボタン押下
        # exec_datetime = self.exec_batch_job()
        # self.assert_message_base_header("ジョブを起動しました")

        # # 67 バッチ起動画面: 「実行履歴」ボタン押下
        # self.click_job_exec_log()

        # # 68 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_68")

        # # 69 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.wait_job_finished(120,20)
        # self.assert_job_normal_end(exec_datetime=exec_datetime)

        # # 70 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_70")

        # # 71 メインメニュー画面: 表示
        # self.do_login()
        # self.screen_shot("メインメニュー画面_71")

        # # 72 メインメニュー画面: 「申請資格管理」ボタン押下
        # self.shinsei_shikaku_kanri_click()

        # # 73 個人検索画面: 表示
        # self.screen_shot("個人検索画面_73")

        # # 74 個人検索画面: 「住民コード」入力

        # # 75 個人検索画面: 「検索」ボタン押下
        # self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # # 76 受給状況画面: 表示
        # self.screen_shot("受給状況画面_76")

        # # 77 受給状況画面: 「特別児童扶養手当」ボタン押下
        # self.click_button_by_label("特別児童扶養手当")

        # # 78 特別児童扶養手当資格管理画面: 表示
        # self.screen_shot("特別児童扶養手当資格管理画面_78")

        # # 79 特別児童扶養手当資格管理画面: 「差止情報」ボタン押下
        # self.click_button_by_label("差止情報")

        # # 80 差止情報画面: 表示
        # self.screen_shot("差止情報画面_80")
