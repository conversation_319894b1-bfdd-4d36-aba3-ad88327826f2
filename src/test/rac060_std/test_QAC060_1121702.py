import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC060_1121702(FukushiSiteTestCaseBase):
    """TestQAC060_1121702"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 以下のEUCが正しく抽出できることを確認する。・特別児童扶養手当決定情報・特別児童扶養手当決定児童情報・特別児童扶養手当決定児童障害分類情報・特別児童扶養手当支給情報・特別児童扶養手当所得判定情報
    def test_QAC060_1121702(self):
        """必要に応じて業務データの抽出・確認_基本データリスト分_"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 EUC画面: 表示
        self.transit_euc_std()
        self.screen_shot("EUC画面_2")

        # 2 EUC画面: テーブル参照「特別児童扶養手当決定情報」ダブルクリック
        self.get_euc_table(table_name="特別児童扶養手当決定情報")

        # 3 EUC_特別児童扶養手当決定情報タグ画面: 表示

        # 4 EUC_特別児童扶養手当決定情報タグ画面: 「検索」ボタン押下

        # 5 [結果]EUC_特別児童扶養手当決定情報タグ画面: 表示

        # 6 [結果]EUC_特別児童扶養手当決定情報タグ画面: ×ボタン押下で閉じる

        # 7 EUC_特別児童扶養手当決定情報タグ画面: 表示

        # 8 EUC_特別児童扶養手当決定情報タグ画面: ×ボタン押下で閉じる

        # 9 EUC画面: 表示
        self.screen_shot("EUC画面_9")

        # 10 EUC画面: テーブル参照「特別児童扶養手当決定児童情報」ダブルクリック
        self.get_euc_table(table_name="特別児童扶養手当決定児童情報")

        # 11 EUC_特別児童扶養手当決定児童情報タグ画面: 表示

        # 12 EUC_特別児童扶養手当決定児童情報タグ画面: 「検索」ボタン押下

        # 13 [結果]EUC_特別児童扶養手当決定児童情報タグ画面: 表示

        # 14 [結果]EUC_特別児童扶養手当決定児童情報タグ画面: ×ボタン押下で閉じる

        # 15 EUC_特別児童扶養手当決定児童情報タグ画面: 表示

        # 16 EUC_特別児童扶養手当決定児童情報タグ画面: ×ボタン押下で閉じる

        # 17 EUC画面: 表示
        self.screen_shot("EUC画面_17")

        # 18 EUC画面: テーブル参照「特別児童扶養手当決定児童障害分類情報」ダブルクリック
        self.get_euc_table(table_name="特別児童扶養手当決定児童障害分類情報")

        # 19 EUC_特別児童扶養手当決定児童障害分類情報タグ画面: 表示

        # 20 EUC_特別児童扶養手当決定児童障害分類情報タグ画面: 「検索」ボタン押下

        # 21 [結果]EUC_特別児童扶養手当決定児童障害分類情報タグ画面: 表示

        # 22 [結果]EUC_特別児童扶養手当決定児童障害分類情報タグ画面: ×ボタン押下で閉じる

        # 23 EUC_特別児童扶養手当決定児童障害分類情報タグ画面: 表示

        # 24 EUC_特別児童扶養手当決定児童障害分類情報タグ画面: ×ボタン押下で閉じる

        # 25 EUC画面: 表示
        self.screen_shot("EUC画面_25")

        # 26 EUC画面: テーブル参照「特別児童扶養手当支給情報」ダブルクリック
        self.get_euc_table(table_name="特別児童扶養手当支給情報")

        # 27 EUC_特別児童扶養手当支給情報タグ画面: 表示

        # 28 EUC_特別児童扶養手当支給情報タグ画面: 「検索」ボタン押下

        # 29 [結果]EUC_特別児童扶養手当支給情報タグ画面: 表示

        # 30 [結果]EUC_特別児童扶養手当支給情報タグ画面: ×ボタン押下で閉じる

        # 31 EUC_特別児童扶養手当支給情報タグ画面: 表示

        # 32 EUC_特別児童扶養手当支給情報タグ画面: ×ボタン押下で閉じる

        # 33 EUC画面: 表示
        self.screen_shot("EUC画面_33")

        # 34 EUC画面: テーブル参照「特別児童扶養手当所得判定情報」ダブルクリック
        self.get_euc_table(table_name="特別児童扶養手当所得判定情報")

        # 35 EUC_特別児童扶養手当所得判定情報タグ画面: 表示

        # 36 EUC_特別児童扶養手当所得判定情報タグ画面: 「検索」ボタン押下

        # 37 [結果]EUC_特別児童扶養手当所得判定情報タグ画面: 表示

        # 38 [結果]EUC_特別児童扶養手当所得判定情報タグ画面: ×ボタン押下で閉じる

        # 39 EUC_特別児童扶養手当所得判定情報タグ画面: 表示

        # 40 EUC_特別児童扶養手当所得判定情報タグ画面: ×ボタン押下で閉じる

        # 41 EUC画面: 表示
        self.screen_shot("EUC画面_41")
