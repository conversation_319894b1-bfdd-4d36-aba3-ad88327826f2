import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC060_1120806(FukushiSiteTestCaseBase):
    """TestQAC060_1120806"""

    def setUp(self):
        case_data = self.test_data["TestQAC060_1120806"]
        super().setUp()

    # 不支給情報を登録し、支払予定が変更されていることを確認する。
    def test_QAC060_1120806(self):
        """不支給情報の登録"""

        case_data = self.test_data["TestQAC060_1120806"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 5 個人検索画面: 「検索」ボタン押下

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「特別児童扶養手当」ボタン押下
        self.click_button_by_label("特別児童扶養手当")

        # 8 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_8")

        # 9 特別児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 10 特別児童扶養手当資格管理画面: 「不支給情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("不支給情報")

        # 11 不支給情報画面: 表示
        self.screen_shot("不支給情報画面_11")

        # 12 不支給情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 13 不支給情報画面: 表示
        self.screen_shot("不支給情報画面_13")

        # 14 不支給情報画面: 不支給理由「その他」不支給開始月「202308」不支給終了月「202308」不支給決定年月日「20230810」
        self.form_input_by_id(idstr="CmbFRiyu", text="その他")
        self.form_input_by_id(idstr="TxtFStartYM", value=202308)
        self.form_input_by_id(idstr="TxtFEndYM", value=202308)
        self.form_input_by_id(idstr="TxtFKetteiYMD", value=20230810)
        self.screen_shot("不支給情報画面_14")

        # 15 不支給情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 16 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("不支給情報画面_16")

        # 17 特別児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 18 特別児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 19 特別児童扶養手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("特別児童扶養手当資格管理画面_19")

        # 20 特別児童扶養手当資格管理画面: 「支払履歴」ボタン押下
        self.click_button_by_label("支払履歴")

        # 21 支払履歴画面: 表示
        self.screen_shot("支払履歴画面_21")

        # 22 支払履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 23 特別児童扶養手当資格管理画面: 表示
        self.screen_shot("特別児童扶養手当資格管理画面_23")
