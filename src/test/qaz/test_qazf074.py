import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select


class Test_QAZF074(FukushiSiteTestCaseBase):
    """Test_QAZF074"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAZF074_実行前スクリプト.sql", params=atena_list) 
        super().setUp()
        
    def test_case_001(self):
        """test_case_001"""
        self.do_login()
        self.click_button_by_label("レセプト(標準外)")
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '障害者医療']").click()
        self.driver.find_element(By.ID, "TxtKensakuYMStart").click()
        self.driver.find_element(By.ID, "TxtKensakuYMStart").send_keys("令和04年01月")
        self.driver.find_element(By.ID, "TxtJukyushaNoStart").click()
        self.driver.find_element(By.ID, "TxtIryoKikanCd").click()
        self.driver.find_element(By.ID, "TxtIryoKikanCd").send_keys("2000000001")
        self.driver.find_element(By.ID, "CmdKensaku").click()
        self.driver.find_element(By.ID, "1").click()
        self.screen_shot("QAZF074_1", caption="QAZF074_初期表示")

        # 更新テスト
        self.driver.find_element(By.ID, "TxtJukyushaShimei").click()
        self.driver.find_element(By.ID, "TxtJukyushaShimei").send_keys("公共　劶㐬")
        self.driver.find_element(By.ID, "CmdToroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF074_2", caption="QAZF074_更新_登録")
        self.find_element(By.ID,"GOBACK").click()
        
        # 新規追加テスト
        self.driver.find_element(By.ID, "CmdTsuika").click()
        self.screen_shot("QAZF074_3", caption="QAZF074_初期表示_追加")

        self.driver.find_element(By.ID, "TxtSeikyuYM").click()
        self.driver.find_element(By.ID, "TxtSeikyuYM").send_keys("令和05年01月")
        self.driver.find_element(By.ID, "TxtShinryoYM").click()
        self.driver.find_element(By.ID, "TxtShinryoYM").send_keys("令和04年12月")
        self.driver.find_element(By.ID, "TxtJukyushaNo").click()
        self.driver.find_element(By.ID, "TxtJukyushaNo").send_keys("9990007")
        self.driver.find_element(By.ID, "CmdJukyushaNo").click()
        self.driver.find_element(By.ID, "TxtIryoKikanCd").click()
        self.driver.find_element(By.ID, "TxtIryoKikanCd").send_keys("0110000001")
        self.driver.find_element(By.ID, "CmdIryoKikan").click()
        self.driver.find_element(By.ID, "TxtZikoFutanWariai").click()
        self.driver.find_element(By.ID, "TxtZikoFutanWariai").send_keys("30")
        time.sleep(1)
        self.driver.find_element(By.ID, "CmdToroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF074_4", caption="QAZF074_追加_登録")
        self.find_element(By.ID,"GOBACK").click()
        
        self.driver.find_element(By.ID, "span_CmdTsuika").click()
        self.driver.find_element(By.ID, "TxtSeikyuYM").click()
        self.driver.find_element(By.ID, "TxtSeikyuYM").send_keys("令和05年01月")
        self.driver.find_element(By.ID, "span_CmdShokiHyoji").click()
        self.screen_shot("QAZF074_5", caption="QAZF074_初期表示")
        self.driver.find_element(By.ID, "span_CmdHokensha").click()
        self.screen_shot("QAZF074_6", caption="QAZF074_保険者")