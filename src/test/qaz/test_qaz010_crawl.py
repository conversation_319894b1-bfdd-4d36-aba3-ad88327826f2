import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAZ010_JUKYUJOUKYOU_CRAWL(FukushiSiteTestCaseBase):
    """TESTQAZ010_JUKYUJOUKYOU_CRAWL"""

    def test_case_001(self):
        """受給状況クロール"""

        test_data = self.common_test_data
        atena_code = test_data.get("jukyu_joukyou_crawl_atena_code","0")
        self.jukyu_joukyou_crawl(atena_code)
