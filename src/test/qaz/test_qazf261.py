import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class Test_QAZF261(FukushiSiteTestCaseBase):
    """Test_QAZF261"""

    def test_case_001(self):
        """test_case_001"""
        driver = None
        # test_data = self.common_test_data
        
        self.do_login()
        
        self.click_button_by_label("マスタメンテナンス")
        self.click_button_by_label("指定医療情報メンテナンス")
        self.screen_shot("QAZF261_1",caption="QAZF261_指定医療情報メンテナンス")
        self.driver.find_element(By.ID, "KijunYMD").click()
        self.driver.find_element(By.ID, "KijunYMD").send_keys("令和02年01月01日")
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '身体障害者手帳']").click()
        self.driver.find_element(By.ID, "span_KakuteiBtn").click()
        self.driver.find_element(By.ID, "span_KensakuBtn").click()
        
        self.driver.find_element(By.ID, "span_KaijoBtn").click()
        self.driver.find_element(By.ID, "KijunYMD").click()
        self.driver.find_element(By.ID, "KijunYMD").send_keys("令和02年01月01日")
        self.driver.find_element(By.CSS_SELECTOR, ".wr_table:nth-child(4)").click()
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '自立支援医療(更生)']").click()
        self.driver.find_element(By.ID, "span_KakuteiBtn").click()
        self.driver.find_element(By.ID, "span_KensakuBtn").click()
        self.driver.find_element(By.ID, "TxtNameKana").click()
        self.driver.find_element(By.ID, "TxtNameKana").send_keys("1")
        self.driver.find_element(By.ID, "span_ShokiBtn").click()
        self.screen_shot("QAZF261_2",caption="QAZF261_初期表示ボタン押下")
        
        self.driver.find_element(By.CSS_SELECTOR, "tr > .wr_button_layout").click()
        self.driver.find_element(By.ID, "span_KensakuBtn").click()
        self.screen_shot("QAZF261_3",caption="QAZF261_検索ボタン押下")
        
        self.driver.find_element(By.CSS_SELECTOR, ".wr_table_title:nth-child(2) th").click()
        self.driver.find_element(By.ID, "CmdSortIryoCode").click()
        self.screen_shot("QAZF261_4",caption="QAZF261_ソートボタン押下")
        
        self.driver.find_element(By.ID, "CmdSortIryoCode").click()
        self.screen_shot("QAZF261_5",caption="QAZF261_ソートボタン押下")
        
        self.driver.find_element(By.ID, "span_CmdNextPage").click()
        self.screen_shot("QAZF261_6",caption="QAZF261_次頁ボタン押下")
        
        self.driver.find_element(By.ID, "span_CmdBackPage").click()
        self.screen_shot("QAZF261_7",caption="QAZF261_前頁ボタン押下")
        
        self.driver.find_element(By.ID, "CmbPage").click()
        dropdown = self.driver.find_element(By.ID, "CmbPage")
        dropdown.find_element(By.XPATH, "//option[. = '02']").click()
        self.driver.find_element(By.ID, "span_CmdJumpPage").click()
        self.screen_shot("QAZF261_8",caption="QAZF261_へ移動ボタン押下")
        
        self.driver.find_element(By.ID, "CmbPage").click()
        dropdown = self.driver.find_element(By.ID, "CmbPage")
        dropdown.find_element(By.XPATH, "//option[. = '01']").click()
        self.driver.find_element(By.ID, "span_CmdJumpPage").click()
        self.screen_shot("QAZF261_9",caption="QAZF261_へ移動ボタン押下")
        
