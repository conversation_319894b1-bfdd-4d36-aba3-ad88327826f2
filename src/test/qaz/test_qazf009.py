import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class Test_QAZF009(FukushiSiteTestCaseBase):
    """Test_QAZF009"""
    def test_case_001(self):
        """test_case_001"""        
        # driver = None
        test_data = self.common_test_data        
        self.do_login()
        self.click_button_by_label("申請資格管理")
        self.find_element(By.ID, "AtenaCD").click()
        self.find_element(By.ID, "AtenaCD").send_keys(test_data.get("qazf009_atena_code"))
        self.find_element(By.ID, "span_Kensaku").click()
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="世帯員検索") 
        self.find_element(By.ID, "span_CmdJusho").click()
        self.find_element(By.ID, "span_Sel1").click()
        self.screen_shot("QAZF009_1",caption="QAZF009_画面初期化")
        self.find_element(By.ID, "CmdTansitRenkeiData").click()
        self.screen_shot("QAZF009_2",caption="QAZF009_「連携データ表示」ボタン押下")
        self.find_element(By.ID, "GOBACK").click()
        self.screen_shot("QAZF009_4",caption="QAZF009_「戻る」ボタン押下")
        self.find_element(By.ID, "span_CmdInput").click()
        self.screen_shot("QAZF009_5",caption="QAZF009_「福祉独自項目入力」ボタン押下")
        self.find_element(By.ID, "GOBACK").click()
        self.screen_shot("QAZF009_6",caption="QAZF009_「戻る」ボタン押下")
        self.find_element(By.ID, "CmdChuiKubun").click()
        self.screen_shot("QAZF009_7",caption="QAZF009_「注意区分入力」ボタン押下")
        self.find_element(By.ID, "GOBACK").click()
        self.screen_shot("QAZF009_8",caption="QAZF009_「戻る」ボタン押下")
        self.find_element(By.ID, "CmdKokennin").click()
        self.screen_shot("QAZF009_9",caption="QAZF009_「成年後見人入力」ボタン押下")
        self.find_element(By.ID, "GOBACK").click()
        self.screen_shot("QAZF009_10",caption="QAZF009_「戻る」ボタン押下")
        self.find_element(By.ID, "CmdNextPage").click()
        self.screen_shot("QAZF009_11",caption="QAZF009_「次頁」ボタン押下")
        self.find_element(By.ID, "CmdBackPage").click()
        self.screen_shot("QAZF009_12",caption="QAZF009_「前頁」ボタン押下")
        self.find_element(By.ID, "CmbPage").click()
        dropdown = self.find_element(By.ID, "CmbPage")
        dropdown.find_element(By.XPATH, "//option[. = '02']").click()
        self.find_element(By.ID, "CmdJumpPage").click()
        self.screen_shot("QAZF009_13",caption="QAZF009_「へ移動」ボタン押下")
       