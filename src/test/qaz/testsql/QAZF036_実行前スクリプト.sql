

USE WR$$JICHITAI_CODE$$QZ

delete from QZ居住地マスタ
where 宛名コード =  '$$QAZF036_ATENA_CODE$$';

delete  from QZ送付先マスタ
where 宛名コード =  '$$QAZF036_ATENA_CODE$$';

-- INSERT INTO [QZ送付先マスタ]
--            ([自治体コード]
--            ,[業務コード]
--            ,[宛名コード]
--            ,[関係者宛名コード]
--            ,[用途]
--            ,[有効期間開始]
--            ,[有効期間終了]
--            ,[送付先郵便番号1]
--            ,[送付先郵便番号2]
--            ,[送付先市内住所コード]
--            ,[送付先市内番地]
--            ,[送付先市内号]
--            ,[送付先市内号枝番]
--            ,[送付先市内号小枝番]
--            ,[送付先市外住所コード]
--            ,[送付先住所1]
--            ,[送付先住所2]
--            ,[送付先住所1_市区町村コード]
--            ,[送付先住所1_町字コード]
--            ,[送付先住所1_都道府県]
--            ,[送付先住所1_市区郡町村名]
--            ,[送付先住所1_町字]
--            ,[送付先住所1_番地号表記]
--            ,[送付先住所2_方書コード]
--            ,[送付先住所2_方書カナ]
--            ,[送付先カナ氏名]
--            ,[送付先カナ氏名_氏]
--            ,[送付先カナ氏名_名]
--            ,[送付先氏名]
--            ,[送付先氏名_氏]
--            ,[送付先氏名_名]
--            ,[送付先続柄コード]
--            ,[送付先続柄漢字]
--            ,[送付先電話番号]
--            ,[非公開フラグ]
--            ,[登録事由]
--            ,[登録事由テキスト]
--            ,[備考]
--            ,[削除フラグ]
--            ,[データ作成担当者]
--            ,[データ更新担当者]
--            ,[データ作成日時]
--            ,[データ更新日時]
--            ,[データ更新プログラム])
--      VALUES
--            ('99101'
--            ,'QAR010'
--            ,'000000000180001'
--            ,''           
--            ,'0000000000'
--            ,'20230605'
--            ,'20230605'
--            ,'312'
--            ,'1111'
--            ,''
--            ,''
--            ,''
--            ,''
--            ,''
--            ,''
--            ,N'住所テスト１２'
--            ,''
--            ,''
--            ,''
--            ,''
--            ,''
--            ,''
--            ,''
--            ,''
--            ,''
--            ,'ﾃｽﾄ'
--            ,''
--            ,''
--            ,'テスト'
--            ,''
--            ,''
--            ,''
--            ,''
--            ,''
--            ,'0'
--            ,'99101'
--            ,'99101'
--            ,'99101'
--            ,'0'
-- ,'INES','INES',getdate(),getdate(),'00000000');

-- INSERT INTO [QZ居住地マスタ]([自治体コード],[業務コード],[宛名コード],[関係者宛名コード],[用途],[有効期間開始],[有効期間終了],[居住地郵便番号1],[居住地郵便番号2],[居住地市内住所コード],[居住地市内番地],[居住地市内号] ,[居住地市内号枝番],[居住地市内号小枝番],[居住地市外住所コード],[居住地1],[居住地2],[居住地住所1_市区町村コード],[居住地住所1_町字コード],[居住地住所1_都道府県],[居住地住所1_市区郡町村名]
-- ,[居住地住所1_町字],[居住地住所1_番地号表記],[居住地住所2_方書コード],[居住地住所2_方書カナ],[居住地カナ氏名],[居住地カナ氏名_氏],[居住地カナ氏名_名],[居住地漢字氏名],[居住地氏名_氏],[居住地氏名_名],[非公開フラグ],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
--      VALUES
--            ('99101'
--            ,'QAR010'
--            ,'000000000180001'
--            ,''
--            ,'0000000000'
--            ,'20230207'
--            ,'20230207'
--            ,'999'
--            ,'9999'
--            ,'00100'
--            ,'00001'
--            ,'00002'
--            ,'00003'
--            ,''
--            ,''
--            ,N'福祉県福祉市福祉町１－２－３'
--            ,''
--            ,''
--            ,''
--            ,''
--            ,''
--            ,''
--            ,''
--            ,''
--            ,''
--            ,N'ｷｮｳﾂｳ ﾀﾛｳ1'
--            ,''
--            ,''
--            ,'共通　太郎１'
--            ,''
--            ,''
--            ,'0'
--            ,'0'
-- ,'INES','INES',getdate(),getdate(),'00000000')