import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver import ActionChains

class Test_QAZF273(FukushiSiteTestCaseBase):
    """Test_QAZF273"""
    
    def test_case_001(self):
        """test_case_001"""
        driver = None
        test_data = self.common_test_data
        
        self.do_login()
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=test_data.get("qazf273_atena_code"), gyoumu_code="QAJ010")       
        self.driver.find_element(By.NAME, "img").click()
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="年金照会先(療養介護)")   
        self.screen_shot("QAZF273_1",caption="QAZF273_初期表示")

        self.driver.find_element(By.ID, "span_CmdTsuika").click()
        self.driver.find_element(By.ID, "ShoriCmb_Insert_1").click()
        dropdown = self.driver.find_element(By.ID, "ShoriCmb_Insert_1")
        dropdown.find_element(By.XPATH, "//option[. = '追加']").click()
        self.driver.find_element(By.ID, "TxtShokai_Insert_1").click()
        self.driver.find_element(By.ID, "TxtShokai_Insert_1").send_keys("全国市町村職員共済組合連合会")
        self.driver.find_element(By.ID, "CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF273_2",caption="QAZF273_追加")

        self.driver.find_element(By.ID, "span_CmdShusei").click()
        time.sleep(10)
        self.driver.find_element(By.ID, "ShoriCmb_Update_1").click()
        dropdown = self.driver.find_element(By.ID, "ShoriCmb_Update_1")
        dropdown.find_element(By.XPATH, "//option[. = '修正']").click()
        self.driver.find_element(By.ID, "TxtShokai_Update_1").click()
        self.driver.find_element(By.ID, "TxtShokai_Update_1").send_keys("東京都職員共済組合")
        self.driver.find_element(By.ID, "span_CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF273_3",caption="QAZF273_更新")

        self.driver.find_element(By.ID, "span_CmdShusei").click()
        self.driver.find_element(By.ID, "ShoriCmb_Update_1").click()
        dropdown = self.driver.find_element(By.ID, "ShoriCmb_Update_1")
        dropdown.find_element(By.XPATH, "//option[. = '削除']").click()
        self.driver.find_element(By.ID, "span_CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF273_4",caption="QAZF273_削除")

        self.driver.find_element(By.ID, "CmdTsuika").click()
        self.driver.find_element(By.ID, "span_CmdKojin").click()
        self.screen_shot("QAZF273_5",caption="QAZF273_「個人追加」ボタン押下")

        self.driver.find_element(By.ID, "Sel1").click()
        self.screen_shot("QAZF273_6",caption="QAZF273_「世帯員一覧のNO1データ」押下")

        self.driver.find_element(By.ID, "ShoriCmb_Insert_1").click()
        dropdown = self.driver.find_element(By.ID, "ShoriCmb_Insert_1")
        dropdown.find_element(By.XPATH, "//option[. = '追加']").click()
        self.driver.find_element(By.ID, "SelectKikanMaster").click()
        element = self.driver.find_element(By.ID, "span_CmdSettei")
        actions = ActionChains(self.driver)
        actions.move_to_element(element).perform()
        self.driver.find_element(By.ID, "SelectKikanMaster").send_keys("全国市町村職員共済組合連合会")
        self.driver.find_element(By.ID, "span_CmdSettei").click()
        self.screen_shot("QAZF273_7",caption="QAZF273_「一括設定」ボタン押下")

        self.driver.find_element(By.ID, "span_CmdShoki").click()    
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.screen_shot("QAZF273_8",caption="QAZF273_「初期表示」押下")
