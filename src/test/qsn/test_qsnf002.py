import time
import unittest
import os
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class Test_QSNF002(FukushiSiteTestCaseBase):
    """Test_QSNF002"""
    
    def test_case_001(self):
        """test_case_001"""
        driver = None
        test_folder_path = os.path.join(self.test_data_root_path, "test_qsnf002")        
        self.do_login()
   
        self.click_button_by_label("収納管理")
        self.save_screenshot_migrate(driver, "QSNF002-001-02", True)

        self.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '高齢']").click()
        self.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '養護老人ホーム入所']").click()
        self.find_element(By.ID, "span_CmdKakutei").click()
        self.save_screenshot_migrate(driver, "QSNF002-001-06", True)

        self.find_element(By.ID, "span_CmdButton12").click()        
        self.find_element(By.ID, "span_CmdFileUpLoad").click()
        self.save_screenshot_migrate(driver, "QSNF002-001-09", True)

        self.fukushi_folder_upload_qsnf002(test_folder_path)
        
        self.find_element(By.ID, "CmdNextPage").click()
        self.save_screenshot_migrate(driver, "QSNF002-001-11", True)

        self.find_element(By.ID, "CmdBackPage").click()
        self.save_screenshot_migrate(driver, "QSNF002-001-13", True)

        self.find_element(By.ID, "CmbPage").click()
        dropdown = self.find_element(By.ID, "CmbPage")
        dropdown.find_element(By.XPATH, "//option[. = '02']").click()
        self.find_element(By.ID, "CmdJumpPage").click()
        self.save_screenshot_migrate(driver, "QSNF002-001-16", True)

        self.find_element(By.ID, "span_CmdAllSelect").click()
        self.assertEqual(u"全てのファイルを削除対象にします。よろしいですか？", self.alert_ok())        
        self.save_screenshot_migrate(driver, "QSNF002-001-18", True)
        self.find_element(By.ID, "span_CmdAllUnSelect").click()
        self.assertEqual(u"全てのファイルを削除対象から除外します。よろしいですか？", self.alert_ok())        
        self.save_screenshot_migrate(driver, "QSNF002-001-20", True)

        self.find_element(By.ID, "span_CmdShoki").click()
        self.save_screenshot_migrate(driver, "QSNF002-001-22", True)

        self.find_element(By.ID, "ChkSakujo_1").click()
        self.find_element(By.ID, "span_CmdFileSakujo").click()
        self.assertEqual(u"ファイルを削除します。よろしいですか？", self.alert_ok())   
        self.save_screenshot_migrate(driver, "QSNF002-001-25", True)
