import time
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG060(FukushiSiteTestCaseBase):
    """TESTQAG060002"""

    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        case_data = self.test_data["case01"]
        atena_code = case_data.get("atena_code")
        params = {"DELETE_ATENA_CODE": atena_code}
        self.exec_sqlfile("QAG060.sql", params=params)
        super().setUp()

    def test_case_qag060_002(self):
        """test_case_qag060_002"""

        # case01のセクションを取得
        case_data = self.test_data["case01"]
        # atena_codeの値を取得
        atena_code = case_data.get("atena_code")

        #ログイン
        driver = None
        self.do_login()
        
        #メインメニュー・資格管理ボタン押下
        self.find_element(By.ID,"CmdProcess1_1").click()

        #資格管理画面・宛名コード入力後、検索ボタン押下
        self.driver.find_element(By.ID, "AtenaCD").click()
        self.driver.find_element(By.ID, "AtenaCD").send_keys(atena_code)
        self.driver.find_element(By.ID, "Kensaku").click()
        self.save_screenshot_migrate(driver, "QAG060-002-2-5" , True)

        #福祉制度受給状況参照画面・特定医療費(指定難病)ボタン押下
        self.driver.find_element(By.ID, "span_02:0000000040:QAG060").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-7" , True)
        
        #特定医療費(指定難病)資格管理画面・申請内容入力ボタン押下
        self.driver.find_element(By.ID, "span_CmdShinsei").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-8" , True)

        #特定医療費(指定難病)資格管理画面・初期表示ボタン押下
        self.driver.find_element(By.ID, "span_CmdShoki").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-9" , True)

        #特定医療費(指定難病)資格管理画面・申請内容入力ボタン押下
        self.driver.find_element(By.ID, "span_CmdShinsei").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-10" , True)

        #特定医療費(指定難病)資格管理画面・届出保険情報ボタン押下
        self.driver.find_element(By.ID, "btnCommon6").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-12" , True)

        #届出保険情報画面
        self.driver.find_element(By.ID, "TxtKigo").click()
        self.driver.find_element(By.ID, "TxtKigo").send_keys("１２３")

        #届出保険情報画面・初期表示ボタン押下
        self.driver.find_element(By.ID, "span_CmdShoki").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-14" , True)

        #届出保険情報画面・保険者検索ボタン押下
        self.driver.find_element(By.ID, "span_CmdHokenshaKensaku").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-16" , True)

        #保険者検索画面・検索ボタン押下
        self.driver.find_element(By.ID, "txtKanaMeisho").click()
        self.driver.find_element(By.ID, "txtKanaMeisho").send_keys("フクシ")
        self.driver.find_element(By.ID, "span_BtnKensaku").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-18" , True)

        #保険者検索画面・「1」ボタン押下
        self.driver.find_element(By.ID, "Sel1").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-20" , True)

        #届出保険情報画面・被保険者検索ボタン押下
        self.driver.find_element(By.ID, "span_CmdHiHokenshaKensaku").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-22" , True)

        #世帯員検索画面・「1」ボタン押下
        self.driver.find_element(By.ID, "Sel1").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-24" , True)

        #保険者検索画面・扶養者区分
        self.driver.find_element(By.ID, "CmbFuyousha").click()
        self.find_element(By.ID,"CmbFuyousha").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbFuyousha"),"本人")

        #保険者検索画面・記号
        self.driver.find_element(By.ID, "TxtKigo").click()
        self.driver.find_element(By.ID, "TxtKigo").send_keys("１２３")
        
        #届出保険情報画面・追加ボタン押下
        self.driver.find_element(By.ID, "span_CmdTsuika").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-26" , True)

        #届出保険情報画面・入力完了ボタン押下
        self.driver.find_element(By.ID, "CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "0件の修正、1件の追加、0件の削除を行います。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.save_screenshot_migrate(driver, "QAG060-001-1-28" , True)

        #特定医療費(指定難病)資格管理画面・申請内容を入力
        self.driver.find_element(By.ID, "ShinseiShubetsuCmb").click()
        self.find_element(By.ID,"ShinseiShubetsuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"ShinseiShubetsuCmb"),"新規")

        #特定医療費(指定難病)資格管理画面・確定ボタン押下
        self.driver.find_element(By.ID, "span_CmdKakutei").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-30" , True)

        #特定医療費(指定難病)資格管理画面・担当管理場所
        self.driver.find_element(By.ID, "TantoShokatsukuCmb").click()
        self.find_element(By.ID,"TantoShokatsukuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"TantoShokatsukuCmb"),"第一区")

        #特定医療費(指定難病)資格管理画面・保険世帯作成ボタン押下
        self.driver.find_element(By.ID, "span_CmdHokenSetai").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-33" , True)

        #保険世帯情報画面・所得情報ボタン押下
        self.driver.find_element(By.ID, "btnCommon0").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-35" , True)

        #住民税世帯情報画面・戻るボタン押下
        self.driver.find_element(By.ID, "GOBACK").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-37" , True)

        #保険世帯情報画面・手当・障害年金等を入力
        self.driver.find_element(By.ID, "TxtTokubetsuShogaiTeate_1").click()
        self.driver.find_element(By.ID, "TxtTokubetsuShogaiTeate_1").send_keys("50000")
        self.save_screenshot_migrate(driver, "QAG060-001-1-42" , True)

        #保険世帯情報画面・初期表示ボタン押下
        self.driver.find_element(By.ID, "span_CmdShoki").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-43" , True)

        #保険世帯情報画面・追加ボタン押下
        self.driver.find_element(By.ID, "span_CmdTsuika").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-45" , True)

        #個人検索画面・戻るボタン押下
        self.driver.find_element(By.ID, "GOBACK").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-47" , True)

        #保険世帯情報画面・保険区分
        self.driver.find_element(By.ID, "CmbHokenKbn").click()
        self.find_element(By.ID,"CmbHokenKbn").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbHokenKbn"),"国民健康保険")

        #保険世帯情報画面・保険情報取得ボタン押下
        self.driver.find_element(By.ID, "span_CmdHokenJyoho").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-49" , True)

        #保険世帯情報画面・特定医療取得ボタン押下
        self.driver.find_element(By.ID, "span_CmdTokuteiIryou").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-50" , True)

        #保険世帯情報画面・税情報取得ボタン押下
        self.driver.find_element(By.ID, "span_CmdZeiJyoho").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-51" , True)

        #保険世帯情報画面・情報入力
        self.find_element(By.ID,"CmbJKankei_1").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbJKankei_1"),"本人")
        self.find_element(By.ID,"CmbHiyosya_1").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbHiyosya_1"),"被保険者")
        self.driver.find_element(By.ID, "TxtTokubetsuShogaiTeate_1").click()
        self.driver.find_element(By.ID, "TxtTokubetsuShogaiTeate_1").send_keys("50000")

        #保険世帯情報画面・収入計算ボタン押下
        self.driver.find_element(By.ID, "span_CmdShunyuKeisan").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-53" , True)
        
        #保険世帯情報画面・「1」ボタン押下
        self.driver.find_element(By.ID, "span_Sel1").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-55" , True)

        #住記情報画面・戻るボタン押下
        self.driver.find_element(By.ID, "GOBACK").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-57" , True)

        # self.driver.find_element(By.CSS_SELECTOR, "u").click()
        # self.driver.find_element(By.ID, "CmbHiyosya_1").click()
        # self.find_element(By.ID,"CmbHiyosya_1").click()
        # self.select_Option(driver,self.find_element(By.ID,"CmbHiyosya_1"),"被保険者")
        #保険世帯情報画面・入力完了ボタン押下
        self.driver.find_element(By.ID, "span_CmdNKanryo").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.save_screenshot_migrate(driver, "QAG060-001-1-59" , True)

        #特定医療費(指定難病)資格管理画面・世帯所得反映ボタン押下
        self.driver.find_element(By.ID, "span_CmdSetaiShotokuHanei").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-60" , True)

        #特定医療費(指定難病)資格管理画面・所得区分ボタン押下
        self.driver.find_element(By.ID, "span_CmdShotokuKeisan").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-61" , True)

        #特定医療費(指定難病)資格管理画面・受診機関管理欄の「追加」ボタン押下
        self.driver.find_element(By.ID, "span_CmdJyushinKikanTsuika").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-62" , True)

        #特定医療費(指定難病)資格管理画面・医療機関ボタン押下
        self.driver.find_element(By.ID, "span_CmdIryoKensaku_1").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-64" , True)

        #医療機関検索画面
        self.driver.find_element(By.ID, "TxtKanaMeisho").click()
        self.driver.find_element(By.ID, "TxtKanaMeisho").send_keys("テスト")
        self.driver.find_element(By.ID, "Shozaichi_1").click()
        
        #医療機関検索画面・検索ボタン押下
        self.driver.find_element(By.ID, "span_BtnKensaku").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-65" , True)

        #医療機関検索画面・「1」ボタン押下
        self.driver.find_element(By.ID, "Sel1").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-67" , True)

        #特定医療費(指定難病)資格管理画面・病名管理部「追加」ボタン押下
        self.driver.find_element(By.ID, "span_CmdByomeiTsuika").click()
        
        #特定医療費(指定難病)資格管理画面・病名ボタン押下
        self.driver.find_element(By.ID, "btn疾病_病名ボタン_1").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-70" , True)
        
        #病名検索画面・「1」ボタン押下
        self.driver.find_element(By.ID, "Sel1").click()
        self.save_screenshot_migrate(driver, "QAG060-001-1-72" , True)

        #特定医療費(指定難病)資格管理画面・登録ボタン押下
        self.driver.find_element(By.ID, "span_CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.save_screenshot_migrate(driver, "QAG060-001-1-73" , True)

        # self.driver.find_element(By.ID, "TantoShokatsukuCmb").click()
        # self.find_element(By.ID,"TantoShokatsukuCmb").click()
        # self.select_Option(driver,self.find_element(By.ID,"TantoShokatsukuCmb"),"第一区")
        # self.driver.find_element(By.ID, "span_CmdTouroku").click()
        # assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        # self.driver.switch_to.alert.accept()