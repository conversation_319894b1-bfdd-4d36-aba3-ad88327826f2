import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG030_01090339(FukushiSiteTestCaseBase):
    """TestQAG030_01090339"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 【パターン：変更-指定医療機関変更】18歳未満の対象者に対して、申請事由「変更」申請理由「指定医療機関変更」の申請を登録できることを確認する。※医療の方針の変更ケースも兼ねる。
    def test_QAG030_01090339(self):
        """変更申請情報登録_指定医療機関変更_"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        shinsei_shubetsu = case_data.get("shinsei_shubetsu")
        shinsei_riyuu = case_data.get("shinsei_riyuu")
        val_1 = case_data.get("val_1", "")
        val_2 = case_data.get("val_2", "")
        val_3 = case_data.get("val_3", "")
        val_4 = case_data.get("val_4", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG030")
        self.click_by_id(idstr="CmdButton1_1")
        # 1 自立支援医療(育成医療)資格管理: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")
        time.sleep(2)
        # 2 自立支援医療(育成医療)資格管理: 申請事由「変更」選択申請理由「指定医療機関変更」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinsei_shubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinsei_riyuu)
        self.screen_shot("自立支援医療(育成医療)資格管理_2")

        # 3 自立支援医療(育成医療)資格管理: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 4 自立支援医療(育成医療)資格管理: 申請日「20240901」入力
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20240901")

        # 5 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_5")

        # 6 自立支援医療(育成医療)資格管理: 病院・診療所有効開始日「20240701」入力有効終了日「20240831」入力
        self.form_input_by_id(idstr="TxtJushinYukokikanKaishi_1", value=val_1)
        self.form_input_by_id(idstr="TxtJushinYukokikanShuryo_1", value=val_2)

        # 7 自立支援医療(育成医療)資格管理: 薬局有効開始日「20240701」入力有効終了日「20240831」入力
        self.form_input_by_id(idstr="TxtJushinYukokikanKaishi_2", value=val_1)
        self.form_input_by_id(idstr="TxtJushinYukokikanShuryo_2", value=val_2)

        # 8 自立支援医療(育成医療)資格管理: 訪問看護有効開始日「20240701」入力有効終了日「20240831」入力
        self.form_input_by_id(idstr="TxtJushinYukokikanKaishi_3", value=val_1)
        self.form_input_by_id(idstr="TxtJushinYukokikanShuryo_3", value=val_2)

        # 9 自立支援医療(育成医療)資格管理: 受診機関管理＞「追加」ボタン押下
        self.click_button_by_label("追加")

        # 10 自立支援医療(育成医療)資格管理: 「医療機関」ボタン押下
        self.click_by_id("CmdIryoKensaku_4")
                                                                      
        # 11 医療機関検索画面: 所在地区分「指定なし」選択点数表「医科」選択医療機関名漢字「○○」入力
        #self.click_by_id(idstr="Shozaichi_1")
        #self.click_by_id(idstr="RdoKanjiMeishoBubun")
        #self.form_input_by_id(idstr="CmbTensuhyo", text="医科")
        #self.form_input_by_id(idstr="TxtKanjiMeisho", value=case_data.get("ika_kanji_meishou", ""))
        self.form_input_by_id(idstr="Shozaichi_"+str(case_data.get("TsuikaIryoukikan_ShozaichiKubun_1", "")), value="1")
        self.form_input_by_id(idstr="CmbTensuhyo", text=case_data.get("TsuikaIryokikan_Shubetsu_1", ""))
        self.form_input_by_id(idstr="TxtKanjiMeisho", value=case_data.get("TsuikaIryokikan_KanjiMeisho_1", ""))


        # 12 医療機関検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 13 医療機関検索画面: 検索結果である医療機関一覧より「1」ボタン押下
        self.click_button_by_label("1")

        # 14 自立支援医療(育成医療)資格管理: 認定決定お知らせ有無「チェック」入力
        self.form_input_by_id(idstr="ChkOshiraseUmu_4", value="1")

        # 15 自立支援医療(育成医療)資格管理: 有効開始日「20240901」入力有効終了日「20250930」入力
        self.form_input_by_id(idstr="TxtJushinYukokikanKaishi_4", value=val_3)
        self.form_input_by_id(idstr="TxtJushinYukokikanShuryo_4", value=val_4)

        # 16 自立支援医療(育成医療)資格管理: 入通院区分「入院外」選択
        self.form_input_by_id(idstr="NyugaiKubunCmb_4", text="入院外")

        # 17 自立支援医療(育成医療)資格管理: 「医療機関」ボタン押下
        self.click_button_by_label("追加")
        self.click_by_id("CmdIryoKensaku_5")

        # 18 医療機関検索画面: 所在地区分「指定なし」選択点数表「調剤」選択医療機関名漢字「○○」入力
        # self.click_by_id(idstr="Shozaichi_1")
        # self.form_input_by_id(idstr="CmbTensuhyo", text="調剤") # TODO Item with ID: NG
        # #部分一致検索を指定↓(前方一致の場合はコメントアウト、完全一致の場合は()内をidstr="RdoKanaMeishoKanzen")に変更
        # self.click_by_id(idstr="RdoKanjiMeishoBubun")
        # self.form_input_by_id(idstr="TxtKanjiMeisho", value=case_data.get("chouzai_kanji_meishou", ""))
        self.form_input_by_id(idstr="Shozaichi_"+str(case_data.get("TsuikaIryoukikan_ShozaichiKubun_2", "")), value="1")
        self.form_input_by_id(idstr="CmbTensuhyo", text=case_data.get("TsuikaIryokikan_Shubetsu_2", ""))
        self.form_input_by_id(idstr="TxtKanjiMeisho", value=case_data.get("TsuikaIryokikan_KanjiMeisho_2", ""))

        # 19 医療機関検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 20 医療機関検索画面: 検索結果である医療機関一覧より「1」ボタン押下
        self.click_button_by_label("1")

        # 21 自立支援医療(育成医療)資格管理: 認定決定お知らせ有無「チェック」入力
        self.form_input_by_id(idstr="ChkOshiraseUmu_5", value="1")

        # 22 自立支援医療(育成医療)資格管理: 有効開始日「20240901」入力有効終了日「20250930」入力
        self.form_input_by_id(idstr="TxtJushinYukokikanKaishi_5", value=val_3)
        self.form_input_by_id(idstr="TxtJushinYukokikanShuryo_5", value=val_4)

        # 23 自立支援医療(育成医療)資格管理: 入通院区分「入院外」選択
        self.form_input_by_id(idstr="NyugaiKubunCmb_5", text="入院外")

        # 24 医療機関検索画面: 所在地区分「指定なし」選択点数表「訪問看護」選択医療機関名漢字「○○」入力
        self.click_button_by_label("追加")
        self.click_by_id("CmdIryoKensaku_6")

        # self.click_by_id(idstr="Shozaichi_1")
        # #部分一致検索を指定↓(前方一致の場合はコメントアウト、完全一致の場合は()内をidstr="RdoKanaMeishoKanzen")に変更
        # self.click_by_id(idstr="RdoKanjiMeishoBubun")
        # self.form_input_by_id(idstr="CmbTensuhyo", text="訪問看護") # TODO Item with ID: NG
        # self.form_input_by_id(idstr="TxtKanjiMeisho", value=case_data.get("houmonkango_kanji_meishou", ""))
        self.form_input_by_id(idstr="Shozaichi_"+str(case_data.get("TsuikaIryoukikan_ShozaichiKubun_3", "")), value="1")
        self.form_input_by_id(idstr="CmbTensuhyo", text=case_data.get("TsuikaIryokikan_Shubetsu_3", ""))
        self.form_input_by_id(idstr="TxtKanjiMeisho", value=case_data.get("TsuikaIryokikan_KanjiMeisho_3", ""))


        # 25 医療機関検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 26 医療機関検索画面: 検索結果である医療機関一覧より「1」ボタン押下
        self.click_button_by_label("1")

        # 27 自立支援医療(育成医療)資格管理: 認定決定お知らせ有無「チェック」入力
        self.form_input_by_id(idstr="ChkOshiraseUmu_6", value="1")

        # 28 自立支援医療(育成医療)資格管理: 有効開始日「20240901」入力有効終了日「20250930」入力
        self.form_input_by_id(idstr="TxtJushinYukokikanKaishi_6", value=val_3)
        self.form_input_by_id(idstr="TxtJushinYukokikanShuryo_6", value=val_4)

        # 29 自立支援医療(育成医療)資格管理: 入通院区分「入院外」選択
        self.form_input_by_id(idstr="NyugaiKubunCmb_6", text="入院外")

        # 30 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_30")

        # 31 自立支援医療(育成医療)資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 32 自立支援医療(育成医療)資格管理: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(育成医療)資格管理_32")
