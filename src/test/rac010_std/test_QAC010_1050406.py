import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC010_1050406(FukushiSiteTestCaseBase):
    """TestQAC010_1050406"""

    def setUp(self):
        case_data = self.test_data["TestQAC010_1050406"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC010", "DELETE_ATENA_CODE": case_data.get("atena_code", ""), "TARGET_NENDO": case_data.get("nendo", ""), "TARGET_SOUSYOTOKU": "10000"}
        self.exec_sqlfile("QAC-ALL-CLEAR.sql", params=sql_params)
        super().setUp()

    # 転出、死亡の住記減異動が発生した対象者をバッチ処理にて抽出、差止履歴を一括で作成できることを確認する。
    def test_QAC010_1050406(self):
        """住記減異動確認_差止_"""

        case_data = self.test_data["TestQAC010_1050406"]
        atena_code = case_data.get("atena_code", "")
        shinsei_shubetsu = case_data.get("shinsei_shubetsu", "")
        shinsei_riyuu = case_data.get("shinsei_riyuu", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「特別障害者手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC010")

        # 8 特別障害者手当資格管理画面: 表示
        self.screen_shot("特別障害者手当資格管理画面_8")

        # 9 特別障害者手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 特別障害者手当資格管理画面: 申請種別「認定請求」選択申請理由「新規」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinsei_shubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinsei_riyuu)

        # 11 特別障害者手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 特別障害者手当資格管理画面: 申請日「20230201」担当所管区「第一区」選択誓約有無「チェック」
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230201")
        self.form_input_by_id(idstr="TantouShokanCmb", text="第一区")
        self.form_input_by_id(idstr="SeiyakuumuChkBox", value="1")

        # 13 特別障害者手当資格管理画面: 表示
        self.screen_shot("特別障害者手当資格管理画面_13")

        # 14 特別障害者手当資格管理画面: 障害区分１「視覚障害」選択有期認定年月１「20251031」認定基準１「一 ロ 一眼の視力が〇・〇四、他眼の視力が手動弁以下のもの」選択
        self.form_input_by_id(idstr="Shougai1Cmb", text="視覚障害")
        self.form_input_by_id(idstr="TxtNinteiYMD1", value="20251031")
        self.form_input_by_id(idstr="SelectHanyo1", text="一 ロ 一眼の視力が〇・〇四、他眼の視力が手動弁以")

        # 15 特別障害者手当資格管理画面: 「障害程度審査情報１」ボタン押下
        self.click_button_by_label("障害程度審査情報１")

        # 16 障害審査情報画面: 表示

        # 17 障害審査情報画面: 判定機関依頼日「20230201」資格判定方法「審査機関」選択資格判定内容「診断書」選択
        self.form_input_by_id(idstr="TxtHanteiYMD", value="20230201")
        self.form_input_by_id(idstr="CmbHRiyu", text="審査機関")
        self.form_input_by_id(idstr="CmbHNaiyou", text="診断書")

        # 18 障害審査情報画面: 表示
        self.screen_shot("障害審査情報画面_18")

        # 19 障害審査情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 20 特別障害者手当資格管理画面: 表示
        self.screen_shot("特別障害者手当資格管理画面_20")

        # 21 特別障害者手当資格管理画面: 「福祉世帯情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("福祉世帯情報")

        # 22 福祉世帯情報画面: 表示
        self.screen_shot("福祉世帯情報画面_22")

        # 23 福祉世帯情報画面: 本人から見た続柄「本人」選択受給者との関係「本人」該当日「20230201」
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230201")
        self.click_button_by_label("入力完了")

        # 24 特別障害者手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 25 特別障害者手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 26 特別障害者手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("特別障害者手当資格管理画面_26")

        # 27 特別障害者手当資格管理画面: 「進達入力」ボタン押下
        # 進達入力ボタンが存在していない場合は、該当箇所をスキップする
        can_shintatsu_button = self.click_button_by_label("進達入力")
        if (can_shintatsu_button):
            # 28 特別障害者手当資格管理画面: 進達日「20230201」進達判定年月日「20230201」進達結果「該当」
            self.form_input_by_id(idstr="TxtShintatsuYMD", value="20230201")
            self.form_input_by_id(idstr="TxtShintatsuHanteiYMD", value="20230201")
            self.form_input_by_id(idstr="ShintasuHanteiCmb", text="該当")

            # 29 特別障害者手当資格管理画面: 「月額計算」ボタン押下
            self.click_button_by_label("月額計算")

            # 30 特別障害者手当資格管理画面: 「登録」ボタン押下
            self.click_button_by_label("登録")
            self.alert_ok()

        # 31 特別障害者手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("特別障害者手当資格管理画面_31")

        # 32 特別障害者手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 33 特別障害者手当資格管理画面: 判定日「20230201」判定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20230201")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")

        # 34 特別障害者手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 35 特別障害者手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 36 特別障害者手当資格管理画面: 表示
        # Assert: 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        self.screen_shot("特別障害者手当資格管理画面_36")

        # 37 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_37")

        # 38 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 39 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_39")

        # 40 バッチ起動画面: 業務：障害事業：特別障害者手当処理区分：月次処理処理分類：住記異動者自動差止
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="特別障害者手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="住記異動者自動差止")

        # 41 バッチ起動画面: 「住記異動者自動差止処理」のNoボタン押下
        self.click_batch_job_button_by_label("住記異動者自動差止処理")

        # 42 バッチ起動画面: 対象開始年月日「20230401」対象終了年月日「20230702」差止決定日「20230701」
        params = [
            {"title": "対象開始年月日", "type": "text", "value": "20230401"},
            {"title": "対象終了年月日", "type": "text", "value": "20230702"},
            {"title": "差止決定年月日", "type": "text", "value": "20230701"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_42")

        # 43 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 44 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 45 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_45")

        # 46 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 47 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_47")

        # 48 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 49 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_49")

        # 50 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 51 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_51")

        # 52 ジョブ帳票履歴画面: 「住記異動者自動差止一覧」のNoボタン押下
        # 53 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # 54 住記異動者自動差止一覧（PDF）: 表示
        # 55 住記異動者自動差止一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 56 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 57 メインメニュー画面: 表示
        self.do_login()

        # 58 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 59 個人検索画面: 表示

        # 60 個人検索画面: 「住民コード」入力
        # 61 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 62 受給状況画面: 表示

        # 63 受給状況画面: 「特別障害者手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC010")

        # 64 特別障害者手当資格管理画面: 表示

        # 65 特別障害者手当資格管理画面: 「差止情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("差止情報")

        # 66 差止情報画面: 表示
        self.screen_shot("差止情報画面_66")
