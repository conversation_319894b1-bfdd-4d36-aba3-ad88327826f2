import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC010_1050207(FukushiSiteTestCaseBase):
    """TestQAC010_1050207"""

    def setUp(self):
        case_data = self.test_data["TestQAC010_1050207"]
        super().setUp()

    # 支払対象者の一覧等を出力できること、支払データが作成できることを確認する。
    def test_QAC010_1050207(self):
        """支払データ作成"""

        case_data = self.test_data["TestQAC010_1050207"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：障害事業：特別障害者手当処理区分：月次処理処理分類：支払処理
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="特別障害者手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="支払処理")

        # 5 バッチ起動画面: 「支払更新処理」のNoボタン押下
        self.click_batch_job_button_by_label("支払更新処理")

        # 6 バッチ起動画面: 支払区分「定例」選択対象年月「202308」振込年月日「20230810」依頼日「20230725」
        params = [
            {"title": "支払区分", "type": "select", "value": "定例"},
            {"title": "対象年月", "type": "text", "value": "202402"},
            {"title": "振込年月日", "type": "text", "value": "20240810"},
            {"title": "依頼日", "type": "text", "value": "20240725"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_6")

        # 7 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 8 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 9 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_9")

        # 10 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 11 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_11")

        # 12 ジョブ実行履歴画面: 「ダウンロード」ボタン押下
        exists_down_load_page = self.goto_output_files_dl_page(exec_datetime=exec_datetime)
        if(exists_down_load_page):
            # 作成データぺージに遷移出来た場合は、全ファイルの取得を行う。（戻値はDLしたファイル数）
            output_file_dl_count = self.get_job_output_files(case_name="dl_file")

            # 13 ダウンロード画面: 表示
            self.screen_shot("ダウンロード画面_13")
            # 作成データページに遷移出来てる場合は戻るボタンで実行履歴に戻る。
            self.return_click()

        # 14 ダウンロード画面: ダウンロードファイル一覧「1」Noボタン押下

        # 15 ジョブ実行履歴画面: 「ファイルを開く」ボタン押下

        # 16 QAC010: 表示
        # self.screen_shot("QAC010_17")

        # 17 QAC010: ×ボタン押下でファイルを閉じる

        # 18 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 19 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 21 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_21")

        # 22 ジョブ帳票履歴画面: 「銀行別集計表」のNoボタン押下
        # self.click_by_id("Sel1")

        # 23 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # self.click_button_by_label("ファイルを開く")

        # 24 銀行別集計表（PDF）: 表示
        # self.screen_shot("銀行別集計表（PDF）_24")

        # 25 銀行別集計表（PDF）: ×ボタン押下でPDFを閉じる

        # 26 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_26")

        # 27 ジョブ帳票履歴画面: 「口座振替依頼書」のNoボタン押下
        # self.click_by_id("Sel1")

        # 28 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # self.click_button_by_label("ファイルを開く")

        # 29 口座振替依頼書（PDF）: 表示
        # self.screen_shot("口座振替依頼書（PDF）_30")

        # 30 口座振替依頼書（PDF）: ×ボタン押下でPDFを閉じる

        # 31 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_32")

        # 32 ジョブ帳票履歴画面: 「支払明細一覧＿銀行支店別」のNoボタン押下
        # self.click_by_id("Sel1")

        # 33 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # self.click_button_by_label("ファイルを開く")

        # 34 支払明細一覧＿銀行支店別（PDF）: 表示
        # self.screen_shot("支払明細一覧＿銀行支店別（PDF）_34")

        # 35 支払明細一覧＿銀行支店別（PDF）: ×ボタン押下でPDFを閉じる

        # 36 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_36")
