import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC010_1051203(FukushiSiteTestCaseBase):
    """TestQAC010_1051203"""

    def setUp(self):
        case_data = self.test_data["TestQAC010_1051203"]
        super().setUp()

    # 受給者台帳を出力できることを確認する。
    def test_QAC010_1051203(self):
        """受給者台帳の作成"""

        case_data = self.test_data["TestQAC010_1051203"]
        gyomuSelect = case_data.get("gyomuSelect", "")
        jigyoSelect = case_data.get("jigyoSelect", "")
        shoriKubunSelect = case_data.get("shoriKubunSelect", "")
        shoriBunruiSelect = case_data.get("shoriBunruiSelect", "")
        fukushi_code = case_data.get("fukushi_code", "")
        gyomu_code = case_data.get("gyomu_code", "")
        chushutsu_s_ymd = case_data.get("chushutsu_s_ymd", "")
        chushutsu_e_ymd = case_data.get("chushutsu_e_ymd", "")
        shinsei_shubetsu = case_data.get("shinsei_shubetsu", "")
        kettei_kekka = case_data.get("kettei_kekka", "")
        ouput_order = case_data.get("ouput_order", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：障害事業：特別障害者手当処理区分：日次処理処理分類：台帳出力処理
        self.form_input_by_id(idstr="GyomuSelect", text=gyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=jigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=shoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=shoriBunruiSelect)

        # 5 バッチ起動画面: 「受給者台帳出力処理_国様式版」のNoボタン押下
        self.click_batch_job_button_by_label("受給者台帳出力処理_国様式版")

        # 6 バッチ起動画面: 福祉事務所コード「」業務コード「特別障害者手当」選択抽出開始年月日「20230401」抽出
        # 終了年月日「20230531」申請種別「新規」選択決定結果「結果」選択
        # 出力順「整理番号順」選択
        params = [
            {"title": "福祉事務所コード", "type": "select", "value": fukushi_code},
            {"title": "業務コード", "type": "select", "value": gyomu_code},
            {"title": "抽出開始年月日", "type": "text", "value": chushutsu_s_ymd},
            {"title": "抽出終了年月日", "type": "text", "value": chushutsu_e_ymd},
            {"title": "申請種別", "type": "select", "value": shinsei_shubetsu},
            {"title": "決定結果", "type": "select", "value": kettei_kekka},
            {"title": "出力順", "type": "select", "value": ouput_order}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_6")

        # 7 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 8 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 9 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_9")

        # 10 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 11 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_11")

        # 12 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 13 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_13")

        # 14 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 15 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_15")

        # 16 ジョブ帳票履歴画面: 「受給者台帳」のNoボタン押下

        # 17 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 18 受給者台帳（PDF）: 表示
        # self.screen_shot("受給者台帳（PDF）_18")

        # 19 受給者台帳（PDF）: ×ボタン押下でPDFを閉じる

        # 20 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_20")
