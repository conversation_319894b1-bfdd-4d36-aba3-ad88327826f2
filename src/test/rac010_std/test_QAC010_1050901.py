import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC010_1050901(FukushiSiteTestCaseBase):
    """TestQAC010_1050901"""

    def setUp(self):
        case_data = self.test_data["TestQAC010_1050901"]
        super().setUp()

    # 福祉行政報告例25表を出力できることを確認する。
    def test_QAC010_1050901(self):
        """各種報告書作成"""

        case_data = self.test_data["TestQAC010_1050901"]
        taisho_ym = case_data.get("taisho_ym", "")
        gyomu_name = case_data.get("gyomu_name", "")
        jigyo_name = case_data.get("jigyo_name", "")
        shoriKubun_name = case_data.get("shoriKubun_name", "")
        shoriBunrui_name = case_data.get("shoriBunrui_name", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：障害事業：特別障害者手当処理区分：月次処理処理分類：統計処理
        self.form_input_by_id(idstr="GyomuSelect", text=gyomu_name)
        self.form_input_by_id(idstr="JigyoSelect", text=jigyo_name)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=shoriKubun_name)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=shoriBunrui_name)

        # 5 バッチ起動画面: 「福祉行政報告例25出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("福祉行政報告例25出力処理")

        # 6 バッチ起動画面: 対象年月「202307」
        params = [
            {"title": "対象年月", "type": "text", "value": taisho_ym}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_6")

        # 7 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 8 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 9 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_9")

        # 10 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 11 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_11")

        # 12 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 13 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_13")

        # 14 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 15 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_15")

        # 16 ジョブ帳票履歴画面: 「福祉行政報告例25」のNoボタン押下
        # self.click_button_by_label("福祉行政報告例25")

        # 17 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # self.click_button_by_label("ファイルを開く")

        # 18 福祉行政報告例25（PDF）: 表示
        # self.screen_shot("福祉行政報告例25（PDF）_18")

        # 19 福祉行政報告例25（PDF）: ×ボタン押下でPDFを閉じる

        # 20 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_20")
