import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC010_1050102(FukushiSiteTestCaseBase):
    """TestQAC010_1050102"""
    # 10ステップ：10min

    # 不足があった書類の登録ができることを確認する。
    def test_QAC010_1050102(self):
        """不足書類入力"""

        case_data = self.test_data["TestQAC010_1050102"]
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC010")
        self.screen_shot("特別障害者手当資格管理画面_0")

        # 2 特別障害者手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        self.click_button_by_label("確定")
        # TODO(nozawa): ここは本来不要だが、現状修正時に所管区がクリアされているため再度設定
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text=case_data.get("shokan_ku", ""))

        # 3 特別障害者手当資格管理画面: 「提出書類管理」ボタン押下
        self.open_common_buttons_area()
        self.common_button_click("提出書類管理")

        # 4 提出書類管理: 表示
        self.screen_shot("提出書類管理_4")

        # 5 提出書類管理: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 6 提出書類管理: その他にチェック
        self.entry_teishutsu_shorui(shorui_name="その他", is_check=True)
        self.screen_shot("提出書類管理_6")

        # 7 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 8 特別障害者手当資格管理画面: 表示
        self.screen_shot("特別障害者手当資格管理画面_8")

        # 9 特別障害者手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 10 特別障害者手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 11 特別障害者手当資格管理画面: 表示
        self.screen_shot("特別障害者手当資格管理画面_11")
