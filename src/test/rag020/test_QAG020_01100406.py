import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG020_01100406(FukushiSiteTestCaseBase):
    """TestQAG020_01100406"""
    @unittest.skip('skipped')
    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 受給者証の交付についてを出力できることを確認する。シナリオではオンラインになっているが未作成のためバッチ処理で実施しているためこのシナリオはスキップしています。
    def test_QAG020_01100406(self):
        """受給者証の交付について作成"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG020")
        self.click_by_id("CmdButton1_1")
        
        self.click_button_by_label("印刷")
        # 1 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_1")

        # 2 帳票印刷画面: 「受給者証の交付について」行の印刷チェックボックス選択「受給者証の交付について」行の発行年月日チェックボックス選択発行年月日「○○」入力
        self.print_online_reports(case_name="ケース名", report_name=case_data.get("form_name_3", ""), hakkou_ymd=case_data.get("hakkou_ymd", ""))
        self.screen_shot("帳票印刷画面_2")

        # 3 帳票印刷画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 4 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.print_online_reports(case_name="帳票印刷画面")

        # 5 帳票（PDF）: 表示
        self.screen_shot("帳票（PDF）_5")

        # 6 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 7 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 8 自立支援医療(精神通院)資格管理: 表示
        self.screen_shot("自立支援医療(精神通院)資格管理_8")
