import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

# 転用元シナリオ：TestQAG020_01100114
class TestQAG020_01100313(FukushiSiteTestCaseBase):
    """TestQAG020_01100313"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 自立支援医療受給者証を出力できることを確認する。
    def test_QAG020_01100313(self):
        """自立支援医療受給者証作成"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG020")
        self.click_by_id(idstr="CmdButton1_1")

        # 1 自立支援医療(精神通院)資格管理: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_2")

        # 3 帳票印刷画面: 「自立支援医療受給者証」行の印刷チェックボックス選択「自立支援医療受給者証」行の発行年月日チェックボックス選択発行年月日「○○」入力
        # self.print_online_reports(case_name="ケース名", report_name=case_data.get("form_name_0", ""), hakkou_ymd=case_data.get("hakkou_ymd", ""))

        self.click_by_id(idstr="insatsuChk_0")

        self.form_input_by_id(idstr="TxtHakkoYMD", value=case_data.get("hakkou_ymd", ""))

        self.click_button_by_label("一括反映")

        self.screen_shot("帳票印刷画面_3")

        # 4 帳票印刷画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        self.alert_ok()

        # 5 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        self.screen_shot("帳票印刷画面_5")
        # self.print_online_reports(case_name="帳票印刷画面")

        # 6 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_7")

        # 7 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 8 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 9 自立支援医療(精神通院)資格管理: 表示
        self.screen_shot("自立支援医療(精神通院)資格管理_9")
