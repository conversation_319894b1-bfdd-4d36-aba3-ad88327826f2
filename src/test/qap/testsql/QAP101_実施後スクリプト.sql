USE WRG$$JICHITAI_CODE$$QA

UPDATE QZDA福祉世帯
SET 該当日 = '20180226'
WHERE 自治体C = '$$JICHITAI_CODE$$'
AND 業務C = 'QAP020'
AND 業務固有C1 in
(SELECT [業務固有C1] 
FROM [QZDA福祉世帯]
WHERE [福祉世帯員_宛名C] = '$$宛名CD$$')

UPDATE QPDA世帯保護者認定
SET 認定月_開始 = '201802',
申請日 = '20180226',
決定日 = '20180226'
WHERE 自治体C = '$$JICHITAI_CODE$$'
AND 業務C = 'QAP020'
AND 世帯台帳番号 in
(SELECT [業務固有C1] 
FROM [QZDA福祉世帯]
WHERE [福祉世帯員_宛名C] = '$$宛名CD$$'
AND [該当日] = '20180226')

UPDATE QPDA世帯納付義務者認定
SET 認定月_開始 = '201802',
申請日 = '20180226',
決定日 = '20180226'
WHERE 自治体C = '$$JICHITAI_CODE$$'
AND 業務C = 'QAP020'
AND 世帯台帳番号 in
(SELECT [業務固有C1] 
FROM [QZDA福祉世帯]
WHERE [福祉世帯員_宛名C] = '$$宛名CD$$'
AND [該当日] = '20180226')