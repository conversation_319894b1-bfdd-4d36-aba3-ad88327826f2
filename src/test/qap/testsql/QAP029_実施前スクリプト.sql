--QAP029実施前に実行する

USE WRG$$JICHITAI_CODE$$QA

-- DECLARE @選考管理番号 varchar(15)

-- SET @選考管理番号 = '999999962'

-- SELECT *
-- FROM QPWC選考施設情報
-- WHERE (選考管理番号 = '999999962') AND (選考施設C = '190011251')

-- SELECT *
-- FROM QPWC希望施設選考結果
-- WHERE (選考管理番号 = '999999962') AND (児童_宛名C = '8900024') AND (希望順位 = 1)



UPDATE QPWC選考施設情報
SET 調整数 = '0' ,調整後募集数 = '0'
WHERE (選考管理番号 = '$$QAT010_SENKOUBANGO$$') AND (選考施設C = '$$QAP010_SHISETSU$$')

UPDATE QPWC希望施設選考結果
SET 変更理由C = NULL
WHERE (選考管理番号 = '$$QAT010_SENKOUBANGO$$') AND (児童_宛名C = '$$QAT010_ATENA$$') AND (希望順位 = 1)
