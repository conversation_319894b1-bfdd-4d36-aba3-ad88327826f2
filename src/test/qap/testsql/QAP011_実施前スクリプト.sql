/****************************************************************************************************************/
/* [名称]  データ削除スクリプト                                                                      */
/* [概要]                                                                                                       */
/* [履歴]                                                                                                       */
/*                                                                                                              */
/*                                                                                                              */
/* [使用方法] １．$$JICHITAI_CODE$$を自治体コードに置換してください。                                                 */
/****************************************************************************************************************/
USE WRG$$JICHITAI_CODE$$QA

--QAPF10340
DELETE FROM [dbo].[QPMC指数]
WHERE 対象年度 = '2021' 
AND 階層レベルC = '1' 
AND 親指数区分C = '99'
AND 調整区分C = '1'
AND 指数区分C = '99' 
AND 業務C = 'QAP010'
AND 自治体C = '$$JICHITAI_CODE$$'

--QAPF103300
DELETE FROM [dbo].[QPMD施設開局日]
WHERE 施設C = '9999'
 AND 対象年度 = '2021'
 AND 休日 = '20210402'
 AND 業務C = 'QAP010'
 AND 自治体C = '$$JICHITAI_CODE$$'

--QAPF1082000データ削除スクリプト  
UPDATE [dbo].[QPMF所得割額別減免設定]
SET 均等割額_開始 = 1,
    均等割額_終了 = 111111111,
    所得割額_開始 = 1, 
    所得割額_終了 = 111111111, 
    徴収額_第1子_標準時間 = 0, 
    徴収額_第2子_標準時間 = 0,
    徴収額_第3子_標準時間 = 0
WHERE 
自治体C = '$$JICHITAI_CODE$$'
AND 業務C = 'QAP010'
AND (対象年度 = '2021') 
AND (認定月 = '202104') 
AND (支給認定区分C = '1') 
AND (均等割額_開始 = 0) 
AND (均等割額_終了 = 999999999) 
AND (所得割額_開始 = 0) 
AND (所得割額_終了 = 999999999) 
--佐々木一旦削除S 画面上から表示されないため
--AND (徴収額_第1子_標準時間 = 9) 
--AND (徴収額_第2子_標準時間 = 99) 
--AND (徴収額_第3子_標準時間 = 999) 
--佐々木一旦削除E 画面上から表示されないため

--QAPF1083000データ削除スクリプト  
UPDATE [dbo].[QPMG主食費等単価]
SET  単価1 = '1'
WHERE 自治体C = '$$JICHITAI_CODE$$'
AND 業務C = 'QAP010'
AND 対象年度 = '2021' 
AND 認定月 = '202104'
AND 単価1 = '999999999'