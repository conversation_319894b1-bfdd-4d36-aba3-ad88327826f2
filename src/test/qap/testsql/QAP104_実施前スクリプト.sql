USE WRG$$JICHITAI_CODE$$QA

UPDATE QPDA世帯保護者認定
SET 変更理由C = '1'
WHERE 自治体C = '$$JICHITAI_CODE$$'
AND 業務C = 'QAP020'
AND 世帯台帳番号 in
(SELECT [業務固有C1] 
FROM [QZDA福祉世帯]
WHERE [福祉世帯員_宛名C] = '$$宛名CD$$')

UPDATE QPDA世帯納付義務者認定
SET 変更理由C = '1'
WHERE 自治体C = '$$JICHITAI_CODE$$'
AND 業務C = 'QAP020'
AND 世帯台帳番号 in
(SELECT [業務固有C1] 
FROM [QZDA福祉世帯]
WHERE [福祉世帯員_宛名C] = '$$宛名CD$$')

DELETE
FROM QPDA世帯ひとり親認定
WHERE 自治体C = '$$JICHITAI_CODE$$'
AND 業務C = 'QAP020'
AND 世帯台帳番号 in
(SELECT [業務固有C1] 
FROM [QZDA福祉世帯]
WHERE [福祉世帯員_宛名C] = '$$宛名CD$$')

DELETE
FROM  QPDA世帯生保認定
WHERE 自治体C = '$$JICHITAI_CODE$$'
AND 業務C = 'QAP020'
AND 世帯台帳番号 in
(SELECT [業務固有C1] 
FROM [QZDA福祉世帯]
WHERE [福祉世帯員_宛名C] = '$$宛名CD$$')

DELETE
FROM QPDA世帯障がい認定
WHERE 自治体C = '$$JICHITAI_CODE$$'
AND 業務C = 'QAP020'
AND 世帯台帳番号 in
(SELECT [業務固有C1] 
FROM [QZDA福祉世帯]
WHERE [福祉世帯員_宛名C] = '$$宛名CD$$')

DELETE
FROM QPDA世帯課税認定
WHERE 自治体C = '$$JICHITAI_CODE$$'
AND 業務C = 'QAP020'
AND 世帯台帳番号 in
(SELECT [業務固有C1] 
FROM [QZDA福祉世帯]
WHERE [福祉世帯員_宛名C] = '$$宛名CD$$')

DELETE
FROM QPDA世帯員課税管理
WHERE 自治体C = '$$JICHITAI_CODE$$'
AND 業務C = 'QAP020'
AND 世帯台帳番号 in
(SELECT [業務固有C1] 
FROM [QZDA福祉世帯]
WHERE [福祉世帯員_宛名C] = '$$宛名CD$$')


