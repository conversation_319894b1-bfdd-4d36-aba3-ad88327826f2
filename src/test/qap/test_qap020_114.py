import time
from selenium.webdriver.support.select import Select
from base.kodomo_case import KodomoSiteTestCaseBase
import unittest

class TestQAP020114(KodomoSiteTestCaseBase):
    """TestQAP020114"""
    
    def __init__(self, *args, **kwargs):
        self.env_name = "student"
        super().__init__(*args, **kwargs)

    def setUp(self):
        atena_list = self.test_data.get("sql_params")
        self.exec_sqlfile("QAP114_実施前スクリプト.sql", params=atena_list)
        super().setUp()
    
    def test_case_qap020_114(self):
        '''test_case_qap020_114'''
        driver = None
    
        # ログイン
        self.do_login()
        self.find_element_by_id(u"mainmenu_1").click()
        time.sleep(5)
        self.goto_menu(["学童","施設管理","施設検索"],"1")
        #QQAP114-001施設事業所の認定情報の確認
        self.save_screenshot_migrate(driver, "QAP114-001-004" , True)
        self.find_element_by_id("tab01_QAPF201600_txtShisetsuKensakuShisetsuCD_textboxInput").click()
        self.find_element_by_id("tab01_QAPF201600_txtShisetsuKensakuShisetsuCD_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF201600_txtShisetsuKensakuShisetsuCD_textboxInput").send_keys(self.test_data.get('施設検索_施設C'))
        self.find_element_by_id("tab01_QAPF201600_WrCmnBtn05_button").click()
        self.save_screenshot_migrate(driver, "QAP114-001-007" , True)

        #QQAP114-002施設事業所の認定情報の登録
        self.goto_menu(["学童","施設管理","施設追加"],"1")
        self.save_screenshot_migrate(driver, "QAP114-002-004" , True)

        self.find_element_by_id("tab02_QAPF200400_selWrGyoseiku_JigyoshoShokanku_select").click()
        Select(self.find_element_by_id("tab02_QAPF200400_selWrGyoseiku_JigyoshoShokanku_select")).select_by_visible_text(self.test_data.get('所在区'))
   
        self.find_element_by_id("tab02_ZZZ000000_txtTaishoKikanStartYM_textboxInput").click()
        self.find_element_by_id("tab02_ZZZ000000_txtTaishoKikanStartYM_textboxInput").clear()
        self.find_element_by_id("tab02_ZZZ000000_txtTaishoKikanStartYM_textboxInput").send_keys(self.test_data.get('対象期間'))
        self.find_element_by_id("tab02_ZZZ000000_selIdoJiyu_select").click()
        Select(self.find_element_by_id("tab02_ZZZ000000_selIdoJiyu_select")).select_by_visible_text(self.test_data.get('異動事由'))
        self.find_element_by_id("tab02_QAPF200400_lblShisetsuCD_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_lblShisetsuCD_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_lblShisetsuCD_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_lblShisetsuCD_textboxInput").send_keys(self.test_data.get('施設コード'))
        self.find_element_by_id("tab02_QAPF200400_txtJigyoStartYMD_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtJigyoStartYMD_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_txtJigyoStartYMD_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtJigyoStartYMD_textboxInput").send_keys(self.test_data.get('事業開始年月日'))
        self.find_element_by_id("tab02_QAPF200400_txtShisetsuKanaNM_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtShisetsuKanaNM_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_txtShisetsuKanaNM_textboxInput").send_keys(self.test_data.get('施設カナ名称'))
        
        self.find_element_by_id("tab02_QAPF200400_txtShisetsuNM_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtShisetsuNM_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_txtShisetsuNM_textboxInput").send_keys(self.test_data.get("施設名称"))

        self.find_element_by_id("tab02_QAPF200400_txtShisetsuPostNoOya_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtShisetsuPostNoOya_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_txtShisetsuPostNoOya_textboxInput").send_keys(self.test_data.get("郵便番号"))

        self.find_element_by_id("tab02_QAPF200400_txtShisetsuPostNoKo_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtShisetsuPostNoKo_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_txtShisetsuPostNoKo_textboxInput").send_keys(self.test_data.get("施設郵便番号子"))

        self.find_element_by_id("tab02_QAPF200400_btnShisetsuShinai_button").click()
        self.find_element_by_id("tab02_ZZZ000000_btnJushoCode1_1_1_button").click()
        self.find_element_by_id("tab02_ZAAF000300_btnConfirm_button").click()

        self.find_element_by_id("tab02_QAPF200400_selShisetsuShurui_select").click()
        Select(self.find_element_by_id("tab02_QAPF200400_selShisetsuShurui_select")).select_by_visible_text(self.test_data.get("施設種類"))
        self.wait_page_loaded(wait_timeout=10)
        self.find_element_by_id("tab02_QAPF200400_selShisetsuShozaichiTodofuken_select").click()
        Select(self.find_element_by_id("tab02_QAPF200400_selShisetsuShozaichiTodofuken_select")).select_by_visible_text(self.test_data.get("施設所在地"))
        self.wait_page_loaded(wait_timeout=10)
        self.find_element_by_id("tab02_QAPF200400_selShisetsuShozaichiShikutyoson_select").click()
        Select(self.find_element_by_id("tab02_QAPF200400_selShisetsuShozaichiShikutyoson_select")).select_by_visible_text(self.test_data.get("施設所在地_市"))
        self.find_element_by_id("body-webrings").click()

        self.find_element_by_xpath("//li[@id='tab02_QAPF200400_jigyoshajoho_li']/a/span").click()
        self.save_screenshot_migrate(driver, "QAP114-002-016" , True)
        self.find_element_by_id("tab02_QAPF200400_txtHojinKanaNM_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtHojinKanaNM_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_txtHojinKanaNM_textboxInput").send_keys(self.test_data.get("sql_params").get('法人等_名称カナ'))
        
        self.find_element_by_id("tab02_QAPF200400_txtHojinNM_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtHojinNM_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_txtHojinNM_textboxInput").send_keys(self.test_data.get("sql_params").get('法人等_名称'))

        self.find_element_by_id("tab02_QAPF200400_selHojinShubetsu_select").click()       
        Select(self.find_element_by_id("tab02_QAPF200400_selHojinShubetsu_select")).select_by_visible_text(self.test_data.get('法人等種別'))
        self.find_element_by_id("tab02_QAPF200400_btnHojinShinai_button").click()
        self.find_element_by_id("tab02_ZZZ000000_btnJushoCode1_1_1_button").click()
        self.find_element_by_id("tab02_ZAAF000300_btnConfirm_button").click() 
        self.find_element_by_id("tab02_QAPF200400_txtHojinTEL_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtHojinTEL_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_txtHojinTEL_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtHojinTEL_textboxInput").send_keys(self.test_data.get('電話番号'))
        self.wait_page_loaded(wait_timeout=10)
        self.find_element_by_id("tab02_QAPF200400_txtSetsuritsuYMD_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtSetsuritsuYMD_textboxInput").send_keys(self.test_data.get('設立年月日'))

        self.find_element_by_id("tab02_QAPF200400_selHojinShokatsuchoTodofuken_select").click()
        Select(self.find_element_by_id("tab02_QAPF200400_selHojinShokatsuchoTodofuken_select")).select_by_visible_text(self.test_data.get("施設所在地"))

        self.find_element_by_id("tab02_QAPF200400_selHojinShokatsuchoShikuchoson_select").click()
        Select(self.find_element_by_id("tab02_QAPF200400_selHojinShokatsuchoShikuchoson_select")).select_by_visible_text(self.test_data.get("施設所在地_市"))

        self.find_element_by_id("tab02_QAPF200400_txtDaihyoshaKanaShimei_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtDaihyoshaKanaShimei_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_txtDaihyoshaKanaShimei_textboxInput").send_keys(self.test_data.get('代表者カナ氏'))
       
        self.find_element_by_id("tab02_QAPF200400_txtDaihyoshaShimei_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtDaihyoshaShimei_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_txtDaihyoshaShimei_textboxInput").send_keys(self.test_data.get('代表者氏名'))

        self.find_element_by_id("tab02_QAPF200400_txtDaihyoshaShokuNM_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_txtDaihyoshaShokuNM_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtDaihyoshaShokuNM_textboxInput").send_keys(self.test_data.get('代表者職名'))

        self.find_element_by_id("tab02_QAPF200400_lblShuninYMD_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_lblShuninYMD_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_lblShuninYMD_textboxInput").send_keys(self.test_data.get('就任年月日'))

        self.find_element_by_xpath("//li[@id='tab02_QAPF200400_kanrijoho_li']/a/span").click()
        self.save_screenshot_migrate(driver, "QAP114-002-031" , True)
        self.find_element_by_id("tab02_QAPF200400_selKeieiShutai_select").click()
        Select(self.find_element_by_id("tab02_QAPF200400_selKeieiShutai_select")).select_by_visible_text(self.test_data.get("経営主体"))
        self.find_element_by_id("tab02_QAPF200400_selChiikiKubun_select").click()
        Select(self.find_element_by_id("tab02_QAPF200400_selChiikiKubun_select")).select_by_visible_text(self.test_data.get("地域区分"))
        self.find_element_by_xpath("//li[@id='tab02_QAPF200400_riyouteiin_li']/a/span").click()
        self.find_element_by_id("tab02_QAPF200400_selTaishoClassGakunenSita_select").click()
        Select(self.find_element_by_id("tab02_QAPF200400_selTaishoClassGakunenSita_select")).select_by_visible_text(self.test_data.get("対象学年_上限"))
        self.find_element_by_id("tab02_QAPF200400_selTaishoClassGakunenUe_select").click()
        Select(self.find_element_by_id("tab02_QAPF200400_selTaishoClassGakunenUe_select")).select_by_visible_text(self.test_data.get("対象学年_上限"))
    
        self.find_element_by_id("tab02_ZZZ000000_txtRiyoTeiin0Nen_1_2_textboxInput").click()
        self.find_element_by_id("tab02_ZZZ000000_txtRiyoTeiin0Nen_1_2_textboxInput").clear()
        self.find_element_by_id("tab02_ZZZ000000_txtRiyoTeiin0Nen_1_2_textboxInput").send_keys(self.test_data.get('利用定員'))

        self.find_element_by_id("tab02_ZZZ000000_selRiyoSettei1Nen_1_8_select").click()
        Select(self.find_element_by_id("tab02_ZZZ000000_selRiyoSettei1Nen_1_8_select")).select_by_visible_text(self.test_data.get('適用定員区分'))
        
        self.click_button_by_label("登録")  
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        self.save_screenshot_migrate(driver, "QAP114-002-035" , True)

        #QAP114-003施設事業所の認定情報の更新
        self.click_button_by_label("修正")
        self.find_element_by_id("tab02_QAPF200400_txtJigyoStartYMD_textboxInput").click()
        self.find_element_by_id("tab02_QAPF200400_txtJigyoStartYMD_textboxInput").clear()
        self.find_element_by_id("tab02_QAPF200400_txtJigyoStartYMD_textboxInput").send_keys(self.test_data.get("認定情報_事業開始年月日"))
        self.click_button_by_label("登録")  
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        self.save_screenshot_migrate(driver, "QAP114-003-004" , True)   

        #QAP114-004施設の更新履歴の表示     
        self.click_button_by_label("履歴")
        self.save_screenshot_migrate(driver, "QAP114-004-002" , True)         

        #QAP114-005施設検索画面で該当した施設・事業所の一覧表示
        self.goto_menu(["学童","施設管理","施設検索"],"1")
        self.save_screenshot_migrate(driver, "QAP114-005-004" , True)     
        self.find_element_by_id("tab03_QAPF201600_txtShisetsuKensakuHojinKanaNM_textboxInput").click()
        self.find_element_by_id("tab03_QAPF201600_txtShisetsuKensakuHojinKanaNM_textboxInput").clear()
        self.find_element_by_id("tab03_QAPF201600_txtShisetsuKensakuHojinKanaNM_textboxInput").send_keys(self.test_data.get("法人等カナ名称"))
        self.click_button_by_label("検索(Enter)")    
        self.save_screenshot_migrate(driver, "QAP114-005-007" , True)   
    

if __name__ == "__main__":
    unittest.main()
