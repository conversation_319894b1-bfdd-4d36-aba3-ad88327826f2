# -*- coding: utf-8 -*-
#QAP-102 2024年度以降流す方へ
#年度変更で年月等置換する場合は、年月が大きい値から先に繰り上げて置換してください。
import unittest
import time
from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP020102(KodomoSiteTestCaseBase):
    """TestQAP020102"""

    def __init__(self, *args, **kwargs):
        self.env_name = "student"
        super().__init__(*args, **kwargs)
        
    def setUp(self):
        sql_params_list = self.test_data.get("sql_params")
        self.exec_sqlfile("QAP102_実施前スクリプト.sql", params=sql_params_list)
        super().setUp()
    
    def test_case_qap020_102(self):
        '''test_case_qap020_102'''
        driver = None
        # ログイン
        self.do_login()
        # メニュー「子ども子育て支援」メニュー押下
        # メニュー「世帯情報」メニュー押下
        # メニュー「検索」メニューを押下        
        self.find_element_by_id(u"mainmenu_1").click()
        time.sleep(1)
        self.goto_menu(["学童","世帯情報","検索（学童）"],"1")
        self.save_screenshot_migrate(driver, "QAP100-001-004" , True)

        self.find_element_by_id("tab01_QAZF100001_txtJuminCD_textboxInput").click()
        self.find_element_by_id("tab01_QAZF100001_txtJuminCD_textboxInput").clear()
        self.find_element_by_id("tab01_QAZF100001_txtJuminCD_textboxInput").send_keys(self.test_data.get("宛名CD"))
        self.find_element_by_id("tab01_QAZF100001_cond_item_0_9_").click()
        self.click_button_by_label("検索(Enter)")
        self.save_screenshot_migrate(driver, "QAP102-001-007" , True)

        self.click_button_by_label("1")
        time.sleep(1)
        self.wait_page_loaded()
        self.save_screenshot_migrate(driver, "QAP102-001-009" , True)
        self.click_button_by_label("1")
        time.sleep(1)
        self.wait_page_loaded()
        self.save_screenshot_migrate(driver, "QAP102-001-011" , True)

        self.find_element_by_xpath("//li[@id='tab01_QAPF103500_NyushoKanri_li']/a/span").click()
        self.click_button_by_label("入室管理")
        self.save_screenshot_migrate(driver, "QAP102-001-013" , True)

        self.click_button_by_label("変更履歴一覧")
        self.save_screenshot_migrate(driver, "QAP102-001-015" , True)

        self.click_button_by_label("1")
        self.save_screenshot_migrate(driver, "QAP102-001-017" , True)

        self.click_button_by_label("閉じる")
        self.save_screenshot_migrate(driver, "QAP102-001-019" , True)

        self.find_element_by_xpath("//button[@id='btn_back']/span").click()
        self.save_screenshot_migrate(driver, "QAP102-001-021" , True)

        self.click_button_by_label("その他福祉情報")
        self.save_screenshot_migrate(driver, "QAP102-001-023" , True)

        self.find_element_by_xpath("//button[@id='btn_back']/span").click()
        self.save_screenshot_migrate(driver, "QAP102-001-025" , True)

        self.click_button_by_label("提出書類管理")
        self.save_screenshot_migrate(driver, "QAP102-001-027" , True)

        self.click_button_by_label("追加")
        self.find_element_by_id("tab01_ZZZ000000_chkTeishutsuShoruiINFOMiteishutsuY1_1_1chk0").click()
        self.click_button_by_label("登録")
        self.save_screenshot_migrate(driver, "QAP102-001-031" , True)
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        self.save_screenshot_migrate(driver, "QAP102-001-032" , True)

        self.find_element_by_xpath("//button[@id='btn_back']/span").click()
        time.sleep(1)
        self.click_button_by_label("印刷")
        self.save_screenshot_migrate(driver, "QAP102-001-035" , True)

        checkbox = self.find_element_by_id("tab01_QAPF900200_baseCheckBox0chk0")
        if checkbox.is_selected() == False:
            self.wait_page_loaded()
            self.find_element_by_id("tab01_QAPF900200_baseCheckBox0chk0").click()

        self.find_element_by_id("tab01_QAPF900200_section0").click()
        self.find_element_by_id(u"tab01_QAPF900200_QAPP300900_PRM_対象年月_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF900200_QAPP300900_PRM_対象年月_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF900200_QAPP300900_PRM_対象年月_textboxInput").send_keys(self.test_data.get("入室年月"))
        
        self.click_button_by_label("印刷")
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        self.save_screenshot_migrate(driver, "QAP102-001-040" , True)
        
        count = 1
        while count <= 50:
            try:
                status = self.find_element_by_xpath("//div[@id='tab01_ZZZ000000_lblStatus_1_5']/span")
                if "正常終了" == status.text.strip():
                    self.find_element_by_id("tab01_ZZZ000000_WrBtnNo_1_1_button").click()
                    break
                else:
                    count = count + 1
                    time.sleep(1)
            except:
                    count = count + 1
                    time.sleep(1)      

        self.click_button_by_label("閉じる")    
    

if __name__ == "__main__":
    unittest.main()
