# -*- coding: utf-8 -*-
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.select import Select
import unittest
from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010017(KodomoSiteTestCaseBase):
    def setUp(self):
        atena_list = self.test_data.get("sql_params")
        self.exec_sqlfile("QAP017削除スクリプト.sql", params=atena_list)
        super().setUp()
    
    def test_case_qap010_017(self):
        '''test_case_qap010_017'''
        driver = None
        #ログイン
        self.goto_kodomo_ninkagaishinseikanri_search()
        self.find_element_by_id("tab01_QAZF100001_txtJuminCD_textboxInput").click()
        self.find_element_by_id("tab01_QAZF100001_txtJuminCD_textboxInput").send_keys(self.test_data.get('QAP010_ATENACODE'))
        self.find_element_by_id("tab01_QAZF100001_WrCmnBtn05_button").click()
        self.save_screenshot_migrate(driver, "QAP017-001-005" , True)

        #世帯台帳画面表示
        self.find_element_by_id("tab01_ZZZ000000_btnSetaiRirekiNo_1_1_button").click()
        self.save_screenshot_migrate(driver, "QAP017-001-007" , True)
        self.find_element_by_xpath("//li[@id='tab01_QAPF100300_ninteijoho_li']/a/span").click()
        self.find_element_by_id("tab01_QAPF100300_btnYouchienSetaiJoho_button").click()

        #幼稚園世帯認定情報画面（追加）
        self.find_element_by_id("tab01_QAPF234000_btnAddChg_button").click()
        self.find_element_by_id("tab01_QAPF234000_txtKijunbi_textboxInput").click()
        self.find_element_by_id("tab01_ZZZ000000_chkZeiINFOTaisho_1_2chk0").click()
        self.find_element_by_id("tab01_QAPF234000_btnZeiGakuGet_button").click()
        self.find_element_by_id("tab01_ZZZ000000_selZeiINFOGassanKbnTonendo_1_9_select").click()
        Select(self.find_element_by_id("tab01_ZZZ000000_selZeiINFOGassanKbnTonendo_1_9_select")).select_by_visible_text(u"要")
        self.find_element_by_id("tab01_ZZZ000000_selZeiINFOGassanKbnZennendo_1_10_select").click()
        Select(self.find_element_by_id("tab01_ZZZ000000_selZeiINFOGassanKbnZennendo_1_10_select")).select_by_visible_text(u"要")
        self.find_element_by_id("tab01_QAPF234000_regbtn_button").click()
        self.find_element_by_id("tempId__1").click()
        self.save_screenshot_migrate(driver, "QAP017-001-017" , True)

        #幼稚園世帯認定情報画面（更新）
        self.find_element_by_id("tab01_QAPF234000_btnAddChg_button").click()
        self.find_element_by_id("tab01_ZZZ000000_selZeiINFOGassanKbnTonendo_1_9_select").click()
        self.find_element_by_id("tab01_ZZZ000000_selZeiINFOGassanKbnTonendo_1_9_select").click()
        self.find_element_by_id("tab01_ZZZ000000_selZeiINFOInputKbnT_1_13_select").click()
        Select(self.find_element_by_id("tab01_ZZZ000000_selZeiINFOInputKbnT_1_13_select")).select_by_visible_text(u"手入力")
        self.find_element_by_id("tab01_ZZZ000000_txtZeiINFOKintowariKeiTonendo_1_17_textboxInput").click()
        self.find_element_by_id("tab01_ZZZ000000_txtZeiINFOKintowariKeiTonendo_1_17_textboxInput").clear()
        self.find_element_by_id("tab01_ZZZ000000_txtZeiINFOKintowariKeiTonendo_1_17_textboxInput").click()
        self.find_element_by_id("tab01_ZZZ000000_txtZeiINFOKintowariKeiTonendo_1_17_textboxInput").send_keys("300")
        self.find_element_by_id("tab01_ZZZ000000_txtZeiINFOShotokuwariTonendo_1_21_textboxInput").click()
        self.find_element_by_id("tab01_ZZZ000000_txtZeiINFOShotokuwariTonendo_1_21_textboxInput").clear()
        self.find_element_by_id("tab01_ZZZ000000_txtZeiINFOShotokuwariTonendo_1_21_textboxInput").click()
        self.find_element_by_id("tab01_ZZZ000000_txtZeiINFOShotokuwariTonendo_1_21_textboxInput").send_keys("300")
        self.find_element_by_xpath(u"(.//*[normalize-space(text()) and normalize-space(.)='課税情報合計'])[1]/following::table[1]").click()
        self.find_element_by_id("tab01_QAPF234000_btnGassanchiDainyu_button").click()
        self.find_element_by_id("tab01_QAPF234000_regbtn_button").click()
        self.find_element_by_id("tempId__3").click()     
        self.save_screenshot_migrate(driver, "QAP017-002-007" , True)
    


if __name__ == "__main__":
    unittest.main()
