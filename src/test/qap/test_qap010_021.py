# -*- coding: utf-8 -*-
#QAP-001 2022年度以降流す方へ
#年度変更で年月等置換する場合は、年月が大きい値から先に繰り上げて置換してください。
from selenium.webdriver.support.select import Select
import unittest
from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010021(KodomoSiteTestCaseBase):
    def setUp(self):
        atena_list = self.test_data.get("sql_params")
        self.exec_sqlfile("QAP021_実施前スクリプト.sql", params=atena_list)
        super().setUp()
    
    def test_case_qap010_021(self):
        '''test_case_qap010_021'''
        driver = None
                
        #ログイン
        self.goto_kodomo_matsuta_shisetuhoikuryou()      
        self.save_screenshot_migrate(driver, "QAP021-001-004" , True)        
        self.find_element_by_id("tab01_QAPF108500_txtShisetuCode_textboxInput").click()
        self.find_element_by_id("tab01_QAPF108500_txtShisetuCode_textboxInput").clear()
        self.find_element_by_id("tab01_QAPF108500_txtShisetuCode_textboxInput").send_keys(self.test_data.get("QAP010_021_SHISETSUCODE"))
        self.find_element_by_id("body-webrings").click()
        self.find_element_by_id("tab01_QAPF108500_btnKensaku_button").click()
        self.find_element_by_id("tab01_QAPF108500_btnAddChg_button").click()
        self.find_element_by_id("tab01_QAPF108500_selKbn_select").click()      
        Select(self.find_element_by_id("tab01_QAPF108500_selKbn_select")).select_by_visible_text(u"従来園")
        self.find_element_by_id("tab01_QAPF108500_selSHHHHoGoSya_select").click()
        Select(self.find_element_by_id("tab01_QAPF108500_selSHHHHoGoSya_select")).select_by_visible_text(u"償還（利用者）")
        self.find_element_by_id("tab01_QAPF108500_selSHHHNyuEnRyou_select").click()
        Select(self.find_element_by_id("tab01_QAPF108500_selSHHHNyuEnRyou_select")).select_by_visible_text(u"償還（利用者）")
        self.find_element_by_id("tab01_ZZZ000000_txtHoikuryoGetugaku_1_4_textboxInput").click()
        self.find_element_by_id("tab01_ZZZ000000_txtHoikuryoGetugaku_1_4_textboxInput").clear()
        self.find_element_by_id("tab01_ZZZ000000_txtHoikuryoGetugaku_1_4_textboxInput").send_keys("1000")
        self.find_element_by_id("tab01_QAPF108500_regbtn_button").click()
        self.find_element_by_id("tempId__1").click()
        self.save_screenshot_migrate(driver, "QAP021-001-026" , True)
        self.find_element_by_id("tab01_QAPF108500_btnEditChg_button").click()
        self.find_element_by_id("tab01_ZZZ000000_txtNyuenryoNengaku_1_3_textboxInput").click()
        self.find_element_by_id("tab01_ZZZ000000_txtNyuenryoNengaku_1_3_textboxInput").clear()
        self.find_element_by_id("tab01_ZZZ000000_txtNyuenryoNengaku_1_3_textboxInput").send_keys("1000")
        self.find_element_by_id("tab01_ZZZ000000_txtNyuenryoNengaku_2_3_textboxInput").click()
        self.find_element_by_id("tab01_ZZZ000000_txtNyuenryoNengaku_2_3_textboxInput").clear()
        self.find_element_by_id("tab01_ZZZ000000_txtNyuenryoNengaku_2_3_textboxInput").send_keys("1000")
        self.find_element_by_id("tab01_ZZZ000000_txtNyuenryoNengaku_3_3_textboxInput").click()
        self.find_element_by_id("tab01_ZZZ000000_txtNyuenryoNengaku_3_3_textboxInput").clear()
        self.find_element_by_id("tab01_ZZZ000000_txtNyuenryoNengaku_3_3_textboxInput").send_keys("1000")
        self.find_element_by_id("tab01_ZZZ000000_txtNyuenryoNengaku_4_3_textboxInput").click()
        self.find_element_by_id("tab01_ZZZ000000_txtNyuenryoNengaku_4_3_textboxInput").clear()
        self.find_element_by_id("tab01_ZZZ000000_txtNyuenryoNengaku_4_3_textboxInput").send_keys("1000")
        self.find_element_by_xpath("//button[@id='tab01_QAPF108500_regbtn_button']/img").click()
        self.find_element_by_id("tempId__3").click()
        self.save_screenshot_migrate(driver, "QAP021-002-007" , True)
        self.find_element_by_id("tab01_QAPF108500_btnEditChg_button").click()
        self.find_element_by_id("tab01_QAPF108500_deletebtn_button").click()
        self.find_element_by_id("tempId__5").click()
        self.save_screenshot_migrate(driver, "QAP021-003-003" , True)
     

if __name__ == "__main__":
    unittest.main()
