from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC020_1050401(FukushiSiteTestCaseBase):
    """TestQAC020_1050401"""

    def setUp(self):
        case_data = self.test_data["TestQAC020_1050401"]
        # sql_params = {"TARGET_GYOUMU_CODE": "QAC020", "DELETE_ATENA_CODE": case_data.get("atena_code", ""), "TARGET_NENDO": case_data.get("nendoY", "")}
        # self.exec_sqlfile("Test_QAC020_1050401.sql", params=sql_params)
        super().setUp()

    # 有期関連書類の提出により、有期更新された対象者の情報を登録できることを確認する。
    def test_QAC020_1050401(self):
        """有期認定更新者の登録"""

        case_data = self.test_data["TestQAC020_1050401"]
        atena_code = case_data.get("atena_code", "")
        shinsei_shubetsu = case_data.get("shinsei_shubetsu", "")
        shinsei_riyuu = case_data.get("shinsei_riyuu", "")
        shinsei_ymd = case_data.get("shinsei_ymd", "")
        kaitei = case_data.get("kaitei", "")

        self.do_login()

        # 3 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 4 個人検索画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=atena_code)
        self.screen_shot("個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 「障害児福祉手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC020")
        self.screen_shot("受給者台帳画面_6")

        # 7 障害児福祉手当 受給者台帳画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 8 障害児福祉手当資格管理画面: 申請種別「資格喪失」選択 申請理由「死亡」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinsei_shubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinsei_riyuu)
        self.screen_shot("障害児福祉手当資格管理画面_8")

        # 9 障害児福祉手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 10 障害児福祉手当資格管理画面: 申請日「20230701」資格喪失日「20230630」
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinsei_ymd)
        self.form_input_by_id(idstr="TxtKaitei", value=kaitei)
        self.screen_shot("障害児福祉手当資格管理画面_10")

        # 11 障害児福祉手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 12 障害児福祉手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")
        self.screen_shot("障害児福祉手当 受給者台帳画面_12")
