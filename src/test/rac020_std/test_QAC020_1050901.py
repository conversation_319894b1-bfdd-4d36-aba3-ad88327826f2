from base.fukushi_case import FukushiSiteTestCaseBase



class TestQAC020_1050901(FukushiSiteTestCaseBase):
    """TestQAC020_1050901"""

    def setUp(self):
        super().setUp()

    # 福祉行政報告例25表を出力できることを確認する。。
    def test_QAC020_1050901(self):

        case_data = self.test_data["TestQAC020_1050901"]
        taisho_ym = case_data.get("taisho_ym", "")
        gyomu_name = case_data.get("gyomu_name", "")
        jigyo_name = case_data.get("jigyo_name", "")
        shori_kubun_name = case_data.get("shori_kubun_name", "")
        shori_bunrui_name = case_data.get("shori_bunrui_name", "")
        batch_job_001 = case_data.get("batch_job_001")

        # 1,2 メインメニュー画面:「総合福祉メニュー」
        self.do_login()
        self.screen_shot("メインメニュー画面_2")

        # 3 メインメニュー画面: メインメニューから「バッチ起動」ボタン押下
        self.batch_kidou_click()
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：障害 事業：特別障害者手当 処理区分：月次処理 処理分類：統計処理
        self.form_input_by_id(idstr="GyomuSelect", text=gyomu_name)
        self.form_input_by_id(idstr="JigyoSelect", text=jigyo_name)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=shori_kubun_name)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=shori_bunrui_name)
        self.screen_shot("バッチ起動画面_4")

        # 5 バッチ起動画面: 「福祉行政報告例25出力処理」のNoボタン押下
        self.click_batch_job_button_by_label(batch_job_001)

        # 6 バッチ起動画面: 対象年月「202307」
        params = [
            {"title": "対象年月", "type": "text", "value": taisho_ym}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_6")

        # 7 バッチ起動画面:「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面:「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.screen_shot("ジョブ実行履歴画面_8")

        # 9 ジョブ実行履歴画面:「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ実行履歴画面_9")

        # 10 ジョブ実行履歴画面:「帳票履歴」ボタン押下
        self.click_report_log()
        self.screen_shot("ジョブ帳票履歴画面_10")

        # 11 ジョブ帳票履歴画面:「検索」ボタン押下
        # 12 ジョブ帳票履歴画面:「福祉行政報告例25」のNoボタン押下
        # 13 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # 14 福祉行政報告例25（PDF）: ×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ帳票履歴画面_11")
