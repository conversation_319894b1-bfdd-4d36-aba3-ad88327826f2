import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01071502(FukushiSiteTestCaseBase):
    """TestQAJ010_01071502"""

    def setUp(self):
        super().setUp()
    
    def test_QAJ010_01071502(self):
        """負担額一括更新確認リスト作成"""

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")
        
        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")
        
        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")
        
        # 4 バッチ起動画面: 業務「障害」選択
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        
        # 5 バッチ起動画面: 事業「新高額障害サービス費」選択
        self.form_input_by_id(idstr="JigyoSelect", text="障害者総合支援")
        
        # 6 バッチ起動画面: 処理区分「月次処理」選択
        self.form_input_by_id(idstr="ShoriKubunSelect", text="年次処理")
        
        # 7 バッチ起動画面: 処理分類「支払処理」選択
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="負担額一括更新処理")
        
        # 8 バッチ起動画面: Noボタン押下
        self.find_element(By.ID,"Sel2").click()
        
        # 9 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_9")
        
        # 10 バッチ起動画面: 「処理開始」ボタン押下
        self.form_input_by_id(idstr="UQZGC402_PrintSelect", text="ファイルアウト")
        self.form_input_by_id(idstr="UQZGC402_chkPrinter", value="0")
        # ジョブ実行(実行した日時を保持しておく)
        exec_datetime = self.exec_batch_job()
        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        
        # 11 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.screen_shot("バッチ起動画面_11")

        # 12 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        
        # 処理が終わるまで待機する
        self.wait_job_finished(120,20)

        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 13 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_13")
        
        
        # 14 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()
        self.screen_shot("帳票履歴画面_14")

        # 今回の処理で作成したPDFのDL（戻値はDLしたファイル数）
        report_dl_count = self.get_job_report_pdf(exec_datetime=exec_datetime)
        self.return_click()
