import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060601(FukushiSiteTestCaseBase):
    """TestQAJ010_01060601"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060601"]
        super().setUp()
    
    # 相談支援開始年月・モニタリング月を表示できることを確認する。モニタリング結果報告月を登録できることを確認する。
    def test_QAJ010_01060601(self):
        """モニタリング実績登録_者_"""
        
        case_data = self.test_data["TestQAJ010_01060601"]
        atena_code = case_data.get("atena_code", "")
        insatsuChk = case_data.get("insatsuChk", "")
        hakkoChk = case_data.get("hakkoChk", "")
        hakkoTxt = case_data.get("hakkoTxt", "")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")
        
        # 4 個人検索画面: 表示
        self.screen_shot("個人検索画面_4")
        
        # 5 個人検索画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=case_data.get("atena_code",""))
        
        # 6 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        
        # 7 受給状況画面: 表示
        self.screen_shot("受給状況画面_7")
        
        # 8 受給状況画面: 「障害者総合支援」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAJ010")
        
        # 9 障害福祉サービス受給者台帳画面: 表示
        self.screen_shot("障害福祉サービス受給者台帳画面_9")
        
        # 10 障害福祉サービス受給者台帳画面: 「障害者総合支援申請管理」ボタン押下
        self.click_button_by_label("障害者総合支援申請管理")
        
        # 11 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_11")
        
        # 12 障害福祉サービス申請管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        
        # 13 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_13")
        
        # 14 障害福祉サービス申請管理画面: 「利用計画作成事業者」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("利用計画作成事業者")
        
        # 15 相談支援事業所登録画面: 表示
        self.screen_shot("相談支援事業所登録画面_15")
        
        # 16 相談支援事業所登録画面: 「実績管理」ボタン押下
        self.click_button_by_label("実績管理")
        
        # 17 相談支援実績登録画面: 表示
        self.screen_shot("相談支援実績登録画面_17")
        
        # 18 相談支援実績登録画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        
        # 19 相談支援実績登録画面: 表示
        self.screen_shot("相談支援実績登録画面_19")
        
        # 20 相談支援実績登録画面: 6月「モニタリング結果報告月」チェックボックス選択
        self.click_button_by_label("＜")
        self.find_element(By.ID,"ChkHoukoku_6").click()
        
        # 21 相談支援実績登録画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 22 相談支援実績登録画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.screen_shot("相談支援実績登録画面_22")
        
        # 23 相談支援事業所登録画面: 「戻る」ボタン押下
        self.return_click()
        
        # 24 相談支援事業所登録画面: 表示
        self.screen_shot("相談支援事業所登録画面_24")
        
