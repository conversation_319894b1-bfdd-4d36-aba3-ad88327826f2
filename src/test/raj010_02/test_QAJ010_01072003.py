import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01072003(FukushiSiteTestCaseBase):
    """TestQAJ010_01072003"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01072003"]
        super().setUp()
    
    # 給付判定結果情報作成処理を出力できることを確認する。
    def test_QAJ010_01072003(self):
        """給付判定結果情報作成処理"""
        
        case_data = self.test_data["TestQAJ010_01072003"]
        # atena_code = case_data.get("atena_code", "")
        # hakkoTxt = case_data.get("hakkoTxt", "")
        shoritaishoTxt = case_data.get("shoritaishoTxt", "")
        date = datetime.date.today()
        today = format(date, '%Y%m%d')

        self.do_login()
        
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")
        
        # 4 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_4")
        
        # 5 バッチ起動画面: 業務「障害」選択事業「障害者総合支援」選択処理区分「国保連連携　高額支給処理業務」選択処理分類「高額支給処理情報」選択
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="障害者総合支援")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="国保連連携　高額支給処理業務")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="高額支給処理情報")
        self.screen_shot("バッチ起動画面_5")
        
        # 6 バッチ起動画面: 「給付判定結果情報作成処理」No4ボタン押下
        self.click_button_by_label("4")
        
        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: パラメータ入力
        params = [
            {"title":"振込日", "type": "text", "value": today},
            {"title":"発行日", "type": "text", "value": today},
            {"title":"処理対象年月", "type": "text", "value": shoritaishoTxt},
            {"title":"出力順序", "type": "select", "value":"受給者番号（昇順）" }
        ]
        self.set_job_params(params)
        self.form_input_by_id(idstr="UQZGC402_PrintSelect", text="ファイルアウト")
        self.form_input_by_id(idstr="UQZGC402_chkPrinter", value="0")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")
        
        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 12 ジョブ実行履歴画面: 「検索」ボタン押下
        #self.find_element(By.ID,"SearchButton").click()
        
        # 13 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_13")
        
        # 17 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()
        # 今回の処理で作成したPDFのDL（戻値はDLしたファイル数）
        report_dl_count = self.get_job_report_pdf(exec_datetime=exec_datetime)
        self.return_click()
        
        # 20 メインメニュー画面: 表示
        self.screen_shot("ジョブ実行履歴画面_20")
