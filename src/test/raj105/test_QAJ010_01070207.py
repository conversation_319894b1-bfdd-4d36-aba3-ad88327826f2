import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01070207(FukushiSiteTestCaseBase):
    """TestQAJ010_01070207"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01070207"]
        sql_params = {"ATENA_CODE": case_data.get("atena_code", "")}
        self.exec_sqlfile("TestQAJ010_01070207.sql", params=sql_params)
        super().setUp()
    
    # 新高額支給判定結果を登録できることを確認する。
    def test_QAJ010_01070207(self):
        """新高額支給判定結果登録"""
        
        case_data = self.test_data["TestQAJ010_01070207"]
        atena_code = case_data.get("atena_code", "")
        #システム日付を変数に設定
        date = datetime.date.today()
        today = format(date, '%Y%m%d')

        #self.do_login()
        # 1 メインメニュー画面: 表示
        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        # 3 個人検索画面: 表示
        # 4 個人検索画面: 「住民コード」入力
        # 5 個人検索画面: 「検索」ボタン押下
        # 6 受給状況画面: 表示
        # 7 受給状況画面: 「新高額障害サービス費」ボタン押下
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ104")
        
        # 8 新高額資格管理画面: 表示
        self.screen_shot("新高額資格管理画面_8")
        
        # 9 新高額資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")
        
        # 10 新高額資格管理画面: 「決定年月日」入力決定結果「決定」選択
        self.form_input_by_id(idstr="TxtKetteiYMD", value=today)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")

        # 11 新高額資格管理画面: 受給者証番号「9999999999」入力対象有効期間開始日「20240101」入力対象有効期間終了日「20241231」入力
        #self.form_input_by_id(idstr="40101", value="9999999999")
        #self.form_input_by_id(idstr="40102", value="20240101")
        #self.form_input_by_id(idstr="40103", value="20241231")

        # 所得区分_65歳
        self.select_by_name("10103", text="非課税")

        # 介護保険利用有無
        self.select_by_name("10104", text="無")

        # 届出日
        self.find_element(By.ID,"10201").click()
        self.find_element(By.ID,"10201").send_keys(today)

        # 税年度
        self.select_by_name("10203", text="令和06年")
        
        # 所得区分
        self.select_by_name("10204", text="非課税")

        # self.find_element(By.ID,"40101").click()
        # self.find_element(By.ID,"40101").send_keys("9999999999")
        self.find_element(By.ID,"40102").click()
        self.find_element(By.ID,"40102").send_keys("20240101")
        self.find_element(By.ID,"40103").click()
        self.find_element(By.ID,"40103").send_keys("20241231")
        self.screen_shot("新高額資格管理画面_11")
        self.return_click()
        # 12 新高額資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 13 新高額資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("新高額資格管理画面_13")
        
