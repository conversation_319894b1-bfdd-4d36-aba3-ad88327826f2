import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB01119905(FukushiSiteTestCaseBase):
    """TESTRAB01119905"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_list = case_data.get("sql_params", {})
        self.exec_sqlfile("RAB011199-05.sql",  params=atena_list,db_key="db")
        self.exec_sqlfile("RAB011199-05.sql", params=atena_list,db_key="stddb")
        super().setUp()

    # 補装具情報が登録できることを確認する。補装具情報が検索できることを確認する。
    def test_RAB010_01119905(self):
        """補装具登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 「マスタメンテナンス」ボタン押下
        # self.master_maintenance_click()
        self.click_button_by_label("マスタメンテナンス")

        # 2 サブメニュー画面: 表示
        self.screen_shot("[011199-05]_サブメニュー画面_2")

        # 3 サブメニュー画面: 「用具マスタメンテナンス」ボタン押下
        self.click_button_by_label("用具マスタメンテナンス")

        # 4 用具マスタ画面: 表示
        self.screen_shot("[011199-05]_用具マスタ画面_4")

        # 5 用具マスタ画面: 事業「補装具費支給」選択
        self.form_input_by_id(idstr="CmbJigyo", text="補装具費支給")

        # 6 用具マスタ画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 7 用具マスタ画面: 表示
        self.screen_shot("[011199-05]_用具マスタ画面_7")

        # 8 用具マスタ画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 9 用具マスタ画面: 表示
        self.screen_shot("[011199-05]_用具マスタ画面_9")

        # 10 用具マスタ画面: 用具コード「999999」入力有効期間開始「20230401」入力用具略称「テスト略称」入力用具略称「テスト正式名称」入力
        self.form_input_by_id(idstr="TxtRegItemCode", value=case_data.get("RegItemCode", ""))
        self.form_input_by_id(idstr="TxtRegStartDate", value=case_data.get("RegStartDate", ""))
        self.form_input_by_id(idstr="TxtRegItemShort", value=case_data.get("RegItemShort", ""))
        self.form_input_by_id(idstr="TxtRegItemName", value=case_data.get("RegItemName", ""))
        self.form_input_by_id(idstr="TxtSyumokuMeiCode",value=case_data.get("SyumokuMeiCode", ""))

        # 11 用具マスタ画面: 「登録」ボタン押下
        self.click_button_by_label("登録／復活")
        self.alert_ok()

        # 12 用具マスタ画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("[011199-05]_用具マスタ画面_12")

        # 13 用具マスタ画面: 「初期表示」ボタン押下
        self.click_button_by_label("初期表示")

        # 14 用具マスタ画面: 事業「補装具費支給」選択用具略称「テスト略称」入力
        self.form_input_by_id(idstr="CmbJigyo", text=case_data.get("CmbJigyo", ""))
        self.form_input_by_id(idstr="TxtItemShort", value=case_data.get("TxtItemShort", ""))

        # 15 用具マスタ画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 16 用具マスタ画面: 表示
        self.screen_shot("[011199-05]_用具マスタ画面_16")

        # 17 用具マスタ画面: 「戻る」ボタン押下
        self.return_click()

        # 18 サブメニュー画面: 表示
        self.screen_shot("[011199-05]_サブメニュー画面_18")

        # 19 サブメニュー画面: 「戻る」ボタン押下
        self.return_click()

        # 20 メインメニュー画面: 表示
        self.screen_shot("[011199-05]_メインメニュー画面_20")
