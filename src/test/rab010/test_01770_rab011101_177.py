import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB011101177(FukushiSiteTestCaseBase):
    """TESTRAB011101177"""

    # 補装具申請者に対して、調査書を一括出力できることを確認する。
    def test_case_rab011101_177(self):
        """調査書一括出力"""
        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})

        self.do_login()
        # 1 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()  # Button with ID: CmdProcess4_1 instead of self.click_button_by_label("バッチ起動")

        # 2 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_2")

        # 3 バッチ起動画面: 業務：障害事業：補装具費支給処理区分：随時処理処理分類：補装具資格関連書類出力
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="補装具費支給")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="随時処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="補装具資格関連書類出力")
        self.screen_shot("バッチ起動画面_3")

        time.sleep(2)
        # 4 バッチ起動画面: 「調査書一括出力」のNoボタン押下
        self.click_batch_job_button_by_label("調査書一括出力")

        # 5 バッチ起動画面: 福祉事務所コード「○○」選択
        # TODO: params = [
        #    {"title": "福祉事務所コード", "type": "select", "value": "○○"}
        #]
        #self.set_job_params(params)
        self.screen_shot("バッチ起動画面_5")

        time.sleep(2)
        
        #6	バッチ起動画面	所管区「第一区」入力
        self.select_Option(driver,self.find_element(By.ID,"item4"),case_data.get("syokanku", ""))
        #6	バッチ起動画面	抽出開始日「20231101」入力
        self.form_input_by_id(idstr="item5", value=case_data.get("start_ymd", ""))
        #6	バッチ起動画面	抽出終了日「20231101」入力
        self.form_input_by_id(idstr="item6", value=case_data.get("end_ymd", ""))
        #6	バッチ起動画面	申請種類「0」入力
        self.form_input_by_id(idstr="item11", value=case_data.get("shinsei_shubetsu", ""))
        #6	バッチ起動画面	出力順序「1」入力
        self.select_Option(driver,self.find_element(By.ID,"item12"),case_data.get("order", ""))
        #6	バッチ起動画面	発行年月日「20231101」入力
        self.form_input_by_id(idstr="item13", value=case_data.get("hakkou_ymd", ""))
        #6	バッチ起動画面	補装具対象者一覧「1」入力
        self.form_input_by_id(idstr="item14", value=case_data.get("chouhyo1_flg", ""))
        #6	バッチ起動画面	調査書出力有無「1」入力
        self.form_input_by_id(idstr="item15", value=case_data.get("chouhyo2_flg", ""))

        
        # #6	バッチ起動画面	抽出開始日「20231101」入力
        # self.form_input_by_id(idstr="item4", value=case_data.get("start_ymd", ""))
        # #6	バッチ起動画面	抽出終了日「20231101」入力
        # self.form_input_by_id(idstr="item5", value=case_data.get("end_ymd", ""))
        # #6	バッチ起動画面	申請種類「0」入力
        # self.form_input_by_id(idstr="item11", value=case_data.get("shinsei_shubetsu", ""))
        # #6	バッチ起動画面	受給区分「0」入力
        # self.form_input_by_id(idstr="item12", value=case_data.get("jukyu_kubun", ""))
        # #6	バッチ起動画面	事業者コード 空白
        # self.form_input_by_id(idstr="item13", value=case_data.get("gyosha_code", ""))
        # #6	バッチ起動画面	請求有無「0」入力
        # self.form_input_by_id(idstr="item14", value=case_data.get("seikyu_flg", ""))
        # #6	バッチ起動画面	支払有無「0」入力
        # self.form_input_by_id(idstr="item15", value=case_data.get("shiharai_flg", ""))
        # #6	バッチ起動画面	出力順序「1」入力
        # self.select_Option(driver,self.find_element(By.ID,"item17"),case_data.get("order", ""))
        # #6	バッチ起動画面	発行年月日「20231101」入力
        # self.form_input_by_id(idstr="item18", value=case_data.get("hakkou_ymd", ""))
        # #6	バッチ起動画面	補装具対象者一覧「1」入力
        # self.form_input_by_id(idstr="item19", value=case_data.get("chouhyo1_flg", ""))
        # #6	バッチ起動画面	調査書出力有無「1」入力
        # self.form_input_by_id(idstr="item20", value=case_data.get("chouhyo2_flg", ""))
        # #6	バッチ起動画面	文書記号「123」入力
        # self.form_input_by_id(idstr="item21", value=case_data.get("bunsho_kigo2", ""))
        # #6	バッチ起動画面	文書番号「456」入力
        # self.form_input_by_id(idstr="item22", value=case_data.get("bunsho_bango2", ""))
        # #6	バッチ起動画面	枝番号「789」入力
        # self.form_input_by_id(idstr="item23", value=case_data.get("edaban2", ""))
        self.screen_shot("バッチ起動画面_6")

        # 7 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()  # Button with ID: ExecuteButton
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")

        # 8 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()  # Button with ID: JobListButton

        # 9 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_9")

        # 10 ジョブ実行履歴画面: 「検索」ボタン押下
        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(30,20)
        # 状態が「正常終了」であることを確認
        time.sleep(30)
        self.assert_job_normal_end(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 11 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_11")

        # 12 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()  # Button with ID: ReportListButton

        # 13 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_13")

        # 14 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 15 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_15")

        # 16 ジョブ帳票履歴画面: 「補装具対象者一覧」のNoボタン押下
        self.find_element(By.ID, "Sel2").click()
        self.find_element(By.CSS_SELECTOR, "html").click()
        # 17 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 18 帳票（PDF）: 表示
        self.screen_shot("帳票（PDF）_18")

        # 19 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # TODO: I don't know which function to close the pdf tab

        # 20 ジョブ帳票履歴画面: 「調査書」のNoボタン押下
        self.find_element(By.ID, "Sel1").click()
        self.find_element(By.CSS_SELECTOR, "html").click()
        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 22 帳票（PDF）: 表示
        self.screen_shot("帳票（PDF）_22")

        # 23 帳票（PDF）: ×ボタン押下でPDFを閉じる
        # TODO: I don't know which function to close the pdf tab

        # 24 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_24")
