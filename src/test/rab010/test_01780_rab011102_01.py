import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB01110201(FukushiSiteTestCaseBase):
    """TESTRAB01110201"""
    
    # 支給券の請求番号から請求消込登録をできることを確認する。
    def test_case_rab011102_01(self):
        """請求消込登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 「請求消込(新)」ボタン押下
        self.click_button_by_label("請求消込(新)")

        # 2 請求消込検索画面: 表示
        self.screen_shot("[011102-01]_請求消込検索画面_2")

        # 3 請求消込検索画面: 業務「障害」選択事業「補装具費支給」選択新規消込　選択
        self.form_input_by_id(idstr="CmbGyomu", text=case_data.get("CmbGyomu", "")) 
        self.form_input_by_id(idstr="CmbJigyo", text=case_data.get("CmbJigyo", "")) 
        self.form_input_by_id(idstr="Radio1_0", value=case_data.get("Radio1_0", "")) 
        self.screen_shot("[011102-01]_請求消込検索画面_3")

        # 4 請求消込検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 5 請求消込登録画面: 表示
        self.screen_shot("[011102-01]_請求消込登録画面_5")

        # 6 請求消込登録画面: 支給番号　入力
        self.form_input_by_id(idstr="TxtKyufuBango", value=case_data.get("TxtKyufuBango", "")) 

        # 7 請求消込登録画面: 「支給番号」ボタン押下
        self.click_button_by_label("支給番号")
        self.screen_shot("[011102-01]_請求消込登録画面_7")

        # 8 請求消込登録画面: 表示
        self.screen_shot("[011102-01]_請求消込登録画面_8")

        # 9 請求消込登録画面: 請求日「20231201」入力
        self.form_input_by_id(idstr="TxtSeikyuYMD", value=case_data.get("TxtSeikyuYMD", "")) 

        # 10 請求消込登録画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 11 請求消込登録画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("[011102-01]_請求消込登録画面_11")

        # 12 請求消込登録画面: 「１」ボタン押下
        self.click_button_by_label("1")

        # 13 補装具費支給資格管理画面: 表示
        self.screen_shot("[011102-01]_補装具費支給資格管理画面_13")
