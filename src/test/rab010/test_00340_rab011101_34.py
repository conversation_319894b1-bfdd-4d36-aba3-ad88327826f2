import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB01110134(FukushiSiteTestCaseBase):
    """TESTRAB01110134"""

    # 判定依頼入力済の補装具申請者に対して、判定結果を登録できることを確認する。
    def test_case_rab011101_34(self):
        """判定結果入力"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAB010")
        
        # 1 保健福祉総合システム > 福祉基本情報 > 履歴選択画面 > 「履歴選択一覧」の「資格管理」ボタン押下
        self.click_by_id("CmdButton1_1") 

        # 1 補装具費支給資格管理画面: 「判定結果入力」ボタン押下
        self.click_button_by_label("判定結果入力")

        # 2 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101-34]_補装具費支給資格管理画面_2")

        # 3 補装具費支給資格管理画面: 判定日「〇〇」入力
        self.form_input_by_id(idstr="TxtShintatsuHanteiYMD", value=case_data.get("TxtShintatsuHanteiYMD", ""))
        # 3 判定結果「決定（購入）」選択
        self.form_input_by_id(idstr="ShintasuHanteiCmb", text=case_data.get("ShintasuHanteiCmb", ""))
        # 3 判定書受付日「〇〇」入力
        self.form_input_by_id(idstr="TxtHanteiUketsukeYMD", value=case_data.get("TxtHanteiUketsukeYMD", ""))
        # 3 判定理由「判定理由欄入力テスト」入力
        self.find_element(By.ID, "TxtHanteiRiyu").click()
        self.find_element(By.ID, "TxtHanteiRiyu").send_keys(case_data.get("TxtHanteiRiyu", ""))
        # self.form_input_by_id(idstr="TxtHanteiRiyu", value=case_data.get("TxtHanteiRiyu", ""))
        # 3 判定職員氏名「判定」「氏名」入力
        self.form_input_by_id(idstr="TxtHanteiShokuinKanjiSei", value=case_data.get("TxtHanteiShokuinKanjiSei", ""))
        self.form_input_by_id(idstr="TxtHanteiShokuinKanjiMei", value=case_data.get("TxtHanteiShokuinKanjiMei", ""))

        # 4 補装具費支給資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 5 補装具費支給資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("[011101-34]_補装具費支給資格管理画面_5")
