import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB011101117(FukushiSiteTestCaseBase):
    """TESTRAB011101117"""

    # 補装具支給決定通知書を出力できることを確認する。
    def test_case_rab011101_117(self):
        """補装具支給決定通知書出力"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAB010")
        
        # 1 保健福祉総合システム > 福祉基本情報 > 履歴選択画面 > 「履歴選択一覧」の「資格管理」ボタン押下
        self.click_by_id("CmdButton1_1") 

        # 1 補装具費支給資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("[011101_117]_帳票印刷画面_2")

        # 3 帳票印刷画面: 「補装具支給決定通知書」行の印刷チェックボックス選択「補装具支給決定通知書」行の発行年月日チェックボックス選択発行年月日「〇〇」入力
        # self.print_online_reports(case_name="ケース名", report_name=case_data.get("form_name_0", ""), hakkou_ymd=case_data.get("hakkou_ymd", ""))
        report_param_list = [
            {
                "report_name": case_data.get("form_name_0", ""),
                "params": [
                    {"title": "発行年月日", "type": "text", "value": case_data.get("hakkou_ymd", "")}
                ]
            }
        ]
        self.print_online_reports(case_name="ケース名", report_param_list=report_param_list) 
        self.screen_shot("[011101_117]_帳票印刷画面_3")

        # 4 帳票印刷画面: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")

        # 5 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")

        # 6 帳票（PDF）: 表示
     
        # 7 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 8 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 9 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101_117]_補装具費支給資格管理画面_9")
