import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB011101164(FukushiSiteTestCaseBase):
    """TESTRAB011101164"""

    # 判定依頼入力済の補装具申請者に対して、判定結果を登録できることを確認する。
    def test_case_rab011101_164(self):
        """判定結果入力"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAB010")

        # 1 保健福祉総合システム > 福祉基本情報 > 履歴選択画面 > 「履歴選択一覧」の「資格管理」ボタン押下
        self.click_by_id("CmdButton1_1") 

        # 1 補装具費支給資格管理画面: 「判定結果入力」ボタン押下
        self.click_button_by_label("判定結果入力")
 
         # 2 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101-164]_補装具費支給資格管理画面_2")

         # 3 補装具費支給資格管理画面: 申請日「○○」入力
        #self.form_input_by_id(idstr="TxtShinseiYMD", value=case_data.get("TxtShinseiYMD", "")) #DEL
        # 3 補装具費支給資格管理画面: 判定日「○○」入力
        self.form_input_by_id(idstr="TxtShintatsuHanteiYMD", value=case_data.get("shintatsu_hantei_ymd", ""))
        # 3 補装具費支給資格管理画面: 判定結果「決定（借受）」選択
        self.form_input_by_id(idstr="ShintasuHanteiCmb", text=case_data.get("shintasu_hantei", ""))
        # 3 補装具費支給資格管理画面: 判定書受付日「○○」入力
        self.form_input_by_id(idstr="TxtHanteiUketsukeYMD", value=case_data.get("hantei_uketsuke_ymd", ""))
        # 3 補装具費支給資格管理画面: 判定理由「判定理由欄入力テスト」入力
        self.find_element(By.ID, "TxtHanteiRiyu").click()
        self.find_element(By.ID, "TxtHanteiRiyu").send_keys(case_data.get("hantei_riyu", ""))
        # self.form_input_by_id(idstr="TxtHanteiRiyu", value=case_data.get("hantei_riyu", ""))
        # 3 補装具費支給資格管理画面: 判定職員氏名「判定」「氏名」入力
        self.form_input_by_id(idstr="TxtHanteiShokuinKanjiSei", value=case_data.get("hantei_shokuin_kanji_sei", ""))
        self.form_input_by_id(idstr="TxtHanteiShokuinKanjiMei", value=case_data.get("hantei_shokuin_kanji_mei", ""))

        # 3 補装具費支給資格管理画面: 「福祉世帯情報」ボタン押下
        self.click_button_by_label("福祉世帯情報") 

        # 3 福祉世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了") 
        self.alert_ok() 

        # 4 補装具費支給資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 5 補装具費支給資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("[011101-164]_補装具費支給資格管理画面_5")
