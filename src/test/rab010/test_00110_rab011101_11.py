import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB01110111(FukushiSiteTestCaseBase):
    """TESTRAB01110111"""

    # 補装具申請者に対して、却下登録ができることを確認する。
    def test_case_rab011101_11(self):
        """支給却下情報登録_判定なし_"""

        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAB010")

        # 1 保健福祉総合システム > 福祉基本情報 > 履歴選択画面 > 「履歴選択一覧」の「資格管理」ボタン押下
        self.click_by_id("CmdButton1_1") 


        # 1 補装具費支給資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 補装具費支給資格管理画面: 表示
        #self.screen_shot("[011101-11]_補装具費支給資格管理画面_2")
        self.save_screenshot_migrate(driver, "[011101-11]_補装具費支給資格管理画面_2", True)

        # 3 補装具費支給資格管理画面: 決定結果「却下」選択
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=case_data.get("KetteiKekkaCmb", ""))

        # 4 補装具費支給資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 5 補装具費支給資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        #self.screen_shot("[011101-11]_補装具費支給資格管理画面_5")
        self.save_screenshot_migrate(driver, "[011101-11]_補装具費支給資格管理画面_5", True)
