import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01040212(FukushiSiteTestCaseBase):
    """TESTRAA01040212"""

    def test_case_raa010402_12(self):
        """test_case_raa010402_12"""
    
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        keittei_ymd_start = case_data.get("keittei_ymd_start", "")
        keittei_ymd_end = case_data.get("keittei_ymd_end", "")
        bunsho_no = case_data.get("bunsho_no", "")
        order = case_data.get("order", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        hantei_data = self.common_test_data.get("TESTRAA01040204", {})
        # 手帳交付方法
        techou_kouhu_houhou = hantei_data.get("techou_kouhu_houhou", "")

        if techou_kouhu_houhou == "郵送":
            return
        
        # ログイン
        self.do_login()
        self.batch_kidou_click()
        self.screen_shot("010402-12-02" )
        self.form_input_by_id( "GyomuSelect", text = "障害")
        self.form_input_by_id( "JigyoSelect", text = "精神手帳")
        self.form_input_by_id( "ShoriKubunSelect", text = "随時処理")
        self.form_input_by_id( "ShoriBunruiSelect", text = "帳票出力処理（一括）")        
        self.screen_shot("010402-12-07" )

        if not self.check_batch_job_exist(insatsu_tyouhyou_name):
            return

        # 「障害者手帳交付_再交付について出力処理」の行の数字ボタン押下
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name) 
        self.screen_shot("010402-12-09" )
        params = [
            {"title": "発行年月日", "type": "text", "value": hakkou_ymd},
            {"title": "文書番号", "type": "text", "value": bunsho_no},
            {"title": "決定年月日開始", "type": "text", "value": keittei_ymd_start},
            {"title": "決定年月日終了", "type": "text", "value": keittei_ymd_end},
            {"title": "出力順序", "type": "select", "value": order}
        ]
        self.set_job_params(params)             
        self.screen_shot("010402-12-10" ) 

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("010402-12-13" )

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.screen_shot("010402-12-16" )

        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(30,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("010402-12-18" )
        
        # No.の「１」行の「ダウンロード」ボタンを押下
        self.click_by_id("DownLoad1")
        self.screen_shot("010402-12-20" )

        # ダウンロード画面のNo.の「１」ボタンを押下
        self.get_job_output_files(case_name="ケース名")
        self.screen_shot("010402-12-22" )

        # 「ファイルを開く(O)」ボタンを押下

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 「戻る」ボタン押下
        self.return_click()
        self.return_click()
        self.screen_shot("010402-12-26" )

