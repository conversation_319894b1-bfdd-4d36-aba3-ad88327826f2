import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01040201(FukushiSiteTestCaseBase):
    """TESTRAA01040201"""

    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("RAA010402-01_実行前スクリプト_STD.sql", params=atena_list, db_key="stddb")
        self.exec_sqlfile("RAA010402-01_実行前スクリプト_NOSTD.sql", params=atena_list, db_key="db")
        super().setUp()

    def test_case_raa010402_01(self):
        """test_case_raa010402_01"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        # 発行年月日
        hakkou_ymd = case_data.get("hakkou_ymd", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA040")
        self.screen_shot("raa010402_01-08" )
        
        self.click_button_by_label("印刷")
        self.screen_shot("raa010402_01-10" )
        # 「申請書に切替」ボタン押下
        self.switch_online_report_type("申請書")

        # 「障害者手帳交付申請書」行の印刷チェックボックス選択
        report_param_list = [
            {
                "report_name": insatsu_tyouhyou_name,
                "params": [
                    {"title": "発行年月日", "type": "text", "value": hakkou_ymd}
                ]
            }
        ]
        self.print_online_reports(case_name="ケース名", report_param_list=report_param_list)
        # 「ファイルを開く(O)」ボタンを押下 メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        self.screen_shot("raa010402_01-14" )

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        self.screen_shot("raa010402_01-17" )
