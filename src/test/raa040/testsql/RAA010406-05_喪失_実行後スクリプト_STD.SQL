--精神手帳
DELETE FROM WR$$JICHITAI_CODE$$QA..QAZ受給状況 WHERE 業務コード = 'QAA040' AND 宛名コード IN ('$$ATENA_QAA040_SOUSHITSU02$$')
DELETE FROM WR$$JICHITAI_CODE$$QA..QAZ福祉世帯 WHERE 業務コード = 'QAA040' AND 本人宛名コード IN ('$$ATENA_QAA040_SOUSHITSU02$$') AND 福祉世帯員宛名コード IN ('$$ATENA_QAA040_SOUSHITSU02$$','$$ATENA_QAA040_SOUSHITSU02_HOGOSHA$$')
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA資格履歴 WHERE 業務コード = 'QAA040' AND 宛名コード IN ('$$ATENA_QAA040_SOUSHITSU02$$')
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA精神手帳資格内容 WHERE 業務コード = 'QAA040' AND 宛名コード IN ('$$ATENA_QAA040_SOUSHITSU02$$')
--精神通院
DELETE FROM WR$$JICHITAI_CODE$$QA..QAZ受給状況 WHERE 業務コード = 'QAG020' AND 宛名コード IN ('$$ATENA_QAA040_SOUSHITSU02$$')
DELETE FROM WR$$JICHITAI_CODE$$QA..QAG資格履歴 WHERE 業務コード = 'QAG020' AND 宛名コード IN ('$$ATENA_QAA040_SOUSHITSU02$$')
DELETE FROM WR$$JICHITAI_CODE$$QA..QAG資格内容 WHERE 業務コード = 'QAG020' AND 宛名コード IN ('$$ATENA_QAA040_SOUSHITSU02$$')

--QAZ受給状況
INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ受給状況 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,資格取得日,資格喪失日,申請年月日,申請種別,申請理由,進達年月日1,進達判定年月日1,進達結果1,進達年月日2,進達判定年月日2,進達結果2,決定年月日,決定結果,決定理由,進捗状況,業務固有コード1,業務固有コード2,業務固有コード3,複数申請フラグ,職権フラグ,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA040',744,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA040_SOUSHITSU02$$',N'20231223',N'99999999',N'20231221',7,1,N'20231222',N'00000000',0,N'00000000',N'00000000',0,N'20231223',1,0,N'0000300010',N'1040605',N'9887654319',N'',0,N'0',N'0',N'9501',N'9501','2023-12-21 20:04:04.237','2024-05-24 16:08:17.37',N'RAAF006 ');

--QAZ福祉世帯
INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ福祉世帯 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,本人宛名コード,福祉世帯員宛名コード,該当日,非該当日,本人から見た続柄,受給者との関係,汎用項目,同居別居コード,旧姓併記有無,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA040',743,0,N'$$JICHITAI_CODE$$',N'00000',N'',N'$$ATENA_QAA040_SOUSHITSU02$$',N'$$ATENA_QAA040_SOUSHITSU02$$',N'20230401',N'99999999',N'0000000001',N'0000000001',N'0000000000',N'0000000000',N'0',N'0',N'9501',N'9501','2023-12-21 20:04:04.143','2024-05-30 10:02:06.320',N'RAAF006 '),
	 (N'QAA040',743,0,N'$$JICHITAI_CODE$$',N'00000',N'',N'$$ATENA_QAA040_SOUSHITSU02$$',N'$$ATENA_QAA040_SOUSHITSU02_HOGOSHA$$',N'20230401',N'99999999',N'0000000007',N'0000000007',N'0000000000',N'0000000000',N'0',N'0',N'9501',N'9501','2023-12-21 20:04:04.17','2024-05-30 10:02:06.333',N'RAAF006 '),
	 (N'QAA040',744,0,N'$$JICHITAI_CODE$$',N'00000',N'',N'$$ATENA_QAA040_SOUSHITSU02$$',N'$$ATENA_QAA040_SOUSHITSU02$$',N'20230401',N'99999999',N'0000000001',N'0000000001',N'0000000000',N'0000000000',N'0',N'0',N'9501',N'9501','2023-12-21 20:04:04.143','2024-05-30 10:02:06.320',N'RAAF006 '),
	 (N'QAA040',744,0,N'$$JICHITAI_CODE$$',N'00000',N'',N'$$ATENA_QAA040_SOUSHITSU02$$',N'$$ATENA_QAA040_SOUSHITSU02_HOGOSHA$$',N'20230401',N'99999999',N'0000000007',N'0000000007',N'0000000000',N'0000000000',N'0',N'0',N'9501',N'9501','2023-12-21 20:04:04.17','2024-05-30 10:02:06.333',N'RAAF006 ');
--QAA資格履歴
INSERT INTO WR$$JICHITAI_CODE$$QA..QAA資格履歴 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,申請年月日,申請種別,申請理由,申請理由2,申請理由3,申請理由テキスト,申請内容入力日,進達年月日1,進達判定年月日1,進達結果1,進達内容入力日1,進達先機関コード1,進達年月日2,進達判定年月日2,進達結果2,進達内容入力日2,進達先機関コード2,決定年月日,決定結果,決定理由,決定理由テキスト,決定内容入力日,業務固有コード1,業務固有コード2,業務固有コード3,職権フラグ,受付場所コード,担当場所コード,変更日,資格状態コード,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA040',743,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA040_SOUSHITSU02$$',N'20231221',1,1,9,1,N'',N'20231221',N'20231222',N'00000000',0,N'20231221',N'0000000000',N'00000000',N'00000000',0,N'00000000',N'',N'20231223',1,0,N'かきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこ',N'20231221',N'1040605',N'9887654318',N'',N'0',N'0000000001',N'0000000002',N'20231218',N'0000000040',N'0',N'9501',N'9501','2023-12-21 20:04:04.113','2024-05-24 16:09:51.09',N'RAAF006 '),
	 (N'QAA040',744,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA040_SOUSHITSU02$$',N'20231221',7,1,9,2,N'',N'20231221',N'20231222',N'00000000',0,N'20231221',N'',N'00000000',N'00000000',0,N'00000000',N'',N'20231223',1,0,N'かきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこかきくけこ',N'20231221',N'1040605',N'9887654319',N'',N'0',N'0000000001',N'0000000002',N'20231218',N'0000000010',N'0',N'9501',N'9501','2023-12-21 20:11:55.51','2023-12-22 13:31:14.87',N'RAAF006 ');

--QAA精神手帳資格内容
INSERT INTO WR$$JICHITAI_CODE$$QA..QAA精神手帳資格内容 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,等級,有効期間開始,有効期間終了,添付書類コード,文書番号,同時申請フラグ,保険種別コード,医療機関コード,サービスコード,医師名_氏,医師名_名,主たる病名コード,主たる病名ICD10コード,従たる病名コード,従たる病名ICD10コード,合併症,入通院フラグ,交付年月日,再交付年月日,受渡年月日,事実発生年月日,返還年月日,旧手帳記号,旧手帳番号,手帳郵便番号1,手帳郵便番号2,手帳住所1,手帳住所2,手帳カナ氏名,手帳漢字氏名,送付先区分,写真貼付有無,添付書類種類コード1,添付書類有無1,添付書類種類コード2,添付書類有無2,添付書類種類コード3,添付書類有無3,添付書類種類コード4,添付書類有無4,添付書類種類コード5,添付書類有無5,添付書類種類コード6,添付書類有無6,添付書類種類コード7,添付書類有無7,添付書類種類コード8,添付書類有無8,添付書類種類コード9,添付書類有無9,添付書類種類コード10,添付書類有無10,希望手帳様式コード,手帳交付方法コード,手帳交付場所コード,宛先優先度コード,手帳印刷要否コード,手帳回収区分コード,更新のお知らせ要否コード,年金種類コード,年金証書番号,年金等級コード,年金照会先コード,障害年金の永久認定有無,障害年金の次回診断書提出年月,事務担当者,主たる精神障害,従たる精神障害,主たる精神障害の初診日,診断書作成医療機関の初診日,日常生活能力の程度コード,決裁日,等級変更状況コード,カード登録日,カード解除日,カード発行日,通知発送日,手帳引渡日,NHK受信料減免有無,NHK受信料減免お客様番号,備考,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA040',743,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA040_SOUSHITSU02$$',N'0000000002',N'20231225',N'20251231',N'0000000000',N'0000000000',N'0',N'0000000000',N'0000000001',N'0000000000',N'らりるれろらりるれろらりるれろらりるれろらりるれろらりるれろらりるれろらりるれろらりるれろらりるれろ',N'わいうえをわいうえをわいうえをわいうえをわいうえをわいうえをわいうえをわいうえをわいうえをわいうえ',N'0000000001',N'F01 ',N'0000000013',N'F13 ',N'はまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわはまやらわ',N'0',N'20231224',N'00000000',N'20230724',N'00000000',N'00000000',N'0000000000',N'',N'114',N'1114',N'あいうえ',N'かきくけ',N'ﾌｸｼ ﾀﾛｳｱ',N'福祉　太郎３４あ',N'0000000001',N'0',N'0000000001',N'0',N'0000000002',N'1',N'0000000003',N'0',N'0000000000',N'0',N'0000000000',N'0',N'0000000000',N'0',N'0000000000',N'0',N'0000000000',N'0',N'0000000000',N'0',N'0000000000',N'0',N'0000000001',N'0000000001',N'0000000001',N'0000000001',N'0',N'0000000002',N'1',N'0000000001',N'1111111111',N'0000000001',N'0000000003',N'0',N'202309',N'あかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたなあかさたな',N'いきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあ',N'うくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるう',N'20240103',N'20230605',N'0000000003',N'20231217',N'0000000000',N'20230616',N'20230719',N'20231005',N'20230726',N'20230928',N'0',N'1234567890',N'あいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこ',N'0',N'9501',N'9501','2023-12-21 20:04:04.127','2024-05-24 16:09:51.107',N'RAAF006 '),
	 (N'QAA040',744,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA040_SOUSHITSU02$$',N'0000000002',N'20231225',N'20251231',N'0000000000',N'0000000000',N'0',N'0000000000',N'0000000001',N'0000000000',N'aaaa',N'gbgbh',N'0000000001',N'F01 ',N'0000000013',N'F13 ',N'1111111',N'0',N'20231224',N'20231228',N'20230604',N'00000000',N'00000000',N'0000000000',N'',N'112',N'1112',N'あいうえお',N'かきくけこ',N'ﾌｸｼ ﾀﾛｳ',N'福祉　太郎３４',N'0000000000',N'0',N'0000000001',N'0',N'0000000002',N'1',N'0000000003',N'0',N'0000000004',N'0',N'0000000005',N'0',N'0000000006',N'0',N'0000000007',N'0',N'0000000008',N'0',N'0000000009',N'0',N'0000000010',N'0',N'0000000001',N'0000000001',N'0000000001',N'0000000001',N'0',N'0000000002',N'1',N'0000000001',N'11',N'0000000001',N'0000000003',N'0',N'202309',N'あああ',N'いきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあいきしちにひみいりあ',N'うくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるううくすつぬふむゆるう',N'20240103',N'20230605',N'0000000003',N'20231217',N'0000000001',N'20230616',N'20230719',N'20231005',N'20230726',N'20231226',N'0',N'1234567890',N'あいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこあいうえおかきくけこ',N'0',N'9501',N'9501','2023-12-21 20:11:55.527','2023-12-22 13:31:14.887',N'RAAF006 ');


--QAZ受給状況
INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ受給状況 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,資格取得日,資格喪失日,申請年月日,申請種別,申請理由,進達年月日1,進達判定年月日1,進達結果1,進達年月日2,進達判定年月日2,進達結果2,決定年月日,決定結果,決定理由,進捗状況,業務固有コード1,業務固有コード2,業務固有コード3,複数申請フラグ,職権フラグ,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAG020',1069,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA040_SOUSHITSU02$$',N'00000000',N'99999999',N'20240524',1,1,N'00000000',N'00000000',0,N'00000000',N'00000000',0,N'00000000',0,0,N'0000100001',N'',N'0000000060',N'',0,N'0',N'0',N'9501',N'9501','2024-05-24 16:28:04.95','2024-05-24 16:28:04.95',N'RAGF001 ');

--QAG資格履歴
INSERT INTO WR$$JICHITAI_CODE$$QA..QAG資格履歴 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,申請年月日,申請種別,申請理由,申請理由2,申請理由3,申請理由テキスト,申請内容入力日,進達年月日1,進達判定年月日1,進達結果1,進達内容入力日1,進達先機関コード1,進達年月日2,進達判定年月日2,進達結果2,進達内容入力日2,進達先機関コード2,決定年月日,決定結果,決定理由,決定理由テキスト,決定内容入力日,業務固有コード1,業務固有コード2,業務固有コード3,職権フラグ,受付場所コード,担当場所コード,変更日,資格状態コード,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAG020',1069,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA040_SOUSHITSU02$$',N'20240524',1,1,0,0,N'0',N'20240524',N'00000000',N'00000000',0,N'00000000',N'',N'00000000',N'00000000',0,N'00000000',N'',N'00000000',0,0,N'',N'00000000',N'',N'0000000060',N'',N'0',N'0000000000',N'0000000000',N'00000000',N'0000000000',N'0',N'9501',N'9501','2024-05-24 16:28:04.717','2024-05-24 16:28:04.717',N'RAGS002 ');

--QAG資格内容
INSERT INTO WR$$JICHITAI_CODE$$QA..QAG資格内容 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,有効期間開始,有効期間終了,汎用項目1,汎用項目2,汎用項目3,汎用項目4,汎用項目5,汎用項目6,文書番号,事由発生年月日,受付番号,交付方法コード,交付日,再交付日,結果受理日,進達番号,判定予定日,判定予定時間,期間調整有無,変更内容,研究同意,添付書類,診断書の種類コード,返却要否,保健所検収年月日,収納年月日,申請内容_有効期間開始,申請内容_有効期間終了,所得判定年度,性別出力,更新勧奨,同時申請,受給者証適用開始日,医療券_有効期間開始,医療券_有効期間終了,経過的特例有効期間開始日,経過的特例有効期間終了日,軽症者登録可否,予定期間,直近5年間の給付状況,備考,指導記録,添付書類種類コード1,添付書類有無1,添付書類種類コード2,添付書類有無2,添付書類種類コード3,添付書類有無3,添付書類種類コード4,添付書類有無4,添付書類種類コード5,添付書類有無5,添付書類種類コード6,添付書類有無6,添付書類種類コード7,添付書類有無7,添付書類種類コード8,添付書類有無8,添付書類種類コード9,添付書類有無9,添付書類種類コード10,添付書類有無10,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAG020',1069,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA040_SOUSHITSU02$$',N'00000000',N'99999999',N'0000000000',N'0000000000',N'0000000000',N'0000000000',N'0000000000',N'0000000000',N'0000000000',N'00000000',0,N'0000000000',N'00000000',N'00000000',N'00000000',N'0000000000',N'0',N'',N'0',N'',N'0',N'0000000000',N'0000000000',N'0',N'00000000',N'00000000',N'00000000',N'99999999',N'2023',N'0',N'0',N'0',N'00000000',N'00000000',N'99999999',N'00000000',N'99999999',N'0',0,N'',N'',N'',N'0000000001',N'0',N'0000000002',N'0',N'0000000003',N'0',N'0000000000',N'0',N'0000000000',N'0',N'0000000000',N'0',N'0000000000',N'0',N'0000000000',N'0',N'0000000000',N'0',N'0000000000',N'0',N'0',N'9501',N'9501','2024-05-24 16:28:04.747','2024-05-24 16:28:04.747',N'RAGS002 ');


