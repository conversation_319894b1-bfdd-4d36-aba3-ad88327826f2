DELETE WR$$JICHITAI_CODE$$QA..QAZ受給状況           WHERE 業務コード = 'QAA040' AND 宛名コード = '$$ATENA_QAA040_TOKYU_HENKOU$$' 
DELETE WR$$JICHITAI_CODE$$QA..QAA資格履歴           WHERE 業務コード = 'QAA040' AND 宛名コード = '$$ATENA_QAA040_TOKYU_HENKOU$$' 
DELETE WR$$JICHITAI_CODE$$QA..QAA精神手帳資格内容    WHERE 業務コード = 'QAA040' AND 宛名コード = '$$ATENA_QAA040_TOKYU_HENKOU$$' 
DELETE WR$$JICHITAI_CODE$$QA..QAZ福祉世帯           WHERE 業務コード = 'QAA040' AND 本人宛名コード = '$$ATENA_QAA040_TOKYU_HENKOU$$' 
DELETE WR$$JICHITAI_CODE$$QZ..QZ住所登録者マスタ     WHERE 業務コード = 'QAA040' AND 宛名コード = '$$ATENA_QAA040_TOKYU_HENKOU$$' 
DELETE WR$$JICHITAI_CODE$$QZ..QZ送付先マスタ        WHERE 業務コード = 'QAA040' AND 宛名コード = '$$ATENA_QAA040_TOKYU_HENKOU$$' 
DELETE WR$$JICHITAI_CODE$$QZ..QZ連絡先              WHERE 業務コード = 'QAA040' AND 宛名コード = '$$ATENA_QAA040_TOKYU_HENKOU$$' 
DELETE WR$$JICHITAI_CODE$$QZ..QZ緊急連絡先          WHERE 業務コード = 'QAA040' AND 宛名コード = '$$ATENA_QAA040_TOKYU_HENKOU$$' 
DELETE WR$$JICHITAI_CODE$$QZ..QZ居住地マスタ        WHERE 業務コード = 'QAA040' AND 宛名コード = '$$ATENA_QAA040_TOKYU_HENKOU$$' 


--QAZ受給状況
INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ受給状況 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,資格取得日,資格喪失日,申請年月日,申請種別,申請理由,進達年月日1,進達判定年月日1,進達結果1,進達年月日2,進達判定年月日2,進達結果2,決定年月日,決定結果,決定理由,進捗状況,業務固有コード1,業務固有コード2,業務固有コード3,複数申請フラグ,職権フラグ,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
('QAA040', 669, '0', '$$JICHITAI_CODE$$', '99301', '', '$$ATENA_QAA040_TOKYU_HENKOU$$', '20230801', '99999999', '20230801', '1', '1', '20230801', '00000000', '0', '00000000', '00000000', '0', '20230801', '1', '0', '0000100010', '1040401', '', '', '0', '1', '0', '9501', '9501', getdate(), getdate(), 'RAAF006 ');

--QAA資格履歴
INSERT INTO WR$$JICHITAI_CODE$$QA..QAA資格履歴 (業務コード, 履歴番号, 履歴分類, 自治体コード, 福祉事務所コード, 支所コード, 宛名コード, 申請年月日, 申請種別, 申請理由, 申請理由2, 申請理由3, 申請理由テキスト, 申請内容入力日, 進達年月日1, 進達判定年月日1, 進達結果1, 進達内容入力日1, 進達先機関コード1, 進達年月日2, 進達判定年月日2, 進達結果2, 進達内容入力日2, 進達先機関コード2, 決定年月日, 決定結果, 決定理由, 決定理由テキスト, 決定内容入力日, 業務固有コード1, 業務固有コード2, 業務固有コード3, 職権フラグ, 受付場所コード, 担当場所コード, 変更日, 資格状態コード, 削除フラグ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム) VALUES
('QAA040', 669, '0', '$$JICHITAI_CODE$$', '99301', '', '$$ATENA_QAA040_TOKYU_HENKOU$$', '20230801', '1', '1', '0', '0', '', '20240522', '20230801', '00000000', '0', '20240522', '0000000000', '00000000', '00000000', '0', '00000000', '', '20230801', '1', '0', '', '20240522', '1040401', '', '', '1', '0000000000', '0000000000', '00000000', '0000000040', '0', '9501', '9501', getdate(), getdate(), 'RAAF006 ');

--QAA精神手帳資格内容
INSERT INTO WR$$JICHITAI_CODE$$QA..QAA精神手帳資格内容 (業務コード, 履歴番号, 履歴分類, 自治体コード, 福祉事務所コード, 支所コード, 宛名コード, 等級, 有効期間開始, 有効期間終了, 添付書類コード, 文書番号, 同時申請フラグ, 保険種別コード, 医療機関コード, サービスコード, 医師名_氏, 医師名_名, 主たる病名コード, 主たる病名ICD10コード, 従たる病名コード, 従たる病名ICD10コード, 合併症, 入通院フラグ, 交付年月日, 再交付年月日, 受渡年月日, 事実発生年月日, 返還年月日, 旧手帳記号, 旧手帳番号, 手帳郵便番号1, 手帳郵便番号2, 手帳住所1, 手帳住所2, 手帳カナ氏名, 手帳漢字氏名, 送付先区分, 写真貼付有無, 添付書類種類コード1, 添付書類有無1, 添付書類種類コード2, 添付書類有無2, 添付書類種類コード3, 添付書類有無3, 添付書類種類コード4, 添付書類有無4, 添付書類種類コード5, 添付書類有無5, 添付書類種類コード6, 添付書類有無6, 添付書類種類コード7, 添付書類有無7, 添付書類種類コード8, 添付書類有無8, 添付書類種類コード9, 添付書類有無9, 添付書類種類コード10, 添付書類有無10, 希望手帳様式コード, 手帳交付方法コード, 手帳交付場所コード, 宛先優先度コード, 手帳印刷要否コード, 手帳回収区分コード, 更新のお知らせ要否コード, 年金種類コード, 年金証書番号, 年金等級コード, 年金照会先コード, 障害年金の永久認定有無, 障害年金の次回診断書提出年月, 事務担当者, 主たる精神障害, 従たる精神障害, 主たる精神障害の初診日, 診断書作成医療機関の初診日, 日常生活能力の程度コード, 決裁日, 等級変更状況コード, カード登録日, カード解除日, カード発行日, 通知発送日, 手帳引渡日, NHK受信料減免有無, NHK受信料減免お客様番号, 備考, 削除フラグ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム) VALUES
('QAA040', 669, '0', '$$JICHITAI_CODE$$', '99301', '', '$$ATENA_QAA040_TOKYU_HENKOU$$', '0000000003', '20230801', '20360531', '0000000000', '0000000000', '0', '0000000000', '0000000000', '0000000000', '', '', '0000000000', 'F00 ', '0000000000', '0000', '', '0', '20230801', '00000000', '00000000', '00000000', '00000000', '0000000000', '', '000', '0000', '', '', '', '', '0000000001', '0', '0000000001', '0', '0000000002', '0', '0000000003', '0', '0000000000', '0', '0000000000', '0', '0000000000', '0', '0000000000', '0', '0000000000', '0', '0000000000', '0', '0000000000', '0', '0000000001', '0000000002', '0000000000', '0000000000', '1', '0000000000', '1', '0000000000', '', '0000000000', '0000000000', '0', '000000', '', 'アルツハイマー病型認知症', '', '00000000', '00000000', '0000000000', '00000000', '0000000000', '00000000', '00000000', '00000000', '00000000', '00000000', '0', '', '', '0', '9501', '9501', getdate(), getdate(), 'RAAF006 ');

--QAZ福祉世帯
INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ福祉世帯 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,本人宛名コード,福祉世帯員宛名コード,該当日,非該当日,本人から見た続柄,受給者との関係,汎用項目,同居別居コード,旧姓併記有無,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
('QAA040', 669, '0', '$$JICHITAI_CODE$$', '00000', '', '$$ATENA_QAA040_TOKYU_HENKOU$$', '$$ATENA_QAA040_TOKYU_HENKOU$$', '20230801', '99999999', '0000000001', '0000000001', '0000000000', '0000000000', '0', '0', '9501', '9501', getdate(), getdate(), 'RAAF006 ');
