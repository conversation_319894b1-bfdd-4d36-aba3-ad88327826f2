DELETE WR$$JICHITAI_CODE$$QA..QAZ受給状況           WHERE 業務コード = 'QAA040' AND 宛名コード = '$$RAA01040515_ATENACODE01$$' 
DELETE WR$$JICHITAI_CODE$$QA..QAA資格履歴           WHERE 業務コード = 'QAA040' AND 宛名コード = '$$RAA01040515_ATENACODE01$$' 
DELETE WR$$JICHITAI_CODE$$QA..QAA精神手帳資格内容    WHERE 業務コード = 'QAA040' AND 宛名コード = '$$RAA01040515_ATENACODE01$$' 
DELETE WR$$JICHITAI_CODE$$QA..QAZ福祉世帯           WHERE 業務コード = 'QAA040' AND 本人宛名コード = '$$RAA01040515_ATENACODE01$$' 
DELETE WR$$JICHITAI_CODE$$QZ..QZ住所登録者マスタ     WHERE 業務コード = 'QAA040' AND 宛名コード = '$$RAA01040515_ATENACODE01$$' 
DELETE WR$$JICHITAI_CODE$$QZ..QZ送付先マスタ        WHERE 業務コード = 'QAA040' AND 宛名コード = '$$RAA01040515_ATENACODE01$$' 
DELETE WR$$JICHITAI_CODE$$QZ..QZ連絡先              WHERE 業務コード = 'QAA040' AND 宛名コード = '$$RAA01040515_ATENACODE01$$' 
DELETE WR$$JICHITAI_CODE$$QZ..QZ緊急連絡先          WHERE 業務コード = 'QAA040' AND 宛名コード = '$$RAA01040515_ATENACODE01$$' 
DELETE WR$$JICHITAI_CODE$$QZ..QZ居住地マスタ        WHERE 業務コード = 'QAA040' AND 宛名コード = '$$RAA01040515_ATENACODE01$$' 


--QAZ受給状況
INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ受給状況 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,資格取得日,資格喪失日,申請年月日,申請種別,申請理由,進達年月日1,進達判定年月日1,進達結果1,進達年月日2,進達判定年月日2,進達結果2,決定年月日,決定結果,決定理由,進捗状況,業務固有コード1,業務固有コード2,業務固有コード3,複数申請フラグ,職権フラグ,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
('QAA040', 994, '0', '$$JICHITAI_CODE$$', '99301', '', '$$RAA01040515_ATENACODE01$$', '20230301', '99999999', '20230301', '1', '1', '20230301', '00000000', '0', '00000000', '00000000', '0', '20230301', '1', '0', '0000100010', '1040515', '', '', '0', '0', '0', '9501', '9501', getdate(), getdate(), 'RAAF006 ');

--QAA資格履歴
INSERT INTO WR$$JICHITAI_CODE$$QA..QAA資格履歴 (業務コード, 履歴番号, 履歴分類, 自治体コード, 福祉事務所コード, 支所コード, 宛名コード, 申請年月日, 申請種別, 申請理由, 申請内容入力日, 進達年月日1, 進達判定年月日1, 進達結果1, 進達内容入力日1, 進達年月日2, 進達判定年月日2, 進達結果2, 進達内容入力日2, 決定年月日, 決定結果, 決定理由, 決定内容入力日, 業務固有コード1, 業務固有コード2, 業務固有コード3, 職権フラグ, 削除フラグ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム) VALUES
('QAA040', 994, '0', '$$JICHITAI_CODE$$', '99301', '', '$$RAA01040515_ATENACODE01$$', '20230301', '1', '1', '20240529', '20230301', '00000000', '0', '20240529', '00000000', '00000000', '0', '00000000', '20230301', '1', '0', '20240529', '1040515', '', '', '0', '0', '9501', '9501', getdate(), getdate(), 'RAAF006 ');

--QAA精神手帳資格内容
INSERT INTO WR$$JICHITAI_CODE$$QA..QAA精神手帳資格内容 (業務コード, 履歴番号, 履歴分類, 自治体コード, 福祉事務所コード, 支所コード, 宛名コード, 等級, 有効期間開始, 有効期間終了, 添付書類コード, 文書番号, 同時申請フラグ, 保険種別コード, 医療機関コード, サービスコード, 主たる病名コード, 主たる病名ICD10コード, 従たる病名コード, 従たる病名ICD10コード, 合併症, 入通院フラグ, 交付年月日, 再交付年月日, 受渡年月日, 事実発生年月日, 返還年月日, 旧手帳記号, 旧手帳番号, 手帳郵便番号1, 手帳郵便番号2, 手帳住所1, 手帳住所2, 手帳カナ氏名, 手帳漢字氏名, 送付先区分, 写真貼付有無, 削除フラグ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム) VALUES
('QAA040', 994, '0', '$$JICHITAI_CODE$$', '99301', '', '$$RAA01040515_ATENACODE01$$', '0000000003', '20230301', '20351130', '0000000000', '0000000000', '0', '0000000000', '0000000000', '0000000000', '0000000003', 'F03 ', '0000000000', '0000', '', '0', '20230301', '00000000', '00000000', '00000000', '00000000', '0000000000', '', '000', '0000', '', '', '', '', '0000000001', '0', '0', '9501', '9501', getdate(), getdate(), 'RAAF006 ');

--QAZ福祉世帯
INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ福祉世帯 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,本人宛名コード,福祉世帯員宛名コード,該当日,非該当日,本人から見た続柄,受給者との関係,汎用項目,同居別居コード,旧姓併記有無,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
('QAA040', 994, '0', '$$JICHITAI_CODE$$', '00000', '', '$$RAA01040515_ATENACODE01$$', '$$RAA01040515_ATENACODE01$$', '20230301', '99999999', '0000000001', '0000000001', '0000000000', '0000000000', '0', '0', '9501', '9501', getdate(), getdate(), 'RAAF006 ');