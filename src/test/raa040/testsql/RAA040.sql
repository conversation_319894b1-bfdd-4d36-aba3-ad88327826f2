DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

--QAA041,QAA042,QAA043
DELETE WR$$JICHITAI_CODE$$QA..QAA資格履歴					WHERE 宛名コード = '$$RAA040_ATENACODE01$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QA..QAA精神手帳資格内容			WHERE 宛名コード = '$$RAA040_ATENACODE01$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QA..QAZ受給状況					WHERE 宛名コード = '$$RAA040_ATENACODE01$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QA..QAZ福祉世帯					WHERE 本人宛名コード = '$$RAA040_ATENACODE01$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QZ..QZ住所登録者マスタ			WHERE 宛名コード = '$$RAA040_ATENACODE01$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QZ..QZ送付先マスタ				WHERE 宛名コード = '$$RAA040_ATENACODE01$$'
DELETE WR$$JICHITAI_CODE$$QZ..QZ連絡先						WHERE 宛名コード = '$$RAA040_ATENACODE01$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QZ..QZ緊急連絡先					WHERE 宛名コード = '$$RAA040_ATENACODE01$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QZ..QZ居住地マスタ				WHERE 宛名コード = '$$RAA040_ATENACODE01$$'

--QAA044
DELETE WR$$JICHITAI_CODE$$QA..QAZ帳票発行履歴				WHERE 宛名コード = '$$RAA040_ATENACODE01$$' AND 業務コード = 'QAA040'

--QAA045
DELETE WR$$JICHITAI_CODE$$QA..QAA資格履歴					WHERE 宛名コード = '$$RAA040_ATENACODE02$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QA..QAA精神手帳資格内容			WHERE 宛名コード = '$$RAA040_ATENACODE02$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QA..QAZ受給状況					WHERE 宛名コード = '$$RAA040_ATENACODE02$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QA..QAZ福祉世帯					WHERE 本人宛名コード = '$$RAA040_ATENACODE02$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QZ..QZ住所登録者マスタ			WHERE 宛名コード = '$$RAA040_ATENACODE02$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QZ..QZ送付先マスタ				WHERE 宛名コード = '$$RAA040_ATENACODE02$$'
DELETE WR$$JICHITAI_CODE$$QZ..QZ連絡先						WHERE 宛名コード = '$$RAA040_ATENACODE02$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QZ..QZ緊急連絡先					WHERE 宛名コード = '$$RAA040_ATENACODE02$$' AND 業務コード = 'QAA040'
DELETE WR$$JICHITAI_CODE$$QZ..QZ居住地マスタ				WHERE 宛名コード = '$$RAA040_ATENACODE02$$'


IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END