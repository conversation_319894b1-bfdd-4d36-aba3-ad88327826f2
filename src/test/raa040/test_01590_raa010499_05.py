import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01049905(FukushiSiteTestCaseBase):
    """TESTRAA01049905"""

    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        self.exec_sqlfile("RAA010499_05.sql")
        super().setUp()
        
    def test_case_raa010499_05(self):
        """test_case_raa010499_05"""
        driver =None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        yuko_kikan_start_ymd = case_data.get("yuko_kikan_start_ymd", "")
        yuko_kikan_end_ymd = case_data.get("yuko_kikan_end_ymd", "")

        self.do_login()

        self.click_button_by_label("マスタメンテナンス")
        self.click_button_by_label("病名マスタメンテナンス")
        self.screen_shot("raa010499_05_04")
        
        self.form_input_by_id(idstr="CmbGyomu", text = "障害")        
        self.form_input_by_id(idstr="CmbJigyo", text = "精神手帳")
        self.click_button_by_label("確定")
        self.screen_shot("raa010499_05_08")

        self.find_element(By.ID,"CmdKensaku").click()
        self.screen_shot("raa010499_05_10")

        self.find_element(By.ID,"CmdTsuika").click()
        self.screen_shot("raa010499_05_12")

        self.find_element(By.ID,"CmdSTsuika").click()
        self.screen_shot("raa010499_05_14")

        self.find_element(By.ID,"ChkAutoBan").click()
        self.form_input_by_id(idstr= "TxtYukoKStrYMD", value =  yuko_kikan_start_ymd)
        self.form_input_by_id(idstr= "TxtYukoKEndYMD", value =  yuko_kikan_end_ymd)
        self.find_element(By.ID,"TxtKanaName").click()
        self.find_element(By.ID,"TxtKanaName").clear()
        self.execute_script('document.getElementById("TxtKanaName").value="%s";' %'ﾃｽﾄﾋﾞｮｳﾒｲ')
        
        self.form_input_by_id(idstr= "TxtRyakusho", value =  "テスト病名略称")
        self.form_input_by_id(idstr= "TxtSMeisho", value =  "テスト病名正式名称")
        self.form_input_by_id(idstr= "TxtTNenreiKagen", value =  "0")
        self.form_input_by_id(idstr= "TxtTNenreiJyogen", value =  "254")
        self.form_input_by_id(idstr= "TxtHJyunjyo", value =  "999")
        self.form_input_by_id(idstr= "TxtICDTenCode", value =  "G99")
        self.find_element(By.ID,"CmdTourokuF").click()
        self.alert_ok()
        self.screen_shot("raa010499_05_17")

        self.return_click()
        self.screen_shot("raa010499_05_19")
        
        self.find_element(By.ID,"CmbGyomu").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbGyomu"),"障害")
        self.find_element(By.ID,"CmbJigyo").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbJigyo"),"精神手帳")
        self.click_button_by_label("確定")
        self.screen_shot("raa010499_05_23")

        self.find_element(By.ID,"TxtSeishikiMeisho").click()
        self.form_input_by_id(idstr="TxtSeishikiMeisho", value =  "テスト病名正式名称")
        self.form_input_by_id(idstr= "ChkYuukouKGai", value =  "1")
        self.find_element(By.ID,"CmdKensaku").click()
        self.screen_shot("raa010499_05_26")

        self.click_by_id("Sel1")
        self.screen_shot("raa010499_05_28")

        self.find_element(By.ID,"CmdSakujo").click()
        self.alert_ok()
        self.screen_shot("raa010499_05_30")

        self.return_click()
        self.screen_shot("raa010499_05_32")
        
        self.return_click()
        self.screen_shot("raa010499_05_34")
        
        self.return_click()
        self.screen_shot("raa010499_05_36")
