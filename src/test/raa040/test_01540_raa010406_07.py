import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01040607(FukushiSiteTestCaseBase):
    """TESTRAA01040607"""
    
    # 各テストメソッドの実行後に実行したいもの
    def tearDown(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("RAA01040607.sql", params=atena_list)
        super().tearDown()
    
    def test_case_raa010406_07(self):
        """test_case_raa010406_07"""
        driver =None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        kettei_ymd = case_data.get("kettei_ymd", "")
        kettei_kekka = case_data.get("kettei_kekka", "")
        kessai_ymd = case_data.get("kessai_ymd", "")
        henkan_ymd = case_data.get("henkan_ymd", "")
        card_kaijo_ymd = case_data.get("card_kaijo_ymd", "")
        kettei_riyu = case_data.get("kettei_riyu", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA040")
        self.screen_shot("raa010406_07-08" )

        self.find_element(By.ID,"CmdKettei").click()
        #決定日「20240103」入力
        # 決定結果「決定」選択
        self.find_element(By.ID,"TxtKetteiYMD").clear()
        self.form_input_by_id(idstr="TxtKetteiYMD", value = kettei_ymd)
        self.find_element(By.ID,"KetteiKekkaCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"KetteiKekkaCmb"), kettei_kekka)
        self.screen_shot("raa010406_07-10" )
        # 決定理由「○○(パラメータ指定)」
        if kettei_riyu != "":
            self.form_input_by_id(idstr="KetteiRiyuCmb", text = kettei_riyu)
        # 決定理由詳細「決定理由詳細テキスト」
        self.form_input_by_id(idstr="TxtKetteiRiyu", value = "決定理由詳細テスト")
        # 決裁日「20240104」入力
        self.form_input_by_id(idstr="TxtKessaiYMD", value = kessai_ymd)
        # 返還日「20240105」入力
        self.find_element(By.ID,"TxtHenkanYMD").clear()
        self.form_input_by_id(idstr= "TxtHenkanYMD", value =  henkan_ymd)
        # カード解除日「（パラメータ指定）」入力
        self.form_input_by_id(idstr="TxtCardKaijoYMD", value = card_kaijo_ymd)
        self.screen_shot("raa010406_07-12" )
        
        self.click_button_by_label("登録")
        self.alert_ok()
        self.screen_shot("raa010406_07-14" )

