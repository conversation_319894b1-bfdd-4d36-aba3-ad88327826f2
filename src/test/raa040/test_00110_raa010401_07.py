import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01040107(FukushiSiteTestCaseBase):
    """TESTRAA01040107"""
    
    def test_case_raa010401_07(self):
        """test_case_raa010401_07"""

        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        kettei_ymd = case_data.get("kettei_ymd", "")
        kessai_ymd = case_data.get("kessai_ymd", "")
        techou_no = case_data.get("techou_no", "")
        koufu_ymd = case_data.get("koufu_ymd", "")
        card_touroku_ymd = case_data.get("card_touroku_ymd", "")
        card_kaijo_ymd = case_data.get("card_kaijo_ymd", "")
        card_hakko_ymd = case_data.get("card_hakko_ymd", "")
        ukewatashi_ymd = case_data.get("ukewatashi_ymd", "")
        tuchi_hasso_ymd = case_data.get("tuchi_hasso_ymd", "")
        techo_hikiwatashi_ymd = case_data.get("techo_hikiwatashi_ymd", "")

        # ログイン
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA040")
        self.screen_shot("raa010401_07-01")

        # 「修正」ボタン押下
        self.click_button_by_label("修正")        
        self.form_input_by_id(idstr="TxtKetteiYMD", value = kettei_ymd)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text = "決定")
        self.form_input_by_id(idstr="TxtKetteiRiyu", value = "決定理由詳細テスト")
        self.form_input_by_id(idstr="TxtKessaiYMD", value = kessai_ymd)
        self.form_input_by_id(idstr="TxtTechouNo",  value = techou_no)
        self.form_input_by_id(idstr="ToukyuuCmb", text = "３級")
        self.form_input_by_id(idstr="TxtKoufuYMD", value = koufu_ymd)
        self.form_input_by_id(idstr="TxtCardTourokuYMD", value = card_touroku_ymd)
        self.form_input_by_id(idstr="TxtCardKaijoYMD", value = card_kaijo_ymd)
        self.form_input_by_id(idstr="TxtCardHakkoYMD", value = card_hakko_ymd)
        self.form_input_by_id(idstr="TxtUkewatashiYMD", value = ukewatashi_ymd)
        self.form_input_by_id(idstr="TxtTuchiHassoYMD", value = tuchi_hasso_ymd)
        self.form_input_by_id(idstr="TxtTechoHikiwatashiYMD", value = techo_hikiwatashi_ymd)
        self.screen_shot("raa010401_07-03")

        # 「有効期間」ボタン押下
        self.click_button_by_label("有効期間")
        self.screen_shot("raa010401_07-05")

        # 「登録」ボタン押下
        self.click_button_by_label("登録")        
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.screen_shot("raa010401_07-07")