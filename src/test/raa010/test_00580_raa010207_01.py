import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01020701(FukushiSiteTestCaseBase):
    """TESTRAA01020701"""

    def test_case_raa010207_01(self):
        """test_case_raa010207_01"""
        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        taisho_ym = case_data.get("taisho_ym", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        bunsho_no = case_data.get("bunsho_no", "")
        order = case_data.get("order", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        # ログイン
        self.do_login()
        self.batch_kidou_click()
        self.save_screenshot_migrate(driver, "010207-01-02" , True)

        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="身体障害者手帳")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="随時処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="帳票出力処理（一括）")
        self.save_screenshot_migrate(driver, "010207-01-07" , True)

        #「身体障害者手帳の再認定について_通知_出力処理」の行の数字ボタン押下
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name)
        self.save_screenshot_migrate(driver, "010207-01-09" , True)
        params = [
            {"title": "発行年月日", "type": "text", "value": hakkou_ymd},
            {"title": "文書番号", "type": "text", "value": bunsho_no},
            {"title": "対象年月", "type": "text", "value": taisho_ym},
            {"title": "出力順序", "type": "select", "value": order}
        ]
        self.set_job_params(params)
        self.save_screenshot_migrate(driver, "010207-01-10" , True)

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        self.save_screenshot_migrate(driver, "010207-01-11" , True)

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.save_screenshot_migrate(driver, "010207-01-16" , True)

        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(30,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.save_screenshot_migrate(driver, "010207-01-18" , True)
        
        # No.の「１」行の「ダウンロード」ボタンを押下
        self.click_by_id("DownLoad1")
        self.save_screenshot_migrate(driver, "010207-01-20" , True)

        # ダウンロード画面のNo.の「１」ボタンを押下
        self.get_job_output_files(case_name="ケース名")
        self.save_screenshot_migrate(driver, "010207-01-22" , True)

        # 「ファイルを開く(O)」ボタンを押下

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 「戻る」ボタン押下
        self.return_click()

        # 「帳票履歴」ボタン押下
        self.click_report_log()
        self.save_screenshot_migrate(driver, "010207-01-22_「帳票履歴」ボタン押下" , True)

        # 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="ケース名")
        self.save_screenshot_migrate(driver, "010207-01-22_「検索」ボタン押下" , True)

        # 帳票（PDF）表示
        # self.click_by_id("Sel1")
        # self.save_screenshot_migrate(driver, "010207-01-22_「ファイルを開く」ボタン押下" , True)

        # 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        self.save_screenshot_migrate(driver, "010207-01-26" , True)

