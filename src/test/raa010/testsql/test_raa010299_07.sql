DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

DELETE WR$$JICHITAI_CODE$$QZ..QZ医療機関マスタ 	WHERE 医療機関コード = '0211234567'


INSERT INTO WR$$JICHITAI_CODE$$QA..QAZユーザーコードマスタ( 
		自治体コード
		, 業務コード
		, ユーザーコード
		, コード区分
		, 有効期間開始
		, 有効期間終了
		, コード略称
		, コード名称
		, 表示順序
		, 設定値1
		, 設定値2
		, 設定値3
		, 設定値4
		, 設定値5
		, 備考
		, 削除フラグ
		, データ作成担当者
		, データ更新担当者
		, データ作成日時
		, データ更新日時
		, データ更新プログラム) 
	values 
		('99101','QAZ010','DD02000001','0000QAA040','00000000','99999999','test','test2',6,'','','','','','医療機関備考管理対象業務','0','FUJII_難病2次検証時','FUJII_難病2次検証時','2018/01/31 18:49:21.973','2018/01/31 18:49:21.973','00000000');

IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END