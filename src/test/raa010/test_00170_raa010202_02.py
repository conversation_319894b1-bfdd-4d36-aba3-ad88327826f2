import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01020202(FukushiSiteTestCaseBase):
    """TESTRAA01020202"""

    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("RAA010202-01_NOSTD.sql", params=atena_list,db_key="db")
        self.exec_sqlfile("RAA010202-01_STD.sql", params=atena_list,db_key="stddb")
        super().setUp()

    def test_case_raa010202_02(self):
        """test_case_raa010202_02"""
        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        shinsei_shubetsu = case_data.get("shinsei_shubetsu", "")
        shinsei_riyuu = case_data.get("shinsei_riyuu", "")
        is_auto_haiban = case_data.get("is_auto_haiban", False)
        uketsuke_bangou = case_data.get("uketsuke_bangou", "")
        uketsuke_basho = case_data.get("uketsuke_basho", "")
        tantou_basho = case_data.get("tantou_basho", "")
        tanto_shokatsuku = case_data.get("tanto_shokatsuku", "")
        techo_kouhu_basho = case_data.get("techo_kouhu_basho", "")
        shutaru_shougai = case_data.get("shutaru_shougai", "")
        shinsei_ymd = case_data.get("shinsei_ymd", "")
        sainintei_tokusoku_ymd = case_data.get("sainintei_tokusoku_ymd", "")
        irai_ymd = case_data.get("irai_ymd", "")
        houkoku_ymd = case_data.get("houkoku_ymd", "")
        kaitou_ymd = case_data.get("kaitou_ymd", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA010")
        #申請内容入力
        self.click_button_by_label("申請内容入力")
        self.save_screenshot_migrate(driver, "保健福祉総合システム_福祉基本情報_ 個人検索表示用のメニュー_raa010202_02_1", True)

        # 福祉基本情報_身体障害者手帳資格管理_福祉世帯情報エリア
        # 4 福祉基本情報_身体障害者手帳資格管理_: 「福祉世帯情報」ボタン押下
        self.click_button_by_label("福祉世帯情報")

        # 4 福祉基本情報_身体障害者手帳資格管理_福祉世帯情報画面: 表示
        self.screen_shot("福祉基本情報_身体障害者手帳資格管理_福祉世帯情報_raa010202_02_3")

        # 4 福祉世帯情報画面: 1人目（本人）該当日「20230630」入力本人から見た続柄「本人」選択受給者との関係「本人」選択
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value=shinsei_ymd)

        # 28 福祉世帯情報画面: 1人目（本人）該当日「20230630」入力本人から見た続柄「本人」選択受給者との関係「本人」選択        
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value=shinsei_ymd)
        self.form_input_by_id(idstr="HoninCmb_2", text="母")
        self.form_input_by_id(idstr="JukyuCmb_2", text="保護者")

        # 29 福祉世帯情報画面: 2人目（父）該当日「20230630」入力本人から見た続柄「父」選択受給者との関係「保護者」選択
        self.form_input_by_id(idstr="GaitoYMDtxt_3", value=shinsei_ymd)
        self.form_input_by_id(idstr="HoninCmb_3", text="父")
        self.form_input_by_id(idstr="JukyuCmb_3", text="続柄なし")

        # 4 福祉基本情報_身体障害者手帳資格管理_福祉世帯情報画面: 「入力完了」ボタン押下
        self.click_by_id("Kakutei_BTN")
        
        # 2 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：「申請種別」「再交付」入力
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinsei_shubetsu)
        # 2 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：「申請理由」「紛失」入力
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinsei_riyuu)
        # 3 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：確認ボタン
        self.click_button_by_label("確定")
        self.save_screenshot_migrate(driver, "福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア_raa010202_02_2", True)
        # 4 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：申請年月日
        self.find_element(By.ID, "TxtShinseiYMD").clear()
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinsei_ymd)

        if self.exist_item(item_type="select", item_id="UketsukeBashoCmb"):
            self.form_input_by_id(idstr="UketsukeBashoCmb", text=uketsuke_basho)
        if self.exist_item(item_type="select", item_id="TantouBashoCmb"):
            self.form_input_by_id(idstr="TantouBashoCmb", text=tantou_basho)
                                
        # 4 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：管理場所
        if self.exist_item(item_type="select", item_id="TantoShokatsukuCmb"):
            self.form_input_by_id(idstr="TantoShokatsukuCmb", text=tanto_shokatsuku)

        # 4 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：職権
        self.form_input_by_id(idstr="ShokkenChkBox", value="1")

        # 4 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：受付番号
        if not is_auto_haiban:
            self.form_input_by_id(idstr="TxtUketsukeBangou", value=uketsuke_bangou)

        # 4 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：希望手帳様式
        self.form_input_by_id(idstr="KibouTechoYoushikiCmb", text="紙様式")

        # 4 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：手帳交付方法
        self.form_input_by_id(idstr="TechoKouhuHouhouCmb", text="窓口")

        # 4 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：手帳交付場所
        self.form_input_by_id(idstr="TechoKouhuBashoCmb", text=techo_kouhu_basho)

        # 4 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：自立支援医療の同時申請有無
        self.form_input_by_id(idstr="DoujiShinseiBox", value="1")
        
        # 4 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：事務担当者
        self.form_input_by_id(idstr="TxtJimuTantousha", value="福祉　太郎")

        # 4 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：申請時の主たる障害
        self.form_input_by_id(idstr="ShutaruShougaiCmb", text=shutaru_shougai)

        # 4 福祉基本情報_身体障害者手帳資格管理_[申請内容]エリア：再認定督促期限
        self.find_element(By.ID, "TxtSaininteiTokusokuYMD").clear()
        self.form_input_by_id(idstr="TxtSaininteiTokusokuYMD", value=sainintei_tokusoku_ymd)

        # 4 福祉基本情報_身体障害者手帳資格管理: 表示
        self.save_screenshot_migrate(driver, "福祉基本情報_身体障害者手帳資格管理_raa010202_02_4", True)  

        if self.exist_item(item_type="input", item_id="TxtIraiYMD"):
            # 4 福祉基本情報_身体障害者手帳資格管理_[福祉事務所間の送付状況]エリア：依頼日（転出元への依頼日）
            self.find_element(By.ID, "TxtIraiYMD").clear()
            self.form_input_by_id(idstr="TxtIraiYMD", value=irai_ymd)
            # 4 福祉基本情報_身体障害者手帳資格管理_[福祉事務所間の送付状況]エリア：報告日（転出元からの報告日）
            self.find_element(By.ID, "TxtHoukokuYMD").clear()
            self.form_input_by_id(idstr="TxtHoukokuYMD", value=houkoku_ymd)
            # 4 福祉基本情報_身体障害者手帳資格管理_[福祉事務所間の送付状況]エリア：回答日（転出先への回答日）
            self.find_element(By.ID, "TxtKaitouYMD").clear()
            self.form_input_by_id(idstr="TxtKaitouYMD", value=kaitou_ymd)

        # 4 福祉基本情報_身体障害者手帳資格管理_エリア：キャプチャー
        self.save_screenshot_migrate(driver, "福祉基本情報_身体障害者手帳資格管理_エリア_raa010202_02_5", True)        
        
        # 4 福祉基本情報_身体障害者手帳資格管理：登録ボタン
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.save_screenshot_migrate(driver, "福祉基本情報_身体障害者手帳資格管理_raa010202_02_6", True)