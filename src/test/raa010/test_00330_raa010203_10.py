import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01020310(FukushiSiteTestCaseBase):
    """TESTRAA01020310"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        super().setUp()
            
    def test_case_raa010203_10(self):
        """test_case_raa010203_10"""
        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA010")
        # 印刷
        self.click_button_by_label("印刷")
        self.save_screenshot_migrate(driver, "福祉基本情報_帳票印刷_raa010203_10-02", True)

        if not self.check_online_report_exist(insatsu_tyouhyou_name):
            return

        # 身体障害者手帳
        report_param_list = [
            {
                "report_name": insatsu_tyouhyou_name
            }
        ]
        self.print_online_reports(case_name="ケース名", report_param_list=report_param_list)
        self.save_screenshot_migrate(driver, "福祉基本情報_帳票印刷_raa010203_10-06", True)
        # 「ファイルを開く(O)」ボタンを押下 メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        self.save_screenshot_migrate(driver, "福祉基本情報_帳票印刷_raa010203_10-09", True)
