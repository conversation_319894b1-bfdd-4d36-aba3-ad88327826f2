
from base.fukushi_case import FukushiSiteTestCaseBase
from selenium.webdriver.common.by import By

class Test_QMDF051(FukushiSiteTestCaseBase):
    """Test_QMDF051"""      
    def test_case_001(self):
        """test_case_001"""           
        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.click_button_by_label("施設マスタメンテナンス")
        self.screen_shot("QMDF051_1",caption="QMDF051_画面初期化")            
        self.driver.find_element(By.ID, "TxtShisetsuCode").click()
        self.driver.find_element(By.ID, "TxtShisetsuCode").send_keys("000075")
        self.driver.find_element(By.ID, "span_CmdKensaku").click()
        self.screen_shot("QMDF051_2",caption="QMDF051_検索")              
        self.driver.find_element(By.ID, "span_CmdShoki").click()
        self.screen_shot("QMDF051_3",caption="QMDF051_初期表示")            
        self.driver.find_element(By.ID, "Shozaichi_2").click()
        self.driver.find_element(By.ID, "CmdKensaku").click()
        self.driver.find_element(By.ID, "CmbPage").click()
        dropdown = self.driver.find_element(By.ID, "CmbPage")
        dropdown.find_element(By.XPATH, "//option[. = '03']").click()
        self.driver.find_element(By.ID, "CmdJumpPage").click()
        self.screen_shot("QMDF051_4",caption="QMDF051_へ移動")                    
        self.driver.find_element(By.ID, "CmdNextPage").click()
        self.screen_shot("QMDF051_5",caption="QMDF051_次頁")          
        self.driver.find_element(By.ID, "CmdBackPage").click()
        self.screen_shot("QMDF051_6",caption="QMDF051_前頁")            
        self.driver.find_element(By.ID, "CmdSortCode").click()
        self.screen_shot("QMDF051_7",caption="QMDF051_降順")              
        self.driver.find_element(By.ID, "CmdSortCode").click()
        self.screen_shot("QMDF051_8",caption="QMDF051_昇順")          
        self.driver.find_element(By.ID, "CmdTsuika").click()
        self.screen_shot("QMDF051_9",caption="QMDF051_追加")            
        self.driver.find_element(By.ID, "ChkHaiban").click()
        self.driver.find_element(By.ID, "CmbShisetsuShurui").click()
        dropdown = self.driver.find_element(By.ID, "CmbShisetsuShurui")
        dropdown.find_element(By.XPATH, "//option[. = '母子施設']").click()
        self.driver.find_element(By.ID, "TxtKanjiMeisho").click()
        self.driver.find_element(By.ID, "TxtKanjiMeisho").send_keys("テスト名称")
        self.driver.find_element(By.ID, "TxtKanaMeisho").click()
        self.driver.find_element(By.ID, "TxtKanaMeisho").send_keys("ﾃｽﾄｶﾅ")
        self.driver.find_element(By.ID, "span_CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMDF052_1",caption="QMDF051_登録／復活ボタン押下_I01")             
        self.driver.find_element(By.ID, "span_CmdSakujo").click()
        assert self.driver.switch_to.alert.text == "削除します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMDF052_2",caption="QMDF051_削除ボタン押下_D01")         
        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "TxtShisetsuCode").click()
        self.driver.find_element(By.ID, "TxtShisetsuCode").send_keys("000075")
        self.driver.find_element(By.ID, "CmdKensaku").click()
        self.driver.find_element(By.ID, "Sel1").click()
        self.screen_shot("QMDF051_10",caption="QMDF051_一覧のNo1")           
        self.driver.find_element(By.ID, "CmbShisetsuShurui").click()
        dropdown = self.driver.find_element(By.ID, "CmbShisetsuShurui")
        dropdown.find_element(By.XPATH, "//option[. = '母子施設']").click()
        self.driver.find_element(By.ID, "span_CmdShoki").click()
        self.screen_shot("QMDF051_10",caption="QMDF051_初期表示")           
        self.driver.find_element(By.ID, "span_CmdKouzaJH").click()
        self.screen_shot("QMDF051_10",caption="QMDF051_口座情報")           
        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "CmbShisetsuShurui").click()
        dropdown = self.driver.find_element(By.ID, "CmbShisetsuShurui")
        dropdown.find_element(By.XPATH, "//option[. = '母子施設']").click()
        self.driver.find_element(By.ID, "span_CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMDF052_3",caption="QMDF051_登録／復活ボタン押下_U02")           
        self.driver.find_element(By.ID, "TxtKanjiMeisho").click()
        self.driver.find_element(By.ID, "TxtKanjiMeisho").send_keys("福祉事業施設テスト")
        self.driver.find_element(By.ID, "span_CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QMDF052_4",caption="QMDF051_登録／復活ボタン押下_U03")     