/*****************【QAE640】の実行前に関連テーブルのデータを削除してから実行する*****************/

/*【QAE640-001 実行前スクリプト】*/
/* 前回のテスト実行時に作成されたデータを削除します。*/
delete FROM WR$$JICHITAI_CODE$$QA..QAE資格履歴	    where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE01$$' 
delete FROM WR$$JICHITAI_CODE$$QA..QAE資格内容      where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE01$$' 
delete FROM WR$$JICHITAI_CODE$$QA..QAE指定医療機関  where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE01$$' 
delete FROM WR$$JICHITAI_CODE$$QA..QAE難病資格内容  where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE01$$' 
delete FROM WR$$JICHITAI_CODE$$QA..QAZ受給状況      where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE01$$' 
delete FROM WR$$JICHITAI_CODE$$QA..QAZ福祉世帯	    where 業務コード = 'QAE640' and 本人宛名コード = '$$QAE640_ATENACODE01$$' 

/*【QAE640-002 実行前スクリプト】*/
/* 前回のテスト実行時に作成されたデータを削除します。*/
delete FROM WR$$JICHITAI_CODE$$QA..QAE資格履歴	    where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE02$$' 
delete FROM WR$$JICHITAI_CODE$$QA..QAE資格内容      where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE02$$' 
delete FROM WR$$JICHITAI_CODE$$QA..QAE指定医療機関  where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE02$$'
delete FROM WR$$JICHITAI_CODE$$QA..QAE難病資格内容  where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE02$$' 
delete FROM WR$$JICHITAI_CODE$$QA..QAZ受給状況      where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE02$$'  
delete FROM WR$$JICHITAI_CODE$$QA..QAZ福祉世帯	    where 業務コード = 'QAE640' and 本人宛名コード = '$$QAE640_ATENACODE02$$' 

/*【QAE640-003 実行前スクリプト】*/
/* 前回のテスト実行時に作成されたデータを削除します。*/
delete FROM WR$$JICHITAI_CODE$$QA..QAE資格履歴	    where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE03$$' 
delete FROM WR$$JICHITAI_CODE$$QA..QAE資格内容      where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE03$$' 
delete FROM WR$$JICHITAI_CODE$$QA..QAE指定医療機関  where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE03$$'
delete FROM WR$$JICHITAI_CODE$$QA..QAE難病資格内容  where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE03$$' 
delete FROM WR$$JICHITAI_CODE$$QA..QAZ受給状況      where 業務コード = 'QAE640' and 宛名コード = '$$QAE640_ATENACODE03$$'   
delete FROM WR$$JICHITAI_CODE$$QA..QAZ福祉世帯	    where 業務コード = 'QAE640' and 本人宛名コード = '$$QAE640_ATENACODE03$$'  
