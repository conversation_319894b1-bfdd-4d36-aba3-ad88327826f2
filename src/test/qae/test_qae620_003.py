import time
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAE620(FukushiSiteTestCaseBase):
    """TESTQAE620003"""

    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAE620-001～003.sql", params=atena_list)
        super().setUp()

    def test_case_qae620_003(self):
        """test_case_qae620_003"""

        driver = None
        test_data = self.common_test_data
        #ログイン
        self.do_login()

        #資格管理ボタン押下
        self.find_element(By.ID, "CmdProcess1_1").click()
        self.save_screenshot_migrate(driver, "QAE620-003-3-2" , True)

        #資格管理画面・宛名コード入力後、検索ボタン押下
        self.find_element(By.ID, "AtenaCD").send_keys("")
        self.find_element(By.ID, "AtenaCD").send_keys(test_data.get("qae620_atena_code03_1",""))
        self.save_screenshot_migrate(driver, "QAE620-003-3-3" , True)
        self.find_element(By.ID, "span_Kensaku").click()
        self.save_screenshot_migrate(driver, "QAE620-003-3-5" , True)

        #難病（ホームヘルプ）ボタン押下
        self.find_element(By.ID, "span_02:0000000040:QAE620").click()
        self.save_screenshot_migrate(driver, "QAE620-003-3-7" , True)

        #申請内容入力ボタン押下
        self.find_element(By.ID, "span_CmdShinsei").click()

        #福祉世帯情報ボタン押下
        self.find_element(By.NAME, "img").click()
        self.find_element(By.ID, "btnCommon7").click()
        self.save_screenshot_migrate(driver, "QAE620-003-3-10" , True)

        #追加ボタン押下
        self.find_element(By.ID, "span_Tsuika_BTN").click()
        self.find_element(By.ID, "AtenaCD").send_keys(test_data.get("qae620_atena_code03_2",""))
        self.find_element(By.ID, "span_Kensaku").click()

        #福祉世帯情報入力
        self.find_element(By.ID, "GaitoYMDtxt_1").click()
        self.find_element(By.ID, "GaitoYMDtxt_1").send_keys("20230601")

        self.find_element(By.ID, "HoninCmb_2").send_keys("子")
        self.find_element(By.ID, "JukyuCmb_2").send_keys("扶養義務者")
        self.find_element(By.ID, "GaitoYMDtxt_2").click()
        self.find_element(By.ID, "GaitoYMDtxt_2").send_keys("20230601")

        self.find_element(By.ID, "HoninCmb_3").send_keys("父")
        self.find_element(By.ID, "JukyuCmb_3").send_keys("生計中心者")
        self.find_element(By.ID, "GaitoYMDtxt_3").send_keys("20230601")
        self.save_screenshot_migrate(driver, "QAE620-003-3-14" , True)

        #入力完了ボタン押下
        self.find_element(By.ID, "span_Kakutei_BTN").click()

        #申請種別を選択
        self.find_element(By.ID, "ShinseiShubetsuCmb").send_keys("新規")
        time.sleep(1)
        #申請理由を選択
        self.find_element(By.ID, "ShinseiRiyuuCmb").send_keys("死亡")

        #確定ボタン押下
        self.find_element(By.ID, "CmdKakutei").click()
        self.save_screenshot_migrate(driver, "QAE620-003-3-17" , True)

        #申請内容入力、登録ボタン押下
        self.find_element(By.ID, "TxtShinseiYMD").send_keys("20230701")
        self.find_element(By.ID, "TantoShokatsukuCmb").send_keys("第一区")

        #階層ボタン押下
        self.find_element(By.ID, "span_CmdKaiso1").click()
        self.save_screenshot_migrate(driver, "QAE620-003-3-19" , True)

        #登録ボタン押下
        self.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAE620-003-3-20" , True)

        #本庁進達入力ボタン押下
        self.find_element(By.ID, "span_CmdShintatsu1").click()
        self.save_screenshot_migrate(driver, "QAE620-003-3-21" , True)

        #進達年月日入力
        self.find_element(By.ID, "TxtShintatsuYMD").send_keys("20230701")

        #登録ボタン押下
        self.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAE620-003-3-23" , True)

        #本庁進達結果入力ボタン押下
        self.find_element(By.ID, "CmdShintatsu1Kekka").click()
        self.save_screenshot_migrate(driver, "QAE620-003-3-24" , True)

        #本庁進達結果入力
        self.find_element(By.ID, "ShintasuHanteiCmb").send_keys("決定")

        #登録ボタン押下
        self.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAE620-003-3-26" , True)

        #決定内容入力ボタン押下
        self.find_element(By.ID, "span_CmdKettei").click()
        self.save_screenshot_migrate(driver, "QAE620-003-3-27" , True)

        #決定内容入力
        self.find_element(By.ID, "TxtKetteiYMD").send_keys("20230801")
        self.find_element(By.ID, "KetteiKekkaCmb").send_keys("承認")
        #派遣期間
        self.find_element(By.ID, "TxtHakenKikanStart").send_keys("20230901")
        self.find_element(By.ID, "TxtHakenKikanEnd").send_keys("20231231")
        #停止期間
        self.find_element(By.ID, "TxtTeishiKikanStart").send_keys("20240101")
        self.save_screenshot_migrate(driver, "QAE620-003-3-30" , True)

        #登録ボタン押下
        self.find_element(By.ID, "span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAE620-003-3-31" , True)

        #削除ボタン押下
        self.find_element(By.ID, "span_CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAE620-003-3-32" , True)
