import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys

class TestRAB010(FukushiSiteTestCaseBase):
    """TESTRAB010003"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        case_data = self.test_data["test_case_RAB010_003"]
        atena_code = case_data.get("atena_code")
        params = {"DELETE_ATENA_CODE": atena_code}
        self.exec_sqlfile("RAB010.sql", params=params)
        super().setUp()

    # @unittest.skip('skipped')  # テストをスキップする 
    def test_case_rab010_003(self):
        """test_case_rab010_003"""

        #セクションを取得
        case_data = self.test_data["test_case_RAB010_003"]
        
        #ログイン→受給状況までを一括で実施
        self.goto_jukyu_joukyou(case_data.get("atena_code",""))
        self.jukyu_joukyou_contents_expand()  # ヘッドレスモード
        self.screen_shot("RAB010-003-3-5",caption="RAB010-003-3-5")

        #個人情報画面・補装具支給の資格管理に遷移
        self.click_jukyujoukyou_by_gyoumu_code("QAB010")
        self.screen_shot("RAB010-003-3-7", caption="RAB010-003-3-7")
        
        #資格管理画面・申請内容入力ボタン押下
        self.click_by_id("CmdShinsei")
        self.screen_shot("RAB010-003-3-8", caption="RAB010-003-3-8")

        #資格管理画面・口座情報ボタン押下
        self.common_button_click("口座情報")
        self.assertIn("QAZF038", self.driver.current_url)

        #口座情報画面・追加ボタン押下
        self.click_by_id("口座情報_u_Tuika")

        #口座情報画面・口座情報入力/登録
        self.click_by_id("口座情報_u_KinyuKikanKensaku_span")
        self.form_input_by_id("TxtGinkoCode", value="1")
        self.click_by_id("span_CmdKensaku")
        self.click_by_id("CmdShori1")
        self.form_input_by_id("口座情報_u_KouzaBango", value="1231231")
        self.driver.find_element(By.ID, "口座情報_u_Touroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "登録します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        self.assert_message_area("登録しました。")
        self.return_click()

        #資格管理画面・保険情報ボタン押下
        self.common_button_click("保険情報")
        self.assertIn("QAZF016", self.driver.current_url)

        #保険情報一覧画面・追加ボタン押下
        self.click_by_id("CmdTsuika")

        #保険情報登録画面・保険情報入力/登録
        self.click_by_id("CmdHokenSerch")
        self.form_input_by_id("txtKanaMeisho", value="フクシ")
        self.click_by_id("BtnKensaku")
        self.click_by_id("Sel1")
        self.click_by_id("CmdHiHokenSerch")
        self.click_by_id("Sel1")
        self.select_Option(self.driver,self.find_element(By.ID,"FuyoshaCmb"),"本人")
        self.form_input_by_id("TxtKigo", value="１２３")
        self.form_input_by_id("TxtBango", value="１２３４")
        self.form_input_by_id("TxtShutokuYMD", value="20210101")
        self.driver.find_element(By.ID, "span_CmdHokenToroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        self.assert_message_area("登録しました。")
        self.return_click()

        #資格管理画面・福祉世帯情報ボタン押下
        self.common_button_click("福祉世帯情報")
        self.assertIn("QAZF027", self.driver.current_url)

        #福祉世帯情報・福祉世帯情報入力/登録
        self.find_element_by_id("GaitoYMDtxt_1").clear()
        self.form_input_by_id("GaitoYMDtxt_1", value="20210701")
        self.click_by_id("Kakutei_BTN")

        #資格管理画面・申請種別入力/確定
        self.click_by_id("ShinseiShubetsuCmb")
        self.select_Option(self.driver,self.find_element(By.ID,"ShinseiShubetsuCmb"),"購入(判定あり)")
        self.click_by_id("CmdKakutei")
        self.screen_shot("RAB010-003-3-13", caption="RAB010-003-3-13")

        #資格管理画面・申請内容入力
        self.form_input_by_id("TxtShinseiYMD", value="20210701")
        self.form_input_by_id("TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id("UketsukeBashoCmb", text="第一区")
        self.form_input_by_id("TantouBashoCmb", text="第一区")
        self.form_input_by_id("KariukeIkouChkBox")

        #資格管理画面・障害内容入力
        self.send_keys_by_name("TxtKiouReki", "障害内容１")

        #資格管理画面・現在受療中の医療機関番号ボタン押下
        self.click_by_id("CmdGenzaiIryoKikan")
        self.assertIn("QAZF060", self.driver.current_url)

        #医療機関検索画面・医療機関検索
        self.send_keys_by_name("TxtKanaMeisho", "テスト")
        self.click_by_id("BtnKensaku")
        self.click_by_id("Sel1")
        
        #資格管理画面・障害内容入力
        self.send_keys_by_name("TxtChoukiKyufuNaiyou", "障害内容２")
        self.send_keys_by_name("TxtShokugyouReki", "障害内容３")

        #資格管理画面・補装具内容入力
        self.select_Option(self.driver,self.find_element(By.ID,"YoguShuCmb"),"義手")
        #（仮）結合環境用
        #self.select_Option(self.driver,self.find_element(By.ID,"HosoguCmb"),"義足")
        #self.select_Option(self.driver,self.find_element(By.ID,"HosoguCmb"),"義手　標準化DB")
        self.select_Option(self.driver,self.find_element(By.ID,"HosoguCmb"),"殻構造義肢　上腕義手　装飾用")
        self.find_element_by_id("TxtTaiyoNensu_Nen").clear()
        self.send_keys_by_name("TxtTaiyoNensu_Nen", "5")
        self.find_element_by_id("TxtTaiyoNensu_Tsuki").clear()
        self.send_keys_by_name("TxtTaiyoNensu_Tsuki", "6")

        #資格管理画面・業者名ボタン押下
        self.click_by_id("CmdGyoshamei")
        self.assertIn("QAZF059", self.driver.current_url)
        self.screen_shot("RAB010-003-3-17", caption="RAB010-003-3-17")

        #業者検索画面・業者検索
        self.click_by_id("KekkaButton1")
        self.screen_shot("RAB010-003-3-19", caption="RAB010-003-3-19")

        #資格管理画面・支給内容入力
        self.find_element_by_id("TxtKyufuTaishoYM").clear()
        self.form_input_by_id("TxtKyufuTaishoYM", value="202107")
        self.find_element_by_id("TxtMitsumoriKakaku").clear()
        self.form_input_by_id("TxtMitsumoriKakaku", value="100000")
        self.find_element_by_id("TxtKetteiKakaku").clear()
        self.form_input_by_id("TxtKetteiKakaku", value="100000")

        #資格管理画面・所得区分ボタン押下
        self.click_by_id("KaisoBtn")
        self.screen_shot("RAB010-003-3-21", caption="RAB010-003-3-21")

        #資格管理画面・入力所得区分で再計算ボタン押下
        self.select_Option(self.driver,self.find_element(By.ID,"KaisoCmb"),"一般")
        self.click_by_id("CmdSaikeisan")
        self.screen_shot("RAB010-003-3-23", caption="RAB010-003-3-23")

        #資格管理画面・自己負担額入力
        self.find_element_by_id("TxtJikoFutangaku").clear()
        self.form_input_by_id("TxtJikoFutangaku", value="7000")

        #資格管理画面・利用者負担額入力
        self.find_element_by_id("TxtRiyousyaFutangaku").clear()
        self.form_input_by_id("TxtRiyousyaFutangaku", value="7000")

        #資格管理画面・用具上限額入力
        self.form_input_by_id("TxtYouguJyougengaku", value="100000")

        #資格管理画面・所得内容入力
        self.select_Option(self.driver,self.find_element(By.ID,"ShotokuHanteiNendoCmb"),"令和03年")
        self.find_element_by_id("TxtKintouwariGaku").clear()
        self.form_input_by_id("TxtKintouwariGaku", value="3000")
        self.find_element_by_id("TxtShotokuwariGaku").clear()
        self.form_input_by_id("TxtShotokuwariGaku", value="10000")
        self.find_element_by_id("TxtSyunyugakuGoukei").clear()
        self.form_input_by_id("TxtSyunyugakuGoukei", value="1000000")
        self.form_input_by_id("SeikatsuHogoUmuChkBox")
        self.form_input_by_id("GenmenTaishouChkBox")

        #資格管理画面・支給券データ作成ボタン押下
        self.click_by_id("CmdKoufukenSakusei")
        self.screen_shot("RAB010-003-3-25", caption="RAB010-003-3-25")

        #（仮）利用者負担額チェックによる登録エラー
        # self.find_element_by_id("TxtRiyousyaFutangaku").clear()
        # self.form_input_by_id("TxtRiyousyaFutangaku", value="0")

        #（仮）支給券データ反映されない
        #self.find_element_by_id("Kofuken_KofuTaishoYM1").clear()
        #self.form_input_by_id("Kofuken_KofuTaishoYM1", value="202107")
        #self.find_element_by_id("Kofuken_Jikofutangaku1").clear()
        #self.form_input_by_id("Kofuken_Jikofutangaku1", value="7000")
        #self.find_element_by_id("Kofuken_Kohifutangaku1").clear()
        #self.form_input_by_id("Kofuken_Kohifutangaku1", value="90000")
        #self.find_element_by_id("Kofuken_Riyousyafutan1").clear()
        #self.form_input_by_id("Kofuken_Riyousyafutan1", value="7000")
        #self.find_element_by_id("Kofuken_JikofutanJosei1").clear()
        #self.form_input_by_id("Kofuken_JikofutanJosei1", value="2000")
        #self.find_element_by_id("Kofuken_ChoukaJikofutan1").clear()
        #self.form_input_by_id("Kofuken_ChoukaJikofutan1", value="0")

        #資格管理画面・登録ボタン押下
        self.driver.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        self.assert_message_area("登録しました。")
        self.screen_shot("RAB010-003-3-26", caption="RAB010-003-3-26")

        #資格管理画面・判定入力ボタン押下
        self.driver.find_element(By.ID, "CmdShintatsu1").click()
        self.screen_shot("RAB010-003-3-27", caption="RAB010-003-3-27")

        #資格管理画面・判定内容入力/登録
        self.find_element_by_id("TxtShintatsuYMD").clear()
        self.form_input_by_id("TxtShintatsuYMD", value="20210701")
        self.driver.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        self.assert_message_area("登録しました。")
        self.screen_shot("RAB010-003-3-29", caption="RAB010-003-3-29")

        #資格管理画面・判定結果入力ボタン押下
        self.driver.find_element(By.ID, "CmdShintatsu1Kekka").click()
        self.screen_shot("RAB010-003-3-30", caption="RAB010-003-3-30")

        #資格管理画面・判定結果入力/登録
        self.find_element_by_id("TxtShintatsuHanteiYMD").clear()
        self.form_input_by_id("TxtShintatsuHanteiYMD", value="20210714")
        self.select_Option(self.driver,self.find_element(By.ID,"ShintasuHanteiCmb"),"決定（購入）")
        self.form_input_by_id("TxtHanteiYoteiYMD", value="20210720")
        self.form_input_by_id("TxtHanteiJikan_Ji", value="10")
        self.form_input_by_id("TxtHanteiJikan_Fun", value="30")
        self.select_Option(self.driver,self.find_element(By.ID,"HanteiHouhouCmb"),"来所")
        self.select_Option(self.driver,self.find_element(By.ID,"HanteiKikanCmb"),"判定機関略１")
        self.select_Option(self.driver,self.find_element(By.ID,"HanteiKaijouCmb"),"判定会場１")
        self.form_input_by_id("TxtHanteiUketsukeYMD", value="20210730")
        self.form_input_by_id("TxtHanteiRiyu", value="あいう")
        self.form_input_by_id("TxtHanteiShokuinKanjiSei", value="アイネス")
        self.form_input_by_id("TxtHanteiShokuinKanjiMei", value="太郎")
        self.driver.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        self.assert_message_area("登録しました。")
        self.screen_shot("RAB010-003-3-32", caption="RAB010-003-3-32")

        #資格管理画面・決定結果入力ボタン押下
        self.driver.find_element(By.ID, "CmdKettei").click()
        self.screen_shot("RAB010-003-3-33", caption="RAB010-003-3-33")

        #資格管理画面・決定結果入力/登録
        self.find_element_by_id("TxtKetteiYMD").clear()
        self.form_input_by_id("TxtKetteiYMD", value="20210714")
        self.select_Option(self.driver,self.find_element(By.ID,"KetteiKekkaCmb"),"決定")
        self.driver.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "支給券番号を配番してよろしいですか？")
        self.driver.switch_to.alert.accept()
        self.assert_message_area("登録しました。")
        self.screen_shot("RAB010-003-3-35", caption="RAB010-003-3-35")
        
        #資格管理画面・申請内容入力ボタン押下
        self.click_by_id("CmdShinsei")
        self.screen_shot("RAB010-003-3-36", caption="RAB010-003-3-36")

        #資格管理画面・申請種別入力/確定
        self.click_by_id("ShinseiShubetsuCmb")
        self.select_Option(self.driver,self.find_element(By.ID,"ShinseiShubetsuCmb"),"購入(判定あり)")
        self.click_by_id("CmdKakutei")
        self.screen_shot("RAB010-003-3-38", caption="RAB010-003-3-38")

        #資格管理画面・申請内容入力
        self.form_input_by_id("TxtShinseiYMD", value="20210720")
        #self.find_element_by_id("TxtShinseiNo").clear()

        #資格管理画面・入力所得区分で再計算ボタン押下
        self.click_by_id("CmdSaikeisan")
        self.screen_shot("RAB010-003-3-45", caption="RAB010-003-3-45")

        #資格管理画面・自己負担額入力
        self.find_element_by_id("TxtJikoFutangaku").clear()
        self.form_input_by_id("TxtJikoFutangaku", value="5000")

        #資格管理画面・利用者負担額入力
        self.find_element_by_id("TxtRiyousyaFutangaku").clear()
        self.form_input_by_id("TxtRiyousyaFutangaku", value="5000")

        #資格管理画面・支給券データ作成ボタン押下
        self.click_by_id("CmdKoufukenSakusei")
        self.screen_shot("RAB010-003-3-40", caption="RAB010-003-3-40")

        #資格管理画面・登録ボタン押下
        self.driver.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        self.assert_message_area("登録しました。")
        self.screen_shot("RAB010-003-3-41", caption="RAB010-003-3-41")
        
        #資格管理画面・判定入力ボタン押下
        self.driver.find_element(By.ID, "CmdShintatsu1").click()
        self.screen_shot("RAB010-003-3-42", caption="RAB010-003-3-42")

        #資格管理画面・判定内容入力/登録
        self.find_element_by_id("TxtShintatsuYMD").clear()
        self.form_input_by_id("TxtShintatsuYMD", value="20210721")
        self.driver.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        self.assert_message_area("登録しました。")
        self.screen_shot("RAB010-003-3-44", caption="RAB010-003-3-44")

        #資格管理画面・判定結果入力ボタン押下
        self.driver.find_element(By.ID, "CmdShintatsu1Kekka").click()
        self.screen_shot("RAB010-003-3-45", caption="RAB010-003-3-45")

        #資格管理画面・判定結果入力/登録
        self.find_element_by_id("TxtShintatsuHanteiYMD").clear()
        self.form_input_by_id("TxtShintatsuHanteiYMD", value="20210722")
        self.select_Option(self.driver,self.find_element(By.ID,"ShintasuHanteiCmb"),"決定（購入）")
        self.driver.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        self.assert_message_area("登録しました。")
        self.screen_shot("RAB010-003-3-47", caption="RAB010-003-3-47")

        #資格管理画面・修正ボタン押下
        self.click_by_id("CmdShuusei")
        self.screen_shot("RAB010-003-3-48", caption="RAB010-003-3-48")

        #資格管理画面・判定内容取り下げ
        self.select_Option(self.driver,self.find_element(By.ID,"ShintasuHanteiCmb"),"差し戻し")
        self.driver.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        self.assert_message_area("登録しました。")
        self.screen_shot("RAB010-003-3-50", caption="RAB010-003-3-50")

        #資格管理画面・却下入力ボタン押下
        self.click_by_id("CmdKyakka")
        self.screen_shot("RAB010-003-3-51", caption="RAB010-003-3-51")
        self.find_element_by_id("TxtKetteiYMD").clear()
        self.form_input_by_id("TxtKetteiYMD", value="20210723")
        self.select_Option(self.driver,self.find_element(By.ID,"KetteiKekkaCmb"),"却下")
        self.driver.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        self.assert_message_area("登録しました。")
        self.screen_shot("RAB010-003-3-53", caption="RAB010-003-3-53")
        self.return_click()
        self.screen_shot("RAB010-002-2-54", caption="RAB010-002-2-54")
        self.return_click()
        self.screen_shot("RAB010-002-2-55", caption="RAB010-002-2-55")
