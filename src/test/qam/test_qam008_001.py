import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TEST_QAM008_001(FukushiSiteTestCaseBase):
    """TEST_QAM008_001"""
    
    def test_case_001(self):
        """test_case_001"""
        driver = None
        test_data = self.common_test_data
        
        self.do_login()
        self.click_button_by_label("民生委員管理")
        self.save_screenshot_migrate(driver, "QAM008-001-02", True)
        self.click_button_by_label("民生委員検索")
        self.save_screenshot_migrate(driver, "QAM008-001-04", True)
        self.find_element(By.ID,"TxtAtenaCode").click()
        self.find_element(By.ID,"TxtAtenaCode").send_keys("")
        self.find_element(By.ID,"TxtAtenaCode").send_keys(test_data.get("qam008_atena_code"))
        self.save_screenshot_migrate(driver, "QAM008-001-05", True)
        self.find_element(By.ID,"CmdKensaku").click()
        self.save_screenshot_migrate(driver, "QAM008-001-07", True)
        
        self.find_element(By.ID,"CmdDelete").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAM008-001-09", True)

        self.find_element(By.ID,"span_CmdIshokuInput").click()
        self.find_element(By.ID,"TxtSetaiinsu").click()
        self.find_element(By.ID,"TxtSetaiinsu").send_keys("3")
        self.find_element(By.ID,"CmbShokugyo").send_keys("その他")
        self.find_element(By.ID,"CmbShubetsu").send_keys("民生委員・児童委員")
        self.find_element(By.ID,"CmbSainin").send_keys("新任")
        self.find_element(By.ID,"TxtIshokuRirekiYoteiYMD").click()
        self.find_element(By.ID,"TxtIshokuRirekiYoteiYMD").send_keys("")
        self.find_element(By.ID,"TxtIshokuRirekiYoteiYMD").send_keys(u"令和03年09月01日")
        self.find_element(By.ID,"span_CmdSubmit").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAM008-001-10", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAM008-001-11", True)
