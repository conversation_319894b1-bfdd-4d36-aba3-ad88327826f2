import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select


class Test_QAMF301(FukushiSiteTestCaseBase):
    """Test_QAMF301"""

      # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        # self.exec_sqlfile("QAZF066_実行前スクリプト.sql", params=atena_list)
        super().setUp()

    def test_case_001(self):
        """test_case_001"""
        driver = None
        test_data = self.common_test_data
        
        self.do_login()
        self.click_button_by_label("民生委員管理")
        self.save_screenshot_migrate(driver, "QAMF301-001-02", True)

        self.click_button_by_label("支給額データ管理")
        self.save_screenshot_migrate(driver, "QAMF301-001-04", True)

        self.driver.find_element(By.ID, "span_CmdKensaku").click()
        self.save_screenshot_migrate(driver, "QAMF301-001-06", True)

        self.driver.find_element(By.ID, "span_CmdKojin").click()
        self.driver.find_element(By.ID, "AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qamf301_atena_code"))
        self.driver.find_element(By.ID, "span_Kensaku").click()
        self.save_screenshot_migrate(driver, "QAMF301-001-09", True)

        self.driver.find_element(By.ID, "CmdTsugi").click()
        self.save_screenshot_migrate(driver, "QAMF301-001-11", True)

        self.driver.find_element(By.ID, "CmdMae").click()
        self.save_screenshot_migrate(driver, "QAMF301-001-13", True)

        self.driver.find_element(By.ID, "PageCmb").click()
        dropdown = self.driver.find_element(By.ID, "PageCmb")
        dropdown.find_element(By.XPATH, "//option[. = '03']").click()
        self.driver.find_element(By.ID, "CmdIdobtn").click()
        self.save_screenshot_migrate(driver, "QAMF301-001-16", True)

        self.driver.find_element(By.ID, "span_CmdShoki").click()
        self.save_screenshot_migrate(driver, "QAMF301-001-18", True)

        self.driver.find_element(By.ID, "span_CmdKensaku").click()
        self.driver.find_element(By.ID, "NoBtn1").click()
        self.save_screenshot_migrate(driver, "QAMF301-001-21", True)
        self.driver.find_element(By.ID, "GOBACK").click()

        self.driver.find_element(By.ID, "CmdTsuika").click()
        self.save_screenshot_migrate(driver, "QAMF301-001-24", True)
        self.driver.find_element(By.ID, "GOBACK").click()
        
        self.driver.find_element(By.ID, "span_CmdKojin").click()
        self.save_screenshot_migrate(driver, "QAMF301-001-28", True)
        self.driver.find_element(By.ID, "AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qamf301_atena_code"))
        self.driver.find_element(By.ID, "span_Kensaku").click()
        self.driver.find_element(By.ID, "span_CmdKensaku").click()
        self.save_screenshot_migrate(driver, "QAMF301-001-31", True)