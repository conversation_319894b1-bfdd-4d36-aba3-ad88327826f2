import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select


class Test_QAMF304(FukushiSiteTestCaseBase):
    """Test_QAMF304"""
    
    def test_case_001(self):
        """test_case_001"""
        self.do_login()
        self.click_button_by_label("民生委員管理")
        self.click_button_by_label("控除額振込データ管理")
        self.driver.find_element(By.ID, "CmdKensaku").click()
        self.driver.find_element(By.ID, "NoBtn1").click()
        self.screen_shot("QAMF304",caption="控除額振込データ照会_初期表示")