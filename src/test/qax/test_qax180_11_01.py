import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
from base.kyufu_case import KyufuSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select


class TestQAX1801101(FukushiSiteTestCaseBase, KyufuSiteTestCaseBase):
    """TestQAX180_11_01"""


    def test_case_QAX180_11_01(self):
        """test_case_QAX180_11_01"""

        driver = None

        case_data = self.test_data["case11_01"]
        kabaraihassei_date_start = case_data.get("kabaraihassei_date_start")
        kabaraihassei_date_end = case_data.get("kabaraihassei_date_end")
        saishunyukin_date_start = case_data.get("saishunyukin_date_start")
        saishunyukin_date_end = case_data.get("saishunyukin_date_end")
        kabaraijiyu = case_data.get("kabaraijiyu")
        kannouflg = case_data.get("kannouflg")
        
        # ログイン
        self.do_login()

        # メインメニュー画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-11-01-1" , True)

        # 「バッチ検索」ボタン押下
        self.find_element(By.XPATH, "//button[normalize-space()='バッチ検索']").click()

        # バッチ検索画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-11-01-3" , True)
        
        # 「ジョブID」入力
        self.find_element(By.ID, "TxtJobID").send_keys("QAXJX062")
        
        # 「検索」ボタン押下
        self.find_element(By.ID, "CmdKensaku").click()
        
        # バッチ起動画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-11-01-6" , True)
        
        # 「過払発生日＜はじめ＞」入力
        self.find_element(By.ID, "item3").send_keys(kabaraihassei_date_start)
        # 「過払発生日＜おわり＞」入力
        self.find_element(By.ID, "item4").send_keys(kabaraihassei_date_end)
        # 「最終入金日＜はじめ＞」入力
        self.find_element(By.ID, "item5").send_keys(saishunyukin_date_start)
        # 「最終入金日＜おわり＞」入力
        self.find_element(By.ID, "item6").send_keys(saishunyukin_date_end)
        # 「過払事由」入力
        self.form_input_by_id("item7", text=kabaraijiyu)
        # 「完納フラグ」入力(先に初期値を消す)
        self.find_element(By.ID, "item8").send_keys(Keys.CONTROL + "a")
        self.find_element(By.ID, "item8").send_keys(Keys.BACK_SPACE)
        self.find_element(By.ID, "item8").send_keys(kannouflg)

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")

        # バッチ起動画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-11-01-9" , True)

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(120,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # ジョブ実行履歴から「正常終了」のジョブ番号の取得(PDFダウンロードで使用する)
        exec_job_ID = self.get_job_execute_id("QAXJX062")

        # ジョブ実行履歴画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-11-01-12" , True)

        # 「帳票履歴」ボタン押下
        self.click_report_log()

        # ジョブ帳票履歴画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-11-01-14" , True)
        
        # 帳票履歴画面の「検索」ボタン押下
        self.find_element(By.ID, "SearchButton").click()

        # ジョブ帳票履歴画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-11-01-16" , True)

        # 今回の処理で作成したPDFのDL
        self.do_report_download_edge(exec_job_ID)

        # ジョブ帳票履歴画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-11-01-21" , True)
