import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
from base.kyufu_case import KyufuSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select


class TestQAX1800302(FukushiSiteTestCaseBase, KyufuSiteTestCaseBase):
    """TestQAX180_03_02"""


    def test_case_QAX180_03_02(self):
        """test_case_QAX180_03_02"""

        driver = None

        # 宛名コード取得
        common_test_data = self.common_test_data
        QAX180_atena_code3 = common_test_data.get("case_common_QAX180_atena_code3")

        # select文を実行して宛名コードから申請書番号を取得
        _db = self.get_db() 

        sql_text = """
        SELECT 申請書番号
        FROM WR$$JICHITAI_CODE$$QA.dbo.QAX180臨時福祉給付金世帯 
        WHERE 申請受給者住民コード = '$$ATENA_CODE$$' and 世代 = '2'
        """
        params = {"ATENA_CODE": QAX180_atena_code3}
        ret = _db.exec_sql_result(sql_text, params=params)

        # [(Decimal('**********'),)] のように配列として値が入ってくるので0番目だけを取り出す
        for rec in ret:
            shinsei_no = str(rec[0])
            break

        # パラメータ設定
        case_data = self.test_data["case03_02"]
        shinsei_date = case_data.get("shinsei_date")
        bank_code = case_data.get("bank_code")
        branch_code = case_data.get("branch_code")
        account_no = case_data.get("account_no")
        account_meiginin_kanji = case_data.get("account_meiginin_kanji")
        dairi_name = case_data.get("dairi_name")
        phone_no = case_data.get("phone_no")
        memo = case_data.get("memo")
        
        # ログイン
        self.do_login()

        # 「均等割のみ課税世帯給付金」ボタン押下
        self.find_element(By.XPATH, "//button[contains(@title,'均等割のみ課税')]").click()

        # サブメニュー画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-03-02-2" , True)
        
        # 「申請書一括消込」ボタン押下
        self.find_element(By.ID, "CmdButton1").click()

        # 申請書発行画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-03-02-4" , True)

        # 「申請日」入力(先に初期値を消す)
        self.find_element(By.ID, "申請日_初期設定").send_keys(Keys.CONTROL + "a")
        self.find_element(By.ID, "申請日_初期設定").send_keys(Keys.BACK_SPACE)
        self.find_element(By.ID, "申請日_初期設定").send_keys(shinsei_date)

        # 「申請書番号」入力
        self.find_element(By.ID, "TxtBarCode").send_keys(shinsei_no)

        # 「検索」ボタン押下
        # (自動で「検索」ボタンを押下できないので代わりにエンターキー押下)
        self.find_element(By.ID, "TxtBarCode").send_keys(Keys.ENTER)

        # 申請書発行画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-03-02-7" , True)

        # 「受取方法」　Aを選択"
        self.find_element(By.ID, "申請書消込_受取方法1").click()

        # 「金融機関選択」ボタン押下
        self.find_element(By.ID, "申請書消込_金融機関検索ボタン").click()

        # 「銀行コード」入力
        self.find_element(By.ID, "TxtGinkoCode").send_keys(bank_code)
        # 「支店コード」入力
        self.find_element(By.ID, "TxtShitenCode").send_keys(branch_code)

        #「検索」ボタン押下
        self.find_element(By.ID, "CmdKensaku").click()

        # 金融機関一覧の№ボタンを押下(No.1のボタンを押下)
        self.find_element(By.ID, "CmdShori1").click()

        # 「口座番号」入力
        self.find_element(By.ID, "申請書消込_口座番号").send_keys(account_no)
        # 「口座名義人漢字」入力(先に初期値を消す)
        self.find_element(By.ID, "申請書消込_口座名義人漢字").send_keys(Keys.CONTROL + "a")
        self.find_element(By.ID, "申請書消込_口座名義人漢字").send_keys(Keys.BACK_SPACE)
        self.find_element(By.ID, "申請書消込_口座名義人漢字").send_keys(account_meiginin_kanji)
        # 「代理申請者」入力
        self.find_element(By.ID, "申請書消込_代理申請者").send_keys(dairi_name)
        # 「電話番号」入力
        self.find_element(By.ID, "申請書消込_電話番号").send_keys(phone_no)
        # 「メモ」入力
        self.find_element(By.ID, "申請書消込_メモ").send_keys(memo)

        # 申請書発行画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-03-02-14" , True)

        #「登録」ボタン押下
        self.find_element(By.ID, "申請書消込_登録ボタン").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())

        # 申請書発行画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-03-02-16" , True)

        # ステータスが「申請消込済」で表示されることを確認
        self.assert_shinsei_status("申請消込済")
