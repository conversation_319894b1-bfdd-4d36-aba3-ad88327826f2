import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
from base.kyufu_case import KyufuSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select


class TestQAX1801301(FukushiSiteTestCaseBase, KyufuSiteTestCaseBase):
    """TestQAX180_13_01"""


    def test_case_QAX180_13_01(self):
        """test_case_QAX180_13_01"""

        driver = None

        # 宛名コード取得
        common_test_data = self.common_test_data
        QAX180_atena_code2 = common_test_data.get("case_common_QAX180_atena_code2")

        # select文を実行して宛名コードから申請書番号を取得
        _db = self.get_db() 

        sql_text = """
        SELECT 申請書番号
        FROM WR$$JICHITAI_CODE$$QA.dbo.QAX180臨時福祉給付金世帯 
        WHERE 申請受給者住民コード = '$$ATENA_CODE$$' and 世代 = '2'
        """
        params = {"ATENA_CODE": QAX180_atena_code2}
        ret = _db.exec_sql_result(sql_text, params=params)

        # [(Decimal('9000000446'),)] のように配列として値が入ってくるので0番目だけを取り出す
        for rec in ret:
            shinsei_no = str(rec[0])
            break

        # パラメータ設定
        case_data = self.test_data["case13_01"]
        shiharai_date = case_data.get("shiharai_date")
        
        # ログイン
        self.do_login()

        # メインメニュー画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-13-01-1" , True)

        # 「バッチ検索」ボタン押下
        self.find_element(By.XPATH, "//button[normalize-space()='バッチ検索']").click()

        # バッチ検索画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-13-01-3" , True)
        
        # 「ジョブID」入力
        self.find_element(By.ID, "TxtJobID").send_keys("QAXJX053")
        
        # 「検索」ボタン押下
        self.find_element(By.ID, "CmdKensaku").click()
        
        # バッチ起動画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-13-01-6" , True)
        
        # 「支払日」入力
        self.find_element(By.ID, "item2").send_keys(shiharai_date)

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")

        # バッチ起動画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-13-01-9" , True)

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(120,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # ジョブ実行履歴画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-13-01-12" , True)

        # ジョブ帳票履歴画面
        # 「戻る」ボタン押下
        self.return_click()

        # バッチ検索画面
        # 「戻る」ボタン押下
        self.return_click()

        # メインメニュー画面 
        # 「均等割のみ課税世帯給付金」ボタン押下
        self.find_element(By.XPATH, "//button[contains(@title,'均等割のみ課税')]").click()

        # サブメニュー表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-13-01-16" , True)

        # 「申請台帳参照」ボタンを押下し、申請書発行画面で「申請書番号」を検索
        self.search_ShinseiNo("QAX180-13-01-18", shinsei_no, "QAX180-13-01-21")

        # ステータス表示が「支給決定済（支給）」であることを確認
        self.assert_shinsei_status("支給決定済（支給）")
