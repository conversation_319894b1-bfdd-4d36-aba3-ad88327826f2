import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
from base.kyufu_case import KyufuSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select


class TestQAX1800502(FukushiSiteTestCaseBase, KyufuSiteTestCaseBase):
    """TestQAX180_05_02"""


    def test_case_QAX180_05_02(self):
        """test_case_QAX180_05_02"""

        driver = None

        # ログイン
        self.do_login()

        # メインメニュー画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-05-02-1" , True)

        # 「バッチ検索」ボタン押下
        self.find_element(By.XPATH, "//button[normalize-space()='バッチ検索']").click()

        # バッチ検索画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-05-02-3" , True)
        
        # 「ジョブID」入力
        self.find_element(By.ID, "TxtJobID").send_keys("QAXJX042")
        
        # 「検索」ボタン押下
        self.find_element(By.ID, "CmdKensaku").click()
        
        # バッチ起動画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-05-02-6" , True)
        
        # パラメータはなし

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")

        # バッチ起動画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-05-02-8" , True)

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(120,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # ジョブ実行履歴から「正常終了」のジョブ番号の取得(PDFダウンロードで使用する)
        exec_job_ID = self.get_job_execute_id("QAXJX042")

        # ジョブ実行履歴画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-05-02-11" , True)

        # 「帳票履歴」ボタン押下
        self.click_report_log()

        # ジョブ帳票履歴画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-05-02-13" , True)
        
        # 帳票履歴画面の「検索」ボタン押下
        self.find_element(By.ID, "SearchButton").click()

        # ジョブ実行履歴画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-05-02-15" , True)

        # 今回の処理で作成したPDFのDL
        self.do_report_download_edge(exec_job_ID)

        # ジョブ実行履歴画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX180-05-02-20" , True)
