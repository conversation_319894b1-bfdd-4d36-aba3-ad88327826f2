import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAR010058(FukushiSiteTestCaseBase):
    """TestQAR010058"""

    def test_case_qar010_058(self):
        """test_case_qar010_058"""
        driver = None
        test_data = self.common_test_data
        self.do_login()

        self.click_button_by_label("収納管理")
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmbGyomu")))
        #self.find_element(By.ID,"CmbGyomu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbGyomu"),"高齢")
        self.find_element(By.ID,"CmbGyomu").send_keys("高齢")
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmbJigyo")))
        #self.find_element(By.ID,"CmbJigyo").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbJigyo"),"養護老人ホーム入所")
        self.find_element(By.ID,"CmbJigyo").send_keys("養護老人ホーム入所")
        self.find_element(By.ID,"CmdKakutei").click()
        # self.find_element(By.ID,"CmdButton9").click()
        # 文書返却入力
        self.find_element(By.ID,"span_CmdButton11").click()
        self.save_screenshot_migrate(driver, "QAR010-058-02", True)
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "BunsyoKubunCmb")))
        self.find_element(By.ID,"BunsyoKubunCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"BunsyoKubunCmb"),"オンライン納付書")
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "NendoCmb")))
        self.find_element(By.ID,"NendoCmb").send_keys("令和02年")
        self.find_element(By.ID,"span_CmdKakutei").click()
        self.find_element(By.ID,"TxtKesikomiKey").click()
        self.find_element(By.ID,"TxtKesikomiKey").send_keys("")
        self.find_element(By.ID,"TxtKesikomiKey").send_keys("0000000002")
        self.find_element(By.ID,"JiyuuCmb").send_keys("転居先不明")
        self.find_element(By.ID,"TxtSyoribiYMD").click()
        self.find_element(By.ID,"TxtSyoribiYMD").send_keys("")
        self.find_element(By.ID,"TxtSyoribiYMD").send_keys("20210818")
        self.find_element(By.ID,"span_CmdNyuryokuKanryo").click()
        self.save_screenshot_migrate(driver, "QAR010-058-08", True)
 
        self.find_element(By.ID,"span_CmdShoki").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAR010-058-10", True)

        self.find_element(By.ID,"BunsyoKubunCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"BunsyoKubunCmb"),"オンライン納付書")
        self.find_element(By.ID,"NendoCmb").send_keys("令和02年")
        self.find_element(By.ID,"span_CmdKakutei").click()
        self.find_element(By.ID,"TxtKesikomiKey").click()
        self.find_element(By.ID,"TxtKesikomiKey").send_keys("")
        self.find_element(By.ID,"TxtKesikomiKey").send_keys("0000000002")
        self.find_element(By.ID,"JiyuuCmb").send_keys("転居先不明")
        self.find_element(By.ID,"TxtSyoribiYMD").click()
        self.find_element(By.ID,"TxtSyoribiYMD").send_keys("")
        self.find_element(By.ID,"TxtSyoribiYMD").send_keys("20210818")
        self.find_element(By.ID,"span_CmdNyuryokuKanryo").click()
        self.find_element(By.ID,"span_CmdKousin").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAR010-058-12", True)

        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"span_CmdButton1").click()
        self.find_element(By.ID,"koyuCd").click()
        self.find_element(By.ID,"koyuCd").send_keys("")
        self.find_element(By.ID,"koyuCd").send_keys("02220002")
        self.find_element(By.ID,"Kensaku").click()
        self.find_common_buttons()
        self.common_button_click(button_text="文書履歴")
        self.save_screenshot_migrate(driver, "QAR010-058-20", True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAR010-058-21", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAR010-058-22", True)

