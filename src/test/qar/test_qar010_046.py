import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAR010046(FukushiSiteTestCaseBase):
    """TestQAR010046"""
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("Insert_QAR010-046.SQL", params=atena_list)
        super().setUp()

    def tearDown(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("Delete_QAR010-046.SQL", params=atena_list)
        super().tearDown()

    def test_case_qar010_046(self):
        """test_case_qar010_046"""
        driver = None
        test_data = self.common_test_data
        self.do_login()

        self.click_button_by_label("収納管理")
        
        #self.find_element(By.ID,"CmbGyomu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbGyomu"),"高齢")
        self.find_element(By.ID,"CmbGyomu").send_keys("高齢")
        
        #self.find_element(By.ID,"CmbJigyo").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbJigyo"),"養護老人ホーム入所")
        self.find_element(By.ID,"CmbJigyo").send_keys("養護老人ホーム入所")
        self.find_element(By.ID,"CmdKakutei").click()
        self.find_element(By.ID,"CmdButton1").click()
        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("case_040_common_atena_code",""))
        self.find_element(By.ID,"span_Kensaku").click()
        self.find_common_buttons()
        self.common_button_click(button_text="徴収猶予")
        self.save_screenshot_migrate(driver, "QAR010-046-11", True)
        self.find_element(By.ID,"span_CmdTsuika").click()
        self.find_element(By.ID,"TxtShinseiYMD").click()
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("")
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("20210810")
        
        self.find_element(By.ID,"ShinseiCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"ShinseiCmb"),"収入減少")
        self.find_element(By.ID,"TxtKetteiYMD").click()
        self.find_element(By.ID,"TxtKetteiYMD").send_keys("")
        self.find_element(By.ID,"TxtKetteiYMD").send_keys("20210810")
        
        #self.find_element(By.ID,"KetteiCmb").click()
        #self.select_Option(driver,self.find_element(By.ID,"KetteiCmb"),"決定")
        self.find_element(By.ID,"KetteiCmb").send_keys("決定")
        # time.sleep(2)
        self.find_element(By.ID,"CmbTainoSentakuTsuika_1").click()
        self.find_element(By.ID,"CmbTainoSentakuTsuika_2").click()
        self.find_element(By.ID,"TxtYuyoStartYMD").click()
        self.find_element(By.ID,"TxtYuyoStartYMD").send_keys("")
        self.find_element(By.ID,"TxtYuyoStartYMD").send_keys("20210610")
        self.find_element(By.ID,"TxtYuyoEndYMD").click()
        self.find_element(By.ID,"TxtYuyoEndYMD").send_keys("")
        self.find_element(By.ID,"TxtYuyoEndYMD").send_keys("20210810")
        self.find_element(By.ID,"span_CmdTainoSentakuTsuika").click()
        # self.accept_next_alert = True
        self.find_element(By.ID,"span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAR010-046-21", True)
        self.find_element(By.ID,"NoBtn1").click()
        self.save_screenshot_migrate(driver, "QAR010-046-23", True)
        self.find_element(By.ID,"span_CmdKoushin").click()
        self.find_element(By.ID,"TxtYuyoEndYMD").click()
        self.find_element(By.ID,"TxtYuyoEndYMD").send_keys("")
        self.find_element(By.ID,"TxtYuyoEndYMD").send_keys("20200726")
        # self.accept_next_alert = True
        self.find_element(By.ID,"span_CmdShoki").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAR010-046-27", True)
        self.find_element(By.ID,"span_CmdKoushin").click()
        self.find_element(By.ID,"TxtYuyoEndYMD").click()
        self.find_element(By.ID,"TxtYuyoEndYMD").send_keys("")
        self.find_element(By.ID,"TxtYuyoEndYMD").send_keys("20210831")
        self.find_element(By.ID, "span_CmdTainoUtiwakeSentaku").click()
        self.find_element(By.ID, "CmbTainoSentakuTsuika_1").click()
        self.find_element(By.ID, "span_CmdTainoSentakuTsuika").click()
        # self.accept_next_alert = True
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAR010-046-35", True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAR010-046-37", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAR010-046-39", True)

        self.find_common_buttons()
        self.common_button_click(button_text="徴収猶予")
        self.find_element(By.ID, "CmdNextPage").click()
        self.save_screenshot_migrate(driver, "QAR010-046-43", True)
        self.find_element(By.ID, "CmdBackPage").click()
        self.save_screenshot_migrate(driver, "QAR010-046-45", True)
        self.find_element(By.ID, "PageCmb").click()
        dropdown = self.driver.find_element(By.ID, "PageCmb")
        dropdown.find_element(By.XPATH, "//option[. = '03']").click()
        self.find_element(By.ID, "CmdJumpPage").click()
        self.save_screenshot_migrate(driver, "QAR010-046-48", True)

        self.find_element(By.ID, "span_CmdTsuika").click()
        self.find_element(By.ID, "span_CmdTainoSentakuAllSelect").click()
        self.save_screenshot_migrate(driver, "QAR010-046-52", True)
        self.find_element(By.ID, "span_CmdTainoSentakuAllDelete").click()
        self.save_screenshot_migrate(driver, "QAR010-046-54", True)

        self.find_element(By.ID, "span_CmdTainoSentakuAllSelect").click()
        self.find_element(By.ID, "span_CmdSaikeisan").click()
        self.save_screenshot_migrate(driver, "QAR010-046-57", True)
        self.find_element(By.ID, "span_CmdTainoSentakuAllSelect").click()
        self.find_element(By.ID, "span_CmdTainoSentakuCancel").click()
        self.save_screenshot_migrate(driver, "QAR010-046-60", True)

        self.find_element(By.ID, "span_CmdTainoUtiwakeSentaku").click()
        self.save_screenshot_migrate(driver, "QAR010-046-62", True)
        self.find_element(By.ID, "CmbTainoSentakuTsuika_3").click()
        self.find_element(By.ID, "CmbTainoSentakuTsuika_4").click()
        self.find_element(By.ID, "span_CmdTainoSentakuTsuika").click()
        self.find_element(By.ID, "span_CmdTainoUtiwakeAllSelect").click()
        self.save_screenshot_migrate(driver, "QAR010-046-66", True)
        self.find_element(By.ID, "span_CmdTainoUtiwakeAllDelete").click()
        self.save_screenshot_migrate(driver, "QAR010-046-68", True)

        self.find_element(By.ID, "TxtShinseiYMD").click()
        self.find_element(By.ID, "TxtShinseiYMD").send_keys("20210810")
        self.find_element(By.ID, "ShinseiCmb").click()
        dropdown = self.driver.find_element(By.ID, "ShinseiCmb")
        dropdown.find_element(By.XPATH, "//option[. = '収入減少']").click()
        self.find_element(By.ID, "TxtKetteiYMD").click()
        self.find_element(By.ID, "TxtKetteiYMD").send_keys("20210810")
        self.find_element(By.ID, "KetteiCmb").click()
        dropdown = self.driver.find_element(By.ID, "KetteiCmb")
        dropdown.find_element(By.XPATH, "//option[. = '決定']").click()
        self.find_element(By.ID, "TxtYuyoStartYMD").click()
        self.find_element(By.ID, "TxtYuyoStartYMD").send_keys("20210610")
        self.find_element(By.ID, "TxtYuyoEndYMD").click()
        self.find_element(By.ID, "TxtYuyoEndYMD").send_keys("20210810")
        self.find_element(By.ID, "span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAR010-046-74", True)

        self.find_element(By.ID, "span_CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAR010-046-76", True)

        self.find_element(By.ID, "GOBACK").click()
        self.save_screenshot_migrate(driver, "QAR010-046-78", True)