import time
import datetime
from datetime import timedelta
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01073305(FukushiSiteTestCaseBase):
    """TestQAJ010_01073305"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01073305"]
        super().setUp()
    
    # 支給変更決定通知書兼利用者負担額減額・免除等変更決定通知書が出力されることを確認する
    def test_QAJ010_01073305(self):
        """支給変更決定通知書兼利用者負担額減額・免除等変更決定通知書"""

        case_data = self.test_data["TestQAJ010_01073305"]
        date = datetime.date.today()
        today = format(date, '%Y%m%d')

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")
        
        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")
        
        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")
        
        # 4 バッチ起動画面: 業務「障害」選択
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        
        # 5 バッチ起動画面: 事業「障害児支援」選択
        self.form_input_by_id(idstr="JigyoSelect", text="障害児支援")
        
        # 6 バッチ起動画面: 処理区分「随時処理_通所」選択
        self.form_input_by_id(idstr="ShoriKubunSelect", text="随時処理_通所")
        
        # 7 バッチ起動画面: 処理分類「障害児通所支給変更決定通知書・受給者証出力」選択
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="障害児通所支給変更決定通知書・受給者証出力")
        
        # 8 バッチ起動画面:支給変更決定通知書兼利用者負担額・減額免除等変更決定通知書出力　ボタン押下
        self.find_element(By.ID,"Sel2").click()
        self.screen_shot("バッチ起動画面_8")
        
        # 9 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_9")
        
        # 10 バッチ起動画面: 「提出期限」、「出力順序」入力
        # ジョブパラ定義
        params = [
            {"title":"提出期限", "type": "text", "value": today},
            {"title":"出力順序", "type": "select", "value":case_data.get("syutsuryoku")}
        ]
        # ジョブパラに入力
        self.set_job_params(params)
        self.form_input_by_id(idstr="UQZGC402_PrintSelect", text="ファイルアウト")
        self.form_input_by_id(idstr="UQZGC402_chkPrinter", value="0")
        
        # 11 バッチ起動画面: 「処理開始」ボタン押下
        # ジョブ実行(実行した日時を保持しておく)
        exec_datetime = self.exec_batch_job()
        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        
        # 12 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.screen_shot("バッチ起動画面_12")
        
        # 13 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        
        # 14 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_14")
        
        # 15 ジョブ実行履歴画面: No.1 支給変更決定通知書兼利用者負担額減額免除等変更決定通知書出力  の状態が「正常終了」となったらエビデンス取得
        # 処理が終わるまで待機する
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ実行履歴画面_15")
        
        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()
        # 今回の処理で作成したPDFのDL（戻値はDLしたファイル数）
        report_dl_count = self.get_job_report_pdf(exec_datetime=exec_datetime)
        self.return_click()