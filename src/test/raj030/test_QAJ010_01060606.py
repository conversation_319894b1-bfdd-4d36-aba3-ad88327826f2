import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060606(FukushiSiteTestCaseBase):
    """TestQAJ010_01060606"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060606"]
        super().setUp()
    
    # モニタリング期間変更通知書を出力できることを確認する。
    def test_QAJ010_01060606(self):
        """モニタリング期間変更通知書出力_児_"""
        
        case_data = self.test_data["TestQAJ010_01060606"]
        atena_code = case_data.get("atena_code", "")
        insatsuChk = case_data.get("insatsuChk", "")
        hakkoChk = case_data.get("hakkoChk", "")
        hakkoTxt = case_data.get("hakkoTxt", "")

        self.do_login()
        
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")
        
        # 4 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_4")
        
        # 5 バッチ起動画面: 業務「障害」選択事業「障害児支援」選択処理区分「随時処理_通所」選択処理分類「相談支援通知書出力」選択
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="障害児支援")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="随時処理_通所")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="相談支援通知書出力")
        self.screen_shot("バッチ起動画面_5")
        
        # 6 バッチ起動画面: 「モニタリング期間変更通知書出力」No2ボタン押下
        self.click_button_by_label("2")
        
        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")
        
        # 8 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        # 9 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        
        # 10 ジョブ実行履歴画面: 「検索」ボタン押下
        # 処理が終わるまで待機する
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 11 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_11")
        
        # 12 ジョブ実行履歴画面：「帳票履歴」ボタン押下
        self.click_report_log()
        # 今回の処理で作成したPDFのDL（戻値はDLしたファイル数）
        report_dl_count = self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 13 帳票履歴画面: 表示
        self.screen_shot("帳票履歴画面_13")
        
        # 14 帳票履歴画面: 「戻る」ボタン押下
        self.return_click()
        
        # 15 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_15")
