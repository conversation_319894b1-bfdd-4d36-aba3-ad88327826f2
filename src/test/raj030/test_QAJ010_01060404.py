import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060404(FukushiSiteTestCaseBase):
    """TestQAJ010_01060404"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060404"]
        super().setUp()
    
    # 児童通所支援給付の支給決定を登録できることを確認する。 受給者証番号が自動附番されることを確認する。決定サービスを登録できることを確認する。利用者負担額を登録できることを確認する。
    def test_QAJ010_01060404(self):
        """児童通所支援給付支給決定登録"""
        
        case_data = self.test_data["TestQAJ010_01060404"]
        atena_code = case_data.get("atena_code", "")
        report_name = case_data.get("report_name","")
        hakkoTxt = case_data.get("hakkoTxt", "")
        
        date = datetime.date.today()
        thisyear = format(date, '%Y')
        thismonth = format(date, '%m')
        next_year = int(thisyear) + 1
        next_ymd = str(next_year) + thismonth + "01"
        today = format(date, '%Y%m%d')

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ030")

        # 2 障害児支援申請管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")
        
        # 3 障害児支援申請管理画面: 表示
        self.screen_shot("障害児支援申請管理画面_3")
        
        # 4 障害児支援申請管理画面: 決定日「20230601」入力、決定結果「却下」選択、却下理由「その他」選択、決定理由テキスト「却下理由テキスト入力テスト」入力
        self.form_input_by_id(idstr="TxtKetteiYMD", value=today)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="却下")
        #self.form_input_by_id(idstr="KetteiRiyuCmb", text="その他")
        #self.form_input_by_id(idstr="TxtTokkijiko", value="却下理由テキスト入力テスト")
        
        # 5 障害児支援申請管理画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        
        # 6 障害児支援申請管理画面: 表示
        self.screen_shot("障害児支援申請管理画面_6")
        
        # 7 障害児支援申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 8 障害児支援申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害児支援申請管理画面_8")
        
        # 9 障害児支援申請管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        
        # 10 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_10")
        
        # 11 帳票印刷画面: 「却下決定通知書（障害児通所給付費）」行の印刷チェックボックス選択、「却下決定通知書（障害児通所給付費）」行の発行年月日チェックボックス選択、発行年月日「20230601」入力
        # 12 帳票印刷画面: 「印刷」ボタン押下
        exec_params = [
            {"report_name": case_data.get("report_name",""),
             "params":[
                 {"title": "交付日", "value":hakkoTxt},
                {"title": "文書番号", "value":"11111"}
                ]
            }
        ] 
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_11")
        
        # 13 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        
        # 14 帳票（PDF）: 表示
        #self.screen_shot("帳票（PDF）_14")
        
        # 15 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 16 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        
        # 17 障害児支援申請管理画面: 表示
        self.screen_shot("障害児支援申請管理画面_17")
        
        # 18 障害児支援申請管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")
        self.click_by_id("CmdKetteiKirikae")
        
        # 19 障害児支援決定管理画面: 表示
        self.screen_shot("障害児支援決定管理画面_19")
        
        # 20 障害児支援決定管理画面: 支給決定日「20230601」入力、決定結果「給付」選択
        self.form_input_by_id(idstr="TxtKetteiYMD", value=today)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="給付")

        # 21 障害児支援決定管理画面: 特記事項「食費等実費負担に係る境界層措置対象者」選択、その他特記事項「その他特記事項入力テスト」入力、受給者証交付年月日「20230601」入力、決定時備考「決定時備考入力テスト」入力
        self.form_input_by_id(idstr="TokkiJikouCmb_1", text="食費等実費負担に係る境界層措置対象者")
        self.form_input_by_id(idstr="TxtTokkijiko", value="その他特記事項入力テスト")
        self.form_input_by_id(idstr="TxtJukyushashoKofuYMD", value=today)
        self.form_input_by_id(idstr="TxtKetteijiBiko", value="決定時備考入力テスト")
        
        # 22 障害児支援決定管理画面: 「決定支援種類」ボタン押下
        self.click_button_by_label("決定支援種類")
        
        # 23 障害児支援決定管理画面: 表示
        self.screen_shot("障害児支援決定管理画面_23")
        
        # 24 障害児支援決定管理画面: 開始日「20230601」入力、資格状態「追加」選択、サービス種類「医療型児童発達支援」選択、サービス区分　医療型児童発達支援肢体　行の状態区分「追加」選択、サービス区分　医療型児童発達支援肢体　以外の行の状態区分「　　」（空白）選択、サービス区分　医療型児童発達支援肢体　行の決定支給量「20」日／月入力、サービス決定備考「サービス決定備考入力テスト」入力
        self.form_input_by_id(idstr="TxtKetteiServiceShikyuKetteiYMD", value=today)
        self.form_input_by_id(idstr="ServiceShuruiJokyoCmb_1", text="追加")
        self.form_input_by_id(idstr="ServiceShuruiCmb_1", text="医療型児童発達支援")
        self.form_input_by_id(idstr="ServiceKubunJokyoCmb_1_1", text="追加")
        self.form_input_by_id(idstr="TxtShikyuryo1_1_1", value="20")
        self.click_by_id("span_ServiceKetteiBikoBtn_1_1")
        self.form_input_by_id(idstr="ServiceKetteiBiko_1_1", value="サービス決定備考入力テスト")
        
        # 25 障害児支援決定管理画面: 「負担額」ボタン押下
        self.click_button_by_label("負担額")
        
        # 26 障害児支援決定管理画面: 表示
        self.screen_shot("障害児支援決定管理画面_26")
        
        # 27 障害児支援決定管理画面: 「世帯範囲情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("世帯範囲情報")
        
        # 28 世帯範囲情報画面: 表示
        
        # 29 世帯範囲情報画面: 世帯員入日「20230401」入力
        self.form_input_by_id(idstr="TxtSetaiStartYMD1", value="20230401")
        self.screen_shot("世帯範囲情報画面_29")
        
        # 30 世帯範囲情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 31 障害児支援決定管理画面: 表示
        self.screen_shot("障害児支援決定管理画面_31")
        
        # 32 障害児支援決定管理画面: 「収入・資産入力」ボタン押下
        self.click_button_by_label("収入・資産入力")
        
        # 33 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_33")
        
        # 34 減免申請・収入資産登録画面: 「確定」ボタン押下
        self.click_button_by_label("確定")
        
        # 35 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_35")
        
        # 36 減免申請・収入資産登録画面: 「取込」ボタン押下
        self.click_button_by_label("取込")
        self.form_input_by_id(idstr="KazeiHikazeiRd_0", value="1")
        
        # 37 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_37")
        
        # 38 減免申請・収入資産登録画面: 「計算ボタン」ボタン押下
        self.click_button_by_label("計算ボタン")
        
        # 39 減免申請・収入資産登録画面: 表示
        self.screen_shot("減免申請・収入資産登録画面_39")
        
        # 40 減免申請・収入資産登録画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 41 障害児支援決定管理画面: 表示
        self.screen_shot("障害児支援決定管理画面_41")
        
        # 42 障害児支援決定管理画面: 上限管理対象　チェック、個別減免有無　チェック、負担額判定時備考「負担額判定時備考入力テスト」入力
        self.form_input_by_id(idstr="ChkJogenKanriTaisho", value="1")
        self.form_input_by_id(idstr="ChkKobetsuGenmenUmu", value="1")
        self.form_input_by_id(idstr="TxtFutangakuHanteijiBiko", value="負担額判定時備考入力テスト")
        self.screen_shot("障害児支援決定管理画面_42")
        
        # 43 障害児支援決定管理画面: 「障害児支援医療」ボタン押下
        self.click_button_by_label("障害児支援医療")
        
        # 44 障害児支援決定管理画面: 表示
        self.screen_shot("障害児支援決定管理画面_44")
        
        # 45 障害児支援決定管理画面: 公費受給者番号「9000001」入力、医療部分負担上限月額「100」入力、食事負担限度額「100」入力、医療受給者証交付年月日「20230601」入力
        #self.form_input_by_id(idstr="TxtRyouyouKaigoJukyushaNo", value="9000001")
        self.form_input_by_id(idstr="TxtRyouyouKaigoJogengaku", value="100")
        self.form_input_by_id(idstr="TxtShokujiRyouyouJogengaku", value="100")
        self.form_input_by_id(idstr="TxtIryoJukyushashoKofuYMD", value=today)
        
        # 46 障害児支援決定管理画面: 「医療機関」ボタン押下
        self.click_button_by_label("医療機関")
        
        # 47 医療機関検索画面: 表示
        self.screen_shot("医療機関検索画面_47")
        
        # 48 医療機関検索画面: 医療機関一覧「１」ボタン押下
        self.click_button_by_label("検索")
        self.click_button_by_label("1")
        
        # 49 障害児支援決定管理画面: 表示
        self.screen_shot("障害児支援決定管理画面_49")
        
        # 50 障害児支援決定管理画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        
        # 51 障害児支援申請管理画面: 表示
        self.screen_shot("障害児支援申請管理画面_51")
        
        # 52 障害児支援申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 53 障害児支援申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害児支援申請管理画面_53")
        
