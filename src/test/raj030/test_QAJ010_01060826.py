import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060826(FukushiSiteTestCaseBase):
    """TestQAJ010_01060826"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060826"]
        super().setUp()
    
    # 児童通所支援給付の支給決定登録が完了している対象者に対して、負担上限額変更申請を登録できることを確認する。
    def test_QAJ010_01060826(self):
        """負担上限額変更申請登録"""
        
        case_data = self.test_data["TestQAJ010_01060826"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ030")
        # 2 障害児支援申請管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")
        
        # 3 障害児支援申請管理画面: 申請区分「利用者負担額変更」選択、申請理由「その他」選択
        time.sleep(1)
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="利用者負担額変更")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="その他")
        self.form_input_by_id(idstr="TxtShinseiRiyu", value="その他備考テスト")
        self.screen_shot("障害児支援申請管理画面_3")
        
        # 4 障害児支援申請管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")
        
        # 5 障害児支援申請管理画面: 申請日「20230701」入力
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230701")
        self.screen_shot("障害児支援申請管理画面_5")
        
        # 6 障害児支援申請管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 7 障害児支援申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害児支援申請管理画面_7")
        
