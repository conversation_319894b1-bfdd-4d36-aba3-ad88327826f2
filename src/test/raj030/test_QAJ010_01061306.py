import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01061306(FukushiSiteTestCaseBase):
    """TestQAJ010_01061306"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01061306"]
        super().setUp()
    
    # 支給決定取消決定登録できることを確認する。
    def test_QAJ010_01061306(self):
        """支給決定取消決定登録"""
        
        case_data = self.test_data["TestQAJ010_01061306"]
        atena_code = case_data.get("atena_code", "")
        date = datetime.date.today()
        thismonth = format(date, '%Y%m')
        today = format(date, '%Y%m%d')

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ030")
        # 2 障害児支援申請管理画面: 「決定内容入力」ボタン押下
        self.find_element(By.ID,"span_CmdKettei").click()
        
        # 3 障害児支援決定管理画面: 表示
        self.screen_shot("障害児支援決定管理画面_3")
        
        # 4 障害児支援決定管理画面: 決定日「カレンダーで入力当日の日付」選択決定結果「決定」選択
        self.find_element(By.ID,"TxtKetteiYMD").send_keys("")
        self.find_element(By.ID,"TxtKetteiYMD").send_keys(today)
        self.find_element(By.ID,"KetteiKekkaCmb").send_keys("決定")
        
        # 5 障害児支援決定管理画面: 認定証明書チェックボックス選択
        #self.find_element(By.ID,"ChkTenshutsu").click()
        
        
        # 6 障害児支援決定管理画面: 「決定支援種類」ボタン押下
        self.find_element(By.ID,"span_CmdKetteiServiceDisp").click()
        
        # 7 障害児支援決定管理画面: 表示
        self.screen_shot("障害児支援決定管理画面_7")
        
        # 8 障害児支援決定管理画面: 取消年月日「カレンダーで入力当日の日付」選択
        # 取消年月日は入力できない
        self.find_element(By.ID,"TxtKetteiServiceTorikeshiYMD").send_keys(today)
        
        # 9 障害児支援決定管理画面: 「入力完了」ボタン押下
        self.find_element(By.ID,"span_CmdTouroku").click()
        
        # 10 障害児支援申請管理画面: 表示
        self.screen_shot("障害児支援申請管理画面_10")
        
        # 11 障害児支援申請管理画面: 「登録」ボタン押下
        self.find_element(By.ID,"span_CmdTouroku").click()
        time.sleep(3)
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        # 12 障害児支援申請管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("障害児支援申請管理画面_12")
        
