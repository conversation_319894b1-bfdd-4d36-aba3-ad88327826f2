import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181605(FukushiSiteTestCaseBase):
    """TestQAC050_181605"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181605"]
        super().setUp()

    # 返納について計画情報を作成できることを確認する。
    def test_QAC050_181605(self):
        """内払調整計画情報入力"""

        case_data = self.test_data["TestQAC050_181605"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「債権管理」ボタン押下
        self.saiken_kanri_click()  # Button with ID: CmdProcess7_2 instead of self.click_button_by_label("債権管理")

        # 3 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_3")

        # 4 債権履歴画面: 事業「児童扶養手当」選択絞り込み条件「返納中のみ」チェック
        self.form_input_by_id(idstr="CmbGyomu", text=case_data.get("cmb_gyomu", ""))
        self.form_input_by_id(idstr="RadioM", value=case_data.get("radio_m", ""))
        self.screen_shot("債権履歴画面_4")

        # 5 債権履歴画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 6 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_6")

        # 7 債権履歴画面: 該当者一覧「1」ボタン押下
        # Instead of self.click_button_by_label("該当者一覧1")
        atena_col = '3'
        table_idx = 0
        th_idx = 0
        tr_elem = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
        for elem in tr_elem:
            table_idx += 1
            try:
                td_elem = elem.find_element(By.CSS_SELECTOR, "td:nth-child(" + atena_col + ")")
            except Exception:
                th_idx += 1
                continue
            if atena_code == td_elem.text:
                table_idx = table_idx - th_idx
                self.click_by_id("Sel" + str(table_idx))
                break

        # 8 債権情報画面: 表示

        # 9 債権情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 10 債権情報画面: 「計算」ボタン押下
        self.click_button_by_label("計算")

        # 11 債権情報画面: 「計画作成」ボタン押下
        self.click_button_by_label("計画作成")

        # 12 返済計画作成画面: 表示
        self.screen_shot("返済計画作成画面_12")

        # 13 返済計画作成画面: 「再計画」ボタン押下
        self.click_button_by_label("再計画")

        # 14 返済計画作成画面: 返納予定月額「1000」返納予定開始年月「202306」
        self.form_input_by_id(idstr="TxtYoteigaku", value=case_data.get("txt_yoteigaku", ""))
        self.form_input_by_id(idstr="TxtKaishi", value=case_data.get("txt_kaishi", ""))
        self.screen_shot("返済計画作成画面_14")

        # 15 返済計画作成画面: 「計画作成」ボタン押下
        self.click_button_by_label("計画作成")

        # 16 返済計画作成画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 17 債権情報画面: 表示
        self.screen_shot("債権情報画面_17")

        # 18 債権情報画面: 「戻る」ボタン押下
        self.return_click()
        self.return_click()

        # 19 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_19")
