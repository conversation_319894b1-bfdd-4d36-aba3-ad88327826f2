import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180808(FukushiSiteTestCaseBase):
    """TestQAC050_180808"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180808"]
        super().setUp()

    # 証書の交付年月日、返付年月日の情報が登録できることを確認する。
    def test_QAC050_180808(self):
        """証書情報入力"""

        case_data = self.test_data["TestQAC050_180808"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.do_login()
        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 証書交付年月日「20230602」証書返付年月日「20230602」
        self.form_input_by_id(idstr="TxtShoushoKoufuYMD", value=case_data.get("txt_shousho_koufu_ymd", ""))
        self.form_input_by_id(idstr="TxtShoushoHenpuYMD", value=case_data.get("txt_shousho_henpu_ymd", ""))
        self.screen_shot("児童扶養手当資格管理画面_2")

        # 3 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 4 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")  # Button with ID: CmdTouroku
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 5 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_5")
