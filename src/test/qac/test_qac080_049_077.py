import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAC080049077(FukushiSiteTestCaseBase):
    """TESTQAC080049077"""

    def test_case_qac080_049_077(self):
        """test_case_qac080_049_077"""
        driver = None
        test_data = self.common_test_data
        
        self.do_login()
        self.find_element(By.ID,"CmdProcess1_1").click()
        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("case_common_qac080_atena_code23"))
        self.save_screenshot_migrate(driver, "QAC080-049-04", True)
        self.find_element(By.ID,"span_Kensaku").click()
        self.save_screenshot_migrate(driver, "QAC080-049-06", True)
        self.find_element(By.NAME,"img").click()
        self.find_element(By.ID,"btnCommon27").click()
        self.find_element(By.ID,"CmbGyomu").click()
        self.find_element(By.ID,"CmbGyomu").send_keys("児童")
        self.save_screenshot_migrate(driver, "QAC080-049-09", True)

        self.find_element(By.ID,"span_CmdShoki").click()
        self.save_screenshot_migrate(driver, "QAC080-049-10", True)
        self.find_element(By.ID,"CmbGyomu").click()
        # self.find_element(By.ID,"CmbGyomu").send_keys("児童")
        self.select_Option(driver,self.find_element(By.ID,"CmbGyomu"),"児童")

        self.find_element(By.ID,"CmbJigyo").click()
        # self.find_element(By.ID,"CmbJigyo").send_keys("児童育成手当")
        self.select_Option(driver,self.find_element(By.ID,"CmbJigyo"),"児童育成手当")
        self.find_element(By.ID,"ChkShori2").click()
        #self.find_element(By.ID,"TxtKikanStaYMD2").click()
        #self.find_element(By.ID,"TxtKikanStaYMD2").send_keys("")
        #self.find_element(By.ID,"TxtKikanStaYMD2").send_keys("20210801")
        self.save_screenshot_migrate(driver, "QAC080-049-13", True)


        self.find_element(By.ID,"span_CmdToroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-049-15", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC080-049-16", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC080-049-17", True)
