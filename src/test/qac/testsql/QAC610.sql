DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

--シナリオNo.QAC0610-001-1
DELETE WR$$JICHITAI_CODE$$QA..QAC資格履歴          WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC手当資格内容      WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC手当障害要件      WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC支払履歴          WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC差止履歴          WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC不支給履歴        WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC過払月額          WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC過払情報          WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC債権計画          WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC債権計画月別      WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC債権情報          WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC債権返納          WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC調整月額          WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC特記事項          WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ受給状況          WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ福祉世帯          WHERE 本人宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ提出書類内容      WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ現況履歴          WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ現況提出書類      WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QZ..QZ口座マスタ         WHERE 固有コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ福祉税マスタ      WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ所得税マスタ      WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ連携税マスタ      WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ福祉税マスタ      WHERE 宛名コード = '100300000100517' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ所得税マスタ      WHERE 宛名コード = '100300000100517' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ連携税マスタ      WHERE 宛名コード = '100300000100517' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ所得判定_計算結果 WHERE 本人宛名コード = '100300000100483' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC汎用現況履歴      WHERE 宛名コード = '100300000100483' AND 業務コード = 'QAC610'

--シナリオNo.QAC0610-003-1
DELETE WR$$JICHITAI_CODE$$QA..QAC資格履歴          WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC手当資格内容      WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC手当障害要件      WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC支払履歴          WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC差止履歴          WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC不支給履歴        WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC過払月額          WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC過払情報          WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC債権計画          WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC債権計画月別      WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC債権情報          WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC債権返納          WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC調整月額          WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC特記事項          WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ受給状況          WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ福祉世帯          WHERE 本人宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ提出書類内容      WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ現況履歴          WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ現況提出書類      WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QZ..QZ口座マスタ         WHERE 固有コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ福祉税マスタ      WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ所得税マスタ      WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ連携税マスタ      WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ福祉税マスタ      WHERE 宛名コード = '100300000100562' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ所得税マスタ      WHERE 宛名コード = '100300000100562' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ連携税マスタ      WHERE 宛名コード = '100300000100562' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAZ所得判定_計算結果 WHERE 本人宛名コード = '100300000100514' AND 業務コード = 'QAC610'
DELETE WR$$JICHITAI_CODE$$QA..QAC汎用現況履歴      WHERE 宛名コード = '100300000100514' AND 業務コード = 'QAC610'


IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END