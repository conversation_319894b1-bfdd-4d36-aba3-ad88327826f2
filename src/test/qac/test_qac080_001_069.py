import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAC080001069(FukushiSiteTestCaseBase):
    """TESTQAC080001069"""
        
    def test_case_qac080_001_069(self):
        """test_case_qac080_001_069"""
        driver = None
        test_data = self.common_test_data
        
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=test_data.get("case_common_qac080_atena_code14"), gyoumu_code="QAC080")
        self.save_screenshot_migrate(driver, "QAC080-043-70-1", True)
        self.find_element(By.ID,"CmdShinsei").click()
        #self.find_element(By.ID,"ShinseiShubetsuCmb").click()
        #self.select_Option(driver,self.find_element(By.ID,"ShinseiShubetsuCmb"),"認定請求")
        self.find_element(By.ID,"ShinseiShubetsuCmb").send_keys("新規")
        #self.find_element(By.ID,"ShinseiShubetsuCmb").click()
        time.sleep(1)

        self.find_element(By.ID,"CmdKakutei").click()
        #self.find_element(By.ID,"ShinseiRiyuuCmb").click()
        #self.select_Option(driver,self.find_element(By.ID,"ShinseiRiyuuCmb"),"出生")
        self.find_element(By.ID,"ShinseiRiyuuCmb").send_keys("出生")
        #self.find_element(By.ID,"CmdKakutei").click()
        #self.find_element(By.ID,"ShinseiRiyuuCmb").click()
        self.find_element(By.ID,"CmdKakutei").click()
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("")
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("20210701")

        self.select_Option(driver,self.find_element(By.ID,"TantoShokatsukuCmb"),"第一区")
        self.find_element(By.ID,"TantoShokatsukuCmb").send_keys("第一区")
        self.find_element(By.ID,"TxtKaitei").send_keys("")
        self.find_element(By.ID,"TxtKaitei").send_keys("202108")
        #self.find_element(By.XPATH,"//div[@id='parentDiv']/table[11]/tbody/tr[2]/td[4]").click()
        self.find_element(By.ID,"RdoHiyo").click()
        self.find_element(By.ID,"CmdJidouTsuika").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-7", True)
        self.find_element(By.ID,"Sel2").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-9", True)
        #self.find_element(By.ID,"CmbSanteiGaitoJiyu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbSanteiGaitoJiyu"),"出生")
        self.find_element(By.ID,"CmbSanteiGaitoJiyu").send_keys("出生")
        #self.find_element(By.ID,"CmbSanteiGaitoJiyu").click()
         #児童との関係
        self.select_Option(driver,self.find_element(By.ID,"CmbJidoKankei"),"父母")

        self.find_element(By.ID,"CmdInputCompleteButton").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-12", True)
        self.find_element(By.ID,"CmdGetsugakuKeisan").click()
        self.find_element(By.ID,"btnCommon4").click()
        # self.assertEqual(u" ",self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-15", True)
        self.find_element(By.ID,"CmdNo0").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-17", True)
        self.find_element(By.ID,"CmdInput").click()

        self.find_element(By.ID,"CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-19", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-21", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-23", True)
        # self.find_element(By.ID,"btnCommon6").click()
        # 口座情報ボタン押下
        self.find_element(By.ID,"btnCommon5").click()
        # self.assertEqual(u" ",self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-25", True)
        self.find_element(By.ID,u"口座情報_u_Tuika").click()
        self.find_element(By.ID,u"口座情報_u_KinyuKikanCode").click()
        self.find_element(By.ID,u"口座情報_u_KinyuKikanCode").click()
        self.find_element(By.ID,u"口座情報_u_KinyuKikanCode").send_keys("")
        self.find_element(By.ID,u"口座情報_u_KinyuKikanCode").send_keys("0001")
        self.find_element(By.ID,u"口座情報_u_ShitenCode").click()
        self.find_element(By.ID,u"口座情報_u_ShitenCode").click()
        self.find_element(By.ID,u"口座情報_u_ShitenCode").send_keys("")
        self.find_element(By.ID,u"口座情報_u_ShitenCode").send_keys("001")
        #self.find_element(By.XPATH,"//div[@id='_wr_body_panel']/table[4]/tbody/tr/td/table[6]/tbody/tr/td[2]").click()
        self.find_element(By.ID,u"口座情報_u_KouzaBango").click()
        self.find_element(By.ID,u"口座情報_u_KouzaBango").click()
        self.find_element(By.ID,u"口座情報_u_KouzaBango").send_keys("")
        self.find_element(By.ID,u"口座情報_u_KouzaBango").send_keys("0030024")
        self.find_element(By.ID,u"口座情報_u_MeigininKana").click()
        self.find_element(By.ID,u"口座情報_u_MeigininKana").click()
        self.find_element(By.ID,u"口座情報_u_MeigininKana").send_keys("")
        self.find_element(By.ID,u"口座情報_u_MeigininKana").send_keys(u"ジテ　タロウ")

        self.find_element(By.ID,u"口座情報_u_Touroku").click()
        self.assertEqual(u"登録します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-29", True)

        self.find_element(By.ID,u"口座情報_u_Sakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-30", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-32", True)
        self.find_element(By.ID,"btnCommon2").click()
        # self.assertEqual(u" ",self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-34", True)
        self.find_element(By.ID,"JukyuCmb_2").click()
        self.find_element(By.ID,"JukyuCmb_2").click()
        self.find_element(By.ID,"Kakutei_BTN").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-37", True)
        # self.find_element(By.ID,"btnCommon14").click()
        # メモ情報ボタン押下
        self.find_element(By.ID,"btnCommon12").click()
        # self.assertEqual(u" ",self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-39", True)
        self.find_element(By.ID,"CmdTsuika").click()
        self.find_element(By.ID,"TxtNyuryokuYMD").send_keys("")
        self.find_element(By.ID,"TxtNyuryokuYMD").send_keys("20210701")
        self.find_element(By.ID,"TxtNyuryokuHH").send_keys("")
        self.find_element(By.ID,"TxtNyuryokuHH").send_keys("16")
        self.find_element(By.ID,"TxtNyuryokuMM").send_keys("")
        self.find_element(By.ID,"TxtNyuryokuMM").send_keys("00")
        self.find_element(By.ID,"TxtNyurkTantoCode").click()
        self.find_element(By.ID,"TxtNaiyo").send_keys("")
        self.find_element(By.ID,"TxtNaiyo").send_keys(u"あいうえお")

        self.find_element(By.ID,"CmdToroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-42", True)

        self.find_element(By.ID,"CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-43", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-45", True)
        # self.find_element(By.ID,"btnCommon15").click()
        # 連絡先管理ボタン押下
        self.find_element(By.ID,"btnCommon13").click()
        # self.assertEqual(u" ",self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-47", True)
        self.find_element(By.ID,"BtnTsuika_Honnin").click()
        self.find_element(By.ID,"TxtTelJitaku").send_keys("")
        self.find_element(By.ID,"TxtTelJitaku").send_keys("123456789")

        self.find_element(By.ID,"BtnTouroku_Honnin").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-50", True)

        self.find_element(By.ID,"BtnSakujyo_Honnin").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-51", True)
        self.find_element(By.ID,"BtnTsuika_Kinkyu").click()
        self.find_element(By.ID,"TxtTelJitaku_Kinkyu").send_keys("")
        self.find_element(By.ID,"TxtTelJitaku_Kinkyu").send_keys("123456789")
        #
        #self.find_element(By.ID,"BtnTouroku_Kinkyu").click()
        #self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.find_element(By.ID,"TxtKanaNM").send_keys("")
        self.find_element(By.ID,"TxtKanaNM").send_keys(u"ジテ　タロウ")
        self.find_element(By.ID,"TxtKanjiNM").send_keys("")
        self.find_element(By.ID,"TxtKanjiNM").send_keys(u"児手　太郎")

        self.find_element(By.ID,"BtnTouroku_Kinkyu").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        #self.save_screenshot("..//evidence/" + "QAC080-043-70-54" +".png")
        self.save_screenshot_migrate(driver, "QAC080-043-70-54", True)

        self.find_element(By.ID,"BtnSakujyo_Kinkyu").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        #self.save_screenshot("..//evidence/" + "QAC080-043-70-55" +".png")
        self.save_screenshot_migrate(driver, "QAC080-043-70-55", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-57", True)
        # 「特記事項」ボタンがないため、コメントアウト
        # self.find_element(By.NAME,"img").click()
        # self.find_element(By.ID,"btnCommon23").click()
        # self.save_screenshot_migrate(driver, "QAC080-043-70-59", True)
        # self.find_element(By.ID,"BtnTsuika").click()
        # self.find_element(By.ID,"ChkFlg_2").click()

        # self.find_element(By.ID,"BtnTouroku").click()
        # self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        # self.save_screenshot_migrate(driver, "QAC080-043-70-62", True)

        # self.find_element(By.ID,"BtnSakujyo").click()
        # self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        # self.save_screenshot_migrate(driver, "QAC080-043-70-63", True)
        # self.find_element(By.ID,"GOBACK").click()
        # self.find_element(By.ID,"CmdGetsugakuKeisan").click()

        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-67", True)
        self.find_element(By.ID,"CmdKettei").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-69", True)
        self.find_element(By.ID,"TxtKetteiYMD").send_keys("")
        self.find_element(By.ID,"TxtKetteiYMD").send_keys("20210701")
        #self.find_element(By.ID,"KetteiKekkaCmb").click()
        #self.select_Option(driver,self.find_element(By.ID,"KetteiKekkaCmb"),"決定")
        self.find_element(By.ID,"KetteiKekkaCmb").send_keys("決定")
        #self.find_element(By.ID,"KetteiKekkaCmb").click()
        time.sleep(1)
        self.find_element(By.ID,"CmdGetsugakuKeisan").click()

        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-72", True)
        self.find_element(By.ID,"btnCommon1").click()
        # self.assertEqual(u" ",self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-043-70-74", True)
        self.find_element(By.ID,"CmdNo9").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-76", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-77", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC080-043-70-78", True)