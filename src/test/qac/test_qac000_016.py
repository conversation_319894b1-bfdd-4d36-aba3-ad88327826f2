import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAC000016(FukushiSiteTestCaseBase):
    """TESTQAC000016"""
        
    def test_case_qac000_016(self):
        """test_case_qac000_016"""
        driver = None
        test_data = self.common_test_data
        self.do_login()
        self.find_element(By.ID,"CmdProcess20_1").click()
        self.find_element(By.ID,"span_CmdButton1").click()
        self.save_screenshot_migrate(driver, "QAC000-016-03", True)
        self.find_element(By.ID,"TxtKikanStaYMD").click()
        self.find_element(By.ID,"TxtKikanStaYMD").send_keys("")
        self.find_element(By.ID,"TxtKikanStaYMD").send_keys("20220901")
        self.find_element(By.ID,"span_BtnKikanKakutei").click()
        self.save_screenshot_migrate(driver, "QAC000-016-05", True)

        #self.find_element(By.ID,"CmbShozokuKu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbShozokuKu"),"９９３０１")
        self.find_element(By.ID,"CmbShozokuKu").send_keys("９９３０１")
        # time.sleep(2)
        self.find_element(By.ID,"span_BtnKensaku").click()

        #self.find_element(By.ID,"CmbShozokuBu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbShozokuBu"),"福祉部")
        self.find_element(By.ID,"CmbShozokuBu").send_keys("福祉部")
        # time.sleep(2)
        self.find_element(By.ID,"span_BtnSyozokuAdd").click()
        self.save_screenshot_migrate(driver, "QAC000-016-08", True)
        self.find_element(By.ID,"SyozokuKaNameLabel").click()
        self.find_element(By.ID,"SyozokuKaNameLabel").send_keys("")
        self.find_element(By.ID,"SyozokuKaNameLabel").send_keys(u"テスト課")
        self.find_element(By.ID,"SyozokuKaCodeLabel").click()
        self.find_element(By.ID,"SyozokuKaCodeLabel").send_keys("")
        self.find_element(By.ID,"SyozokuKaCodeLabel").send_keys("99301001102")

        #self.find_element(By.ID,"CmbGyoumu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbGyoumu"),"身体障害者手帳")
        self.find_element(By.ID,"CmbGyoumu").send_keys("身体障害者手帳")
        # time.sleep(2)

        #self.find_element(By.ID,"CmbProcess").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbProcess"),"申請資格管理")
        self.find_element(By.ID,"CmbProcess").send_keys("申請資格管理")
        # time.sleep(2)

        self.find_element(By.ID,"span_BtnKensaku").click()
        self.find_element(By.ID,"span_BtnTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC000-016-11", True)
        self.find_element(By.ID,"span_BtnShokiHyoji").click()
        self.save_screenshot_migrate(driver, "QAC000-016-13", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC000-016-15", True)
        #self.find_element(By.ID,"CmbShozokuKa").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbShozokuKa"),"テスト課")
        self.find_element(By.ID,"CmbShozokuKa").send_keys("テスト課")
        # time.sleep(2)
        self.find_element(By.ID,"span_BtnKensaku").click()
        self.save_screenshot_migrate(driver, "QAC000-016-17", True)
        self.find_element(By.ID,"span_BtnSet").click()
        self.save_screenshot_migrate(driver, "QAC000-016-19", True)

        #self.find_element(By.ID,"CmbGyoumu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbGyoumu"),"児童手当")
        self.find_element(By.ID,"CmbGyoumu").send_keys("児童手当")
        # time.sleep(2)

        #self.find_element(By.ID,"CmbProcess").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbProcess"),"申請資格管理")
        self.find_element(By.ID,"CmbProcess").send_keys("申請資格管理")
        # time.sleep(2)
        self.find_element(By.ID,"span_BtnKensaku").click()
        self.save_screenshot_migrate(driver, "QAC000-016-21", True)
        #self.find_element(By.ID,"TxtKikanStaYMD_All").click()
        #self.find_element(By.ID,"TxtKikanStaYMD_All").send_keys("")
        #self.find_element(By.ID,"TxtKikanStaYMD_All").send_keys("20210909")
        #self.find_element(By.ID,"BtnKikanStaYMD_All").click()
        #self.save_screenshot_migrate(driver, "QAC000-016-23", True)
        #self.find_element(By.ID,"TxtKikanEndYMD_All").click()
        #self.find_element(By.ID,"TxtKikanEndYMD_All").send_keys("")
        #self.find_element(By.ID,"TxtKikanEndYMD_All").send_keys("20220909")
        #self.find_element(By.ID,"span_BtnKikanEndYMD_All").click()
        #self.save_screenshot_migrate(driver, "QAC000-016-25", True)

        self.find_element(By.ID,"span_BtnTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC000-016-27", True)
        self.find_element(By.ID,"span_BtnShokiHyoji").click()
        self.save_screenshot_migrate(driver, "QAC000-016-29", True)

        #self.find_element(By.ID,"CmbGyoumu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbGyoumu"),"児童手当")
        self.find_element(By.ID,"CmbGyoumu").send_keys("児童手当")
        # time.sleep(2)

        #self.find_element(By.ID,"CmbProcess").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbProcess"),"申請資格管理")
        self.find_element(By.ID,"CmbProcess").send_keys("申請資格管理")
        # time.sleep(2)
        self.find_element(By.ID,"span_BtnKensaku").click()

        #self.find_element(By.ID,"CmbGyoumu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbGyoumu"),"特別児童扶養手当")
        self.find_element(By.ID,"CmbGyoumu").send_keys("特別児童扶養手当")
        # time.sleep(2)

        #self.find_element(By.ID,"CmbProcess").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbProcess"),"申請資格管理")
        self.find_element(By.ID,"CmbProcess").send_keys("申請資格管理")
        # time.sleep(2)
        self.find_element(By.ID,"span_BtnKensaku").click()

        #self.find_element(By.ID,"CmbGyoumu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbGyoumu"),"特別障害者手当")
        self.find_element(By.ID,"CmbGyoumu").send_keys("特別障害者手当")
        # time.sleep(2)

        #self.find_element(By.ID,"CmbProcess").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbProcess"),"申請資格管理")
        self.find_element(By.ID,"CmbProcess").send_keys("申請資格管理")
        # time.sleep(2)
        self.find_element(By.ID,"span_BtnKensaku").click()
        self.save_screenshot_migrate(driver, "QAC000-016-33", True)
        self.find_element(By.ID,"CmdNextPage").click()
        self.save_screenshot_migrate(driver, "QAC000-016-35", True)
        self.find_element(By.ID,"CmdBackPage").click()
        self.save_screenshot_migrate(driver, "QAC000-016-37", True)
        #self.find_element(By.ID,"PageCmb").click()
        #self.select_Option(driver,self.find_element(By.ID,"PageCmb")).select_by_visible_text("02")
        self.find_element(By.ID,"PageCmb").send_keys("02")
        self.find_element(By.ID,"CmdJumpPage").click()
        self.save_screenshot_migrate(driver, "QAC000-016-40", True)
        self.find_element(By.ID,"span_BtnShokiHyoji").click()
        #
        #self.find_element(By.ID,"CmbGyoumu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbGyoumu"),"児童手当")
        ## time.sleep(2)
        #
        #self.find_element(By.ID,"CmbProcess").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbProcess"),"申請資格管理")
        ## time.sleep(2)
        #self.find_element(By.ID,"span_BtnKensaku").click()
        #
        #self.find_element(By.ID,"CmbGyoumu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbGyoumu"),"特別児童扶養手当")
        ## time.sleep(2)
        #
        #self.find_element(By.ID,"CmbProcess").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbProcess"),"申請資格管理")
        ## time.sleep(2)
        #self.find_element(By.ID,"span_BtnKensaku").click()
        #
        #self.find_element(By.ID,"CmbGyoumu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbGyoumu"),"児童手当")
        ## time.sleep(2)
        #
        #self.find_element(By.ID,"CmbProcess").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbProcess"),"申請資格管理")
        ## time.sleep(2)
        #self.find_element(By.ID,"span_BtnKensaku").click()
        self.save_screenshot_migrate(driver, "QAC000-016-43", True)

        self.find_element(By.ID,"span_BtnSakujyo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC000-016-45", True)

        #self.find_element(By.ID,"span_BtnSyozokuDel").click()
        #
        #self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        #self.assertEqual(u"削除する所属はこの所属で間違いありませんか？　：　福祉部・テスト課", self.alert_ok())
        #self.save_screenshot_migrate(driver, "QAC000-016-47", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC000-016-49", True)
        self.find_element(By.ID,"span_BtnKensaku").click()
        self.save_screenshot_migrate(driver, "QAC000-016-51", True)
        self.find_element(By.ID,"span_BtnTantouAdd").click()
        self.save_screenshot_migrate(driver, "QAC000-016-53", True)
        self.find_element(By.ID,"TantousyaNameLabel").click()
        self.find_element(By.ID,"TantousyaNameLabel").send_keys("")
        self.find_element(By.ID,"TantousyaNameLabel").send_keys(u"検証担当者")
        self.find_element(By.ID,"TantousyaKanaLabel").click()
        self.find_element(By.ID,"TantousyaKanaLabel").send_keys("")
        self.find_element(By.ID,"TantousyaKanaLabel").send_keys(u"テストタントウシャ")
        #self.find_element(By.ID,"CmbSyokumei").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbSyokumei"),"担当者")
        self.find_element(By.ID,"CmbSyokumei").send_keys("担当者")
        self.find_element(By.ID,"TantousyaCodeLabel").click()
        self.find_element(By.ID,"TantousyaCodeLabel").send_keys("")
        self.find_element(By.ID,"TantousyaCodeLabel").send_keys("9502001")
        self.find_element(By.ID,"TantouPassword").click()
        self.find_element(By.ID,"TantouPassword").send_keys("")
        self.find_element(By.ID,"TantouPassword").send_keys("9502001")
        self.find_element(By.ID,"RdoYuukouKigenNasi").click()

        self.find_element(By.ID,"CmbGyoumu").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbGyoumu"),"児童手当")
        # time.sleep(2)

        self.find_element(By.ID,"span_BtnKensaku").click()

        self.find_element(By.ID,"CmbProcess").send_keys("申請資格管理")
        # time.sleep(2)
        self.find_element(By.ID,"span_BtnKensaku").click()

        self.find_element(By.ID,"span_BtnTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC000-016-57", True)
        self.find_element(By.ID,"span_BtnShokiHyoji").click()
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC000-016-60", True)
        self.find_element(By.ID,"span_BtnShokiHyoji").click()

        self.find_element(By.ID,"span_BtnKikanKakutei").click()

        self.find_element(By.ID,"CmbShozokuKu").send_keys("９９３０１")
        # time.sleep(2)
        self.find_element(By.ID,"span_BtnKensaku").click()

        self.find_element(By.ID,"CmbShozokuBu").send_keys("福祉部")
        # time.sleep(2)
        self.find_element(By.ID,"span_BtnKensaku").click()

        self.find_element(By.ID,"span_BtnHaneiNew").click()
        self.save_screenshot_migrate(driver, "QAC000-016-62", True)

        self.find_element(By.ID,"span_BtnTantouAdd").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC000-016-64", True)
        self.find_element(By.ID,"span_BtnHanei").click()
        self.save_screenshot_migrate(driver, "QAC000-016-66", True)

        self.find_element(By.ID,"span_ExecuteButton").click()
        self.assertEqual(u"処理を開始します。よろしいですか？", self.alert_ok())
        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC000-016-69", True)
        self.find_element(By.ID,"Sel1").click()
        self.save_screenshot_migrate(driver, "QAC000-016-71", True)
        self.find_element(By.ID,"span_BtnSakujyo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC000-016-73", True)

        #self.find_element(By.ID,"span_BtnHanei").click()
        #self.assertEqual(u"反映処理を行います。修正内容を失う場合がありますが宜しいですか？", self.alert_ok())
        #self.save_screenshot_migrate(driver, "QAC000-016-75", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC000-016-76", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC000-016-77", True)
