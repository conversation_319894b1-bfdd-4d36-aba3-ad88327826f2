import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAC000004(FukushiSiteTestCaseBase):
    """TESTQAC000004"""
    def test_case_qac000_004(self):
        """test_case_qac000_004"""
        #ここにから下に操作したコードをコピーして貼り付ける
        driver = None
        test_data = self.common_test_data
        self.do_login()
        self.find_element(By.ID,"CmdProcess20_1").click()
        self.save_screenshot_migrate(driver, "QAC000-004-01", True)
        self.find_element(By.ID,"CmdButton2").click()
        self.save_screenshot_migrate(driver, "QAC000-004-03", True)
        self.find_element(By.ID,"CmdUserCodeManage").click()
        self.save_screenshot_migrate(driver, "QAC000-004-05", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC000-004-07", True)

        #self.find_element(By.ID,"CmbUserCode").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbUserCode"),"********** : ユーザーコード管理")
        self.find_element(By.ID,"CmbUserCode").send_keys("********** : ユーザーコード管理")
        # time.sleep(2)
        self.find_element(By.ID,"TxtShiboriShort").click()
        self.find_element(By.ID,"TxtShiboriShort").send_keys("")
        self.find_element(By.ID,"TxtShiboriShort").send_keys("**********")

        #self.find_element(By.ID,"CmbJigyo").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbJigyo"),"業務共通")
        self.find_element(By.ID,"CmbJigyo").send_keys("業務共通")
        self.find_element(By.ID,"TxtCodeType").click()
        self.find_element(By.ID,"TxtCodeType").send_keys("")
        self.find_element(By.ID,"TxtCodeType").send_keys("0000000001")
        self.find_element(By.ID,"TxtCodeShort").click()
        self.find_element(By.ID,"TxtCodeShort").send_keys("")
        self.find_element(By.ID,"TxtCodeShort").send_keys(u"コード略称")
        self.find_element(By.ID,"TxtCodeName").click()
        self.find_element(By.ID,"TxtCodeName").send_keys("")
        self.find_element(By.ID,"TxtCodeName").send_keys(u"コード名称")
        self.find_element(By.ID,"span_CmdSearch").click()
        self.save_screenshot_migrate(driver, "QAC000-004-10", True)
        self.find_element(By.ID,"span_CmdClear").click()
        self.save_screenshot_migrate(driver, "QAC000-004-12", True)

        #self.find_element(By.ID,"CmbUserCode").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbUserCode"),"********** : ユーザーコード管理")
        self.find_element(By.ID,"CmbUserCode").send_keys("********** : ユーザーコード管理")
        self.find_element(By.ID,"TxtShiboriShort").click()
        self.find_element(By.ID,"TxtShiboriShort").send_keys("")
        self.find_element(By.ID,"TxtShiboriShort").send_keys("**********")

        #self.find_element(By.ID,"CmbJigyo").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbJigyo"),"児童手当")
        self.find_element(By.ID,"CmbJigyo").send_keys("児童手当")
        self.find_element(By.ID,"TxtCodeType").click()
        self.find_element(By.ID,"TxtCodeType").send_keys("")
        self.find_element(By.ID,"TxtCodeType").send_keys("0000000011")
        self.find_element(By.ID,"TxtCodeShort").click()
        self.find_element(By.ID,"TxtCodeShort").send_keys("")
        self.find_element(By.ID,"TxtCodeShort").send_keys(u"コード略称")
        self.find_element(By.ID,"TxtCodeName").click()
        self.find_element(By.ID,"TxtCodeName").send_keys("")
        self.find_element(By.ID,"TxtCodeName").send_keys(u"コード名称")
        self.find_element(By.ID,"CmdAdd").click()
        self.save_screenshot_migrate(driver, "QAC000-004-15", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"TxtRegDisplayOrder").click()
        self.find_element(By.ID,"TxtRegDisplayOrder").send_keys("")
        self.find_element(By.ID,"TxtRegDisplayOrder").send_keys("11")
        self.find_element(By.ID,"TxtRegEnableStartDate").click()
        self.find_element(By.ID,"TxtRegEnableStartDate").send_keys("")
        self.find_element(By.ID,"TxtRegEnableStartDate").send_keys("20210906")
        self.find_element(By.ID,"TxtRegEnableEndDate").click()
        self.find_element(By.ID,"TxtRegEnableEndDate").send_keys("")
        #self.find_element(By.ID,"TxtRegEnableEndDate").send_keys("20220906")
        self.find_element(By.ID,"TxtRegCodeShort").click()
        self.find_element(By.ID,"TxtRegCodeShort").send_keys("")
        self.find_element(By.ID,"TxtRegCodeShort").send_keys(u"コード略称")
        self.find_element(By.ID,"TxtRegCodeName").click()
        self.find_element(By.ID,"TxtRegCodeName").send_keys("")
        self.find_element(By.ID,"TxtRegCodeName").send_keys(u"コード名称")
        self.find_element(By.ID,"TxtRegValue1").click()
        self.find_element(By.ID,"TxtRegValue1").send_keys("")
        self.find_element(By.ID,"TxtRegValue1").send_keys("1")
        self.find_element(By.ID,"TxtRegValue2").click()
        self.find_element(By.ID,"TxtRegValue2").send_keys("")
        self.find_element(By.ID,"TxtRegValue2").send_keys("2")
        self.find_element(By.ID,"TxtRegValue3").click()
        self.find_element(By.ID,"TxtRegValue3").send_keys("")
        self.find_element(By.ID,"TxtRegValue3").send_keys("3")
        self.find_element(By.ID,"TxtRegValue4").click()
        self.find_element(By.ID,"TxtRegValue4").send_keys("")
        self.find_element(By.ID,"TxtRegValue4").send_keys("4")
        self.find_element(By.ID,"TxtRegValue5").click()
        self.find_element(By.ID,"TxtRegBikou").click()
        self.find_element(By.ID,"TxtRegBikou").send_keys("")
        self.find_element(By.ID,"TxtRegBikou").send_keys(u"備考")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        #self.find_element(By.ID,"CmbJigyo").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbJigyo")).select_by_visible_text("")
        
        #18行データ追加START
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("障害児福祉手当")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("児童扶養手当")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("子ども手当")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("重度心身障害者介護手当")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("難病患者福祉手当")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("重度心身障害者手当")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("原爆見舞金")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("児童育成手当")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("乳児養育手当")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("一宮市遺児手当")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("障害者医療")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("療育手帳")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("精神手帳")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("補装具費支給")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("日常生活用具")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("高齢日常生活用具")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("高齢者実態調査")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("生活保護情報")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.find_element(By.ID,"CmdAdd").click()
        self.find_element(By.ID,"CmbRegJigyo").send_keys("特別障害者手当")
        self.save_screenshot_migrate(driver, "QAC000-004-15-3", True)
        self.find_element(By.ID,"TxtRegCode").click()
        self.find_element(By.ID,"TxtRegCode").send_keys("")
        self.find_element(By.ID,"TxtRegCode").send_keys("0000000011")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        #18行データ追加END
        
        self.select_Option(driver,self.find_element(By.ID,"CmbJigyo"),"")
        self.find_element(By.ID,"span_CmdSearch").click()
        self.save_screenshot_migrate(driver, "QAC000-004-18", True)
        self.find_element(By.ID,"CmdNextPage").click()
        self.save_screenshot_migrate(driver, "QAC000-004-20", True)
        self.find_element(By.ID,"CmdBackPage").click()
        self.save_screenshot_migrate(driver, "QAC000-004-22", True)
        self.find_element(By.ID,"CmdSortJigyo").click()
        self.save_screenshot_migrate(driver, "QAC000-004-24", True)
        self.find_element(By.ID,"CmdSortCode").click()
        self.save_screenshot_migrate(driver, "QAC000-004-26", True)
        #self.find_element(By.ID,"CmbPage").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbPage")).select_by_visible_text("02")
        self.find_element(By.ID,"CmbPage").send_keys("02")
        self.find_element(By.ID,"CmdJumpPage").click()
        self.save_screenshot_migrate(driver, "QAC000-004-29", True)
        self.find_element(By.ID,"CmdSortCode").click()
        self.find_element(By.ID,"CmdNextPage").click()
        self.find_element(By.ID,"Sel11").click()
        self.save_screenshot_migrate(driver, "QAC000-004-33", True)
        self.find_element(By.ID,"span_CmdEdit").click()
        self.save_screenshot_migrate(driver, "QAC000-004-35", True)
        self.find_element(By.ID,"TxtRegCodeShort").click()
        self.find_element(By.ID,"TxtRegCodeShort").send_keys("")
        self.find_element(By.ID,"TxtRegCodeShort").send_keys(u"修正略称")
        self.find_element(By.ID,"span_CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC000-004-38", True)
        self.find_element(By.ID,"Sel11").click()
        self.find_element(By.ID,"span_CmdDelete").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC000-004-41", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC000-004-42", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC000-004-43", True)
