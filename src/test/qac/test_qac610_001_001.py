import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAC610001001(FukushiSiteTestCaseBase):
    """TESTQAC610001001"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAC610.sql", params=atena_list)
        super().setUp()
        
    def test_case_qac610_001_001(self):
        """test_case_qac610_001_001"""
        driver = None
        test_data = self.common_test_data
        
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=test_data.get("case_common_qac610_atena_code1"), gyoumu_code="QAC610")
        self.save_screenshot_migrate(driver, "QAC610-002-2-1", True)
        self.find_element(By.ID,"CmdShinsei").click()
        self.find_element(By.ID,"ShinseiShubetsuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"ShinseiShubetsuCmb"),"認定請求")
        #self.find_element(By.ID,"ShinseiShubetsuCmb").click()
        # time.sleep(2)

        self.find_element(By.ID,"CmdKakutei").click()
        self.find_element(By.ID,"ShinseiRiyuuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"ShinseiRiyuuCmb"),"新規")
        # time.sleep(2)
        #self.find_element(By.ID,"CmdKakutei").click()
        #self.find_element(By.ID,"ShinseiRiyuuCmb").click()
        self.find_element(By.ID,"CmdKakutei").click()
        #self.find_element(By.XPATH,"//img[@onclick=\"_wr_calendar(document.getElementById('_wr_calendar0'), document.getElementById('TxtShinseiYMD'), 'type=ymd&MIRAI=OK&fmt=1');\"]").click()
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | index=0 | ]]
        #self.find_element(By.ID,u"月").click()
        #self.select_Option(driver,self.find_element(By.ID,u"月")).select_by_visible_text("3")
        #self.find_element(By.ID,u"月").click()
        #self.find_element(By.XPATH,u"//td[@onclick=\"button_click('令和03年03月01日');\"]").click()
        self.find_element(By.ID,"TxtShinseiYMD").click()
        self.find_element(By.ID,"TxtShinseiYMD").click()
        self.find_element(By.ID,u"TxtShinseiYMD").send_keys("")
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("令和03年03月01日")
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | relative=parent | ]]
        #self.find_element(By.XPATH,"//div[@id='_wr_body_panel']/div[3]/table[3]/tbody/tr/td").click()
        #self.find_element(By.XPATH,"//img[@onclick=\"_wr_calendar_ym(document.getElementById('_wr_calendar2'), document.getElementById('TxtKaitei'), 'type=YM&MIRAI=OK&fmt=1&top=1.3em&left=-10em');\"]").click()
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | index=2 | ]]
        #self.find_element(By.XPATH,u"//td[@onclick=\"button_click('令和03年04月');\"]").click()
        self.find_element(By.ID,"TxtKaitei").click()
        self.find_element(By.ID,"TxtKaitei").click()
        self.find_element(By.ID,u"TxtKaitei").send_keys("")
        self.find_element(By.ID,"TxtKaitei").send_keys("令和03年04月")

        self.select_Option(driver,self.find_element(By.ID,"TantoShokatsukuCmb"),"第一区")
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | relative=parent | ]]
        self.find_element(By.ID,"CmdJidouTsuika").click()
        self.save_screenshot_migrate(driver, "QAC610-002-2-7", True)
        self.find_element(By.ID,"Sel2").click()
        #self.find_element(By.XPATH,"//img[@onclick=\"_wr_calendar(document.getElementById('_wr_calendar0'), document.getElementById('TxtGaitoYMD1'), 'type=ymd&MIRAI=OK&fmt=1');\"]").click()
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | index=0 | ]]
        #self.find_element(By.ID,u"月").click()
        #self.select_Option(driver,self.find_element(By.ID,u"月")).select_by_visible_text("4")
        #self.find_element(By.ID,u"月").click()
        #self.find_element(By.XPATH,u"//td[@onclick=\"button_click('令和03年04月01日');\"]").click()
        self.find_element(By.ID,"TxtGaitoYMD1").click()
        self.find_element(By.ID,"TxtGaitoYMD1").click()
        self.find_element(By.ID,u"TxtGaitoYMD1").send_keys("")
        self.find_element(By.ID,"TxtGaitoYMD1").send_keys("令和03年04月01日")
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | relative=parent | ]]
        self.find_element(By.ID,"CmbGaitoJiyu1").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbGaitoJiyu1"),"父母が死亡")
        self.find_element(By.ID,"CmbGaitoJiyu1").click()
        self.find_element(By.ID,"CmdInputCompleteButton").click()
        self.find_element(By.ID,"CmdGetsugakuKeisan").click()
        self.find_element(By.ID,"btnCommon3").click()
        self.save_screenshot_migrate(driver, "QAC610-002-2-15", True)
        self.find_element(By.ID,"CmdNo0").click()
        #self.save_screenshot("..//evidence/" + "QAC610-002-2-17" +".png")
        self.save_screenshot_migrate(driver, "QAC610-002-2-17", True)
        self.find_element(By.ID,"CmdInput").click()

        self.find_element(By.ID,"CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC610-002-2-21", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC610-002-2-23", True)
        self.find_element(By.ID,"btnCommon6").click()
        self.save_screenshot_migrate(driver, "QAC610-002-2-25", True)
        self.find_element(By.ID,u"口座情報_u_Tuika").click()
        #self.find_element(By.XPATH,u"//img[@onclick=\"_wr_calendar(document.getElementById('_wr_calendar0'), document.getElementById('口座情報_u_YukoKikanKaishi'), 'type=ymd&MIRAI=OK&FMT=1');\"]").click()
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | index=0 | ]]
        #self.find_element(By.ID,u"月").click()
        #self.select_Option(driver,self.find_element(By.ID,u"月")).select_by_visible_text("4")
        #self.find_element(By.ID,u"月").click()
        #self.find_element(By.XPATH,u"//td[@onclick=\"button_click('令和03年04月01日');\"]").click()
        self.find_element(By.ID,"口座情報_u_YukoKikanKaishi").click()
        self.find_element(By.ID,"口座情報_u_YukoKikanKaishi").click()
        self.find_element(By.ID,u"口座情報_u_YukoKikanKaishi").send_keys("")
        self.find_element(By.ID,"口座情報_u_YukoKikanKaishi").send_keys("令和03年04月01日")
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | relative=parent | ]]
        self.find_element(By.ID,u"口座情報_u_KinyuKikanCode").click()
        self.find_element(By.ID,u"口座情報_u_KinyuKikanCode").click()
        self.find_element(By.ID,u"口座情報_u_KinyuKikanCode").send_keys("")
        self.find_element(By.ID,u"口座情報_u_KinyuKikanCode").send_keys("0001")
        self.find_element(By.ID,u"口座情報_u_ShitenCode").click()
        self.find_element(By.ID,u"口座情報_u_ShitenCode").click()
        self.find_element(By.ID,u"口座情報_u_ShitenCode").send_keys("")
        self.find_element(By.ID,u"口座情報_u_ShitenCode").send_keys("001")
        self.find_element(By.ID,u"口座情報_u_KouzaBango").click()
        self.find_element(By.ID,u"口座情報_u_KouzaBango").click()
        self.find_element(By.ID,u"口座情報_u_KouzaBango").send_keys("")
        self.find_element(By.ID,u"口座情報_u_KouzaBango").send_keys("0035000")

        self.find_element(By.ID,u"口座情報_u_Touroku").click()
        self.assertEqual(u"登録します。よろしいですか？", self.alert_ok())
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC610-002-2-30", True)
        self.find_element(By.ID,"CmdGetsugakuKeisan").click()

        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.find_element(By.ID,"CmdKettei").click()
        self.save_screenshot_migrate(driver, "QAC610-002-2-34", True)
        #self.find_element(By.XPATH,"//img[@onclick=\"_wr_calendar(document.getElementById('_wr_calendar2'), document.getElementById('TxtKetteiYMD'), 'type=ymd&MIRAI=OK&fmt=1');\"]").click()
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | index=2 | ]]
        #self.find_element(By.ID,u"月").click()
        #self.select_Option(driver,self.find_element(By.ID,u"月")).select_by_visible_text("3")
        #self.find_element(By.ID,u"月").click()
        #self.find_element(By.XPATH,u"//td[@onclick=\"button_click('令和03年03月01日');\"]").click()
        self.find_element(By.ID,"TxtKetteiYMD").click()
        self.find_element(By.ID,"TxtKetteiYMD").click()
        self.find_element(By.ID,u"TxtKetteiYMD").send_keys("")
        self.find_element(By.ID,"TxtKetteiYMD").send_keys("令和03年03月01日")
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | relative=parent | ]]
        self.find_element(By.ID,"KetteiKekkaCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"KetteiKekkaCmb"),"決定")
        #self.find_element(By.ID,"KetteiKekkaCmb").click()
        time.sleep(1)


        self.find_element(By.ID,"TxtNinteiBango").click()
        self.find_element(By.ID,"TxtNinteiBango").click()
        self.find_element(By.ID,"TxtNinteiBango").send_keys("")
        self.find_element(By.ID,"TxtNinteiBango").send_keys("123456")
        self.find_element(By.ID,"CmdGetsugakuKeisan").click()
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC610-002-2-37", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC610-002-2-38", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC610-002-2-39", True)