import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class Test_QATF014(FukushiSiteTestCaseBase):
    """Test_QATF014"""

    def tearDown(self):
        self.exec_sqlfile("QATF011-QATF014_実行後スクリプト.sql")
        super().tearDown()

    def test_case_001(self):
        """test_case_001"""
        driver = None
        test_data = self.common_test_data
        
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=test_data.get("qatf014_atena_code"), gyoumu_code="QAT900")
        self.find_element(By.NAME, "img").click()
        self.find_element(By.ID, "btnCommon11").click()
        self.screen_shot("QATF014_1",caption="QATF014_画面初期化")

        self.find_element(By.ID, "CmbKofukenBunrui").click()
        dropdown = self.find_element(By.ID, "CmbKofukenBunrui")
        dropdown.find_element(By.XPATH, "//option[. = '交付内容１']").click()
        self.find_element(By.ID, "span_CmdKakutei").click()
        self.screen_shot("QATF014_2",caption="QATF014_確認ボタン押下")
        self.find_element(By.ID, "span_CmdKakuteiKaijo").click()
        self.screen_shot("QATF014_3",caption="QATF014_確定解除ボタン押下")

        self.find_element(By.ID, "CmbKofukenBunrui").click()
        dropdown = self.find_element(By.ID, "CmbKofukenBunrui")
        dropdown.find_element(By.XPATH, "//option[. = '交付内容１']").click()
        self.find_element(By.ID, "span_CmdKakutei").click()
        self.find_element(By.ID, "span_CmdShori2_1").click()
        self.find_element(By.ID, "span_CmdShusei").click()
        self.screen_shot("QATF014_4",caption="QATF014_修正ボタン押下")
        self.find_element(By.ID, "span_CmdTorikeshi").click()
        self.screen_shot("QATF014_5",caption="QATF014_処理取消ボタン押下")

        self.find_element(By.ID, "span_CmdShori2_1").click()
        self.find_element(By.ID, "span_CmdShusei").click()
        self.find_element(By.ID, "span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.screen_shot("QATF014_6",caption="QATF014_登録ボタン押下")
        self.find_element(By.ID, "GOBACK").click()
