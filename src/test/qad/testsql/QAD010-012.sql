/*****************【QAD010】の実行前に関連テーブルのデータを削除してから実行する*****************/

/*【QAD010-012, 実行前スクリプト】*/
/* ①前回のテスト実行時に使用したデータを削除します。*/
/* ②QAD助成実績パンチデータのレコードを作成します。*/

delete from WR$$JICHITAI_CODE$$QA..QAD助成実績パンチデータ  where 受給者番号 = '$$QAD010_ATENACODE12$$' and 業務コード='QAD010' 

set identity_INSERT WR$$JICHITAI_CODE$$QA..QAD助成実績パンチデータ off
USE WR$$JICHITAI_CODE$$QA
INSERT INTO [QAD助成実績パンチデータ]([業務コード],[データ区分],[請求年月],[申請書番号],[申請書番号枝番],[自治体コード],[福祉事務所コード],[支所コード],[受給者番号],[診療年月],[請求年月日],[初診日],[診療日数],[医療機関コード],[診療科目],[保険者番号],[入院区分],[診療区分],[医療費総額],[薬剤費],[高額療養費],[附加給付額],[自己負担額],[一部負担金],[食事代],[他法公費額],[他法番号],[助成額],[件数],[資格区分],[老人区分],[課税区分],[食事日数],[定額区分],[停止フラグ],[確認区分],[支払区分],[銀行コード],[支店コード],[預金種目],[口座番号],[口座名義人],[生年月日],[県外区分],[結精区分],[継続区分],[支給区分],[食事区分],[特記1],[特記2],[特記3],[特記4],[特記5],[数値1],[数値2],[数値3],[数値4],[数値5],[結果コード],[結果登録日],[ファイル識別ID],[処理年月日],[通番],[連番_予備1],[連番_予備2],[年月_予備1],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QAD010','1','201507','12345   ','0001','99301','00000','','$$QAD010_ATENACODE12$$','201506','20140131','01','1  ','1309900000','00','00000000','2','1 ','10000   ','0       ','00000000','00000000','0       ','00000000','0       ','0       ','00','00000000 ','00','0','0','0','0 ','0','0','6','1','0000','000','0','0000000','','        ','0','0','0','0','0','0         ','0000000000','0000000000','0000000000','0000000000','0000000000','0         ','0         ','0000000000','0000000000','1','20150520','0100000001','20140131',1,'          ','          ','000000','0','INES','INES',getdate(),getdate(),'00000000')
