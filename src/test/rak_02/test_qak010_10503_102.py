import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK01010503102(FukushiSiteTestCaseBase):
    """TESTQAK01010503102"""

    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("test_qak010_10503_102.sql", params=atena_list)
        super().setUp()

    def test_case_qak010_10503_102(self):
        """test_case_qak010_10503_102"""
        driver = None
        case_data = self.test_data[self.__class__.__name__]
        atenaCD = case_data.get("宛名番号", "")
        shobunShurui = case_data.get("処分種類", "")
        shobunYMD = case_data.get("処分年月日", "")
        bukken_TeishiJiyu = case_data.get("物件種類 ／執行停止事由", "")

        # ログイン
        self.do_login()

        # 後期高齢者医療　収納
        # →「収納情報管理」ボタン押下
        self.click_button_by_label("収納情報管理")

        # 「宛名番号」テキストボックス入力
        self.form_input_by_id(idstr="AtenaCD", value=atenaCD)

        # 「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("10503-102-06")

        # 「滞納処分」ボタン押下
        self.click_button_by_label("滞納処分")
        self.screen_shot("10503-102-08")

        # 「追加」ボタン押下
        self.click_button_by_label("追加")
        self.screen_shot("10503-102-10")

        # 「全て選択」ボタン押下
        self.click_button_by_label("全て選択")

        # 「滞納内訳一覧に追加」ボタン押下
        self.click_button_by_label("滞納内訳一覧へ追加")

        # 以下項目を入力
        # ・「処分種類」コンボボックス
        # ・「処分年月日」テキストボックス
        # ・「物件種類/執行停止事由」コンボボックス
        self.form_input_by_id(idstr="ShobunShuruiCmb", text=shobunShurui)
        self.form_input_by_id(idstr="TxtShobunYMD", value=shobunYMD)
        self.form_input_by_id(idstr="Bukken_TeishiJiyuCmb", text=bukken_TeishiJiyu)

         # 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        self.assert_message_area("登録しました。")
        self.screen_shot("10503-102-15")

        # 「戻る」ボタン押下
        self.return_click()
        self.screen_shot("10503-102-17")

        # 「戻る」ボタン押下
        self.return_click()
        self.screen_shot("10503-102-19")

        # 「戻る」ボタン押下
        self.return_click()

        # 「戻る」ボタン押下
        self.return_click()