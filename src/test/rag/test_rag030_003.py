import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys

class TestRAG030(FukushiSiteTestCaseBase):
    """TESTRAG030003"""

    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        case_data = self.test_data["case01"]
        atena_code = case_data.get("atena_code")
        params = {"DELETE_ATENA_CODE": atena_code}
        self.exec_sqlfile("RAG030.sql", params=params)
        super().setUp()    

    def test_case_rag030_003(self):
        """test_case_rag030_003"""

        # case01のセクションを取得
        case_data = self.test_data["case01"]
        case_data2 = self.test_data["case02"]
        # atena_codeの値を取得
        atena_code = case_data.get("atena_code")
        atena_code2 = case_data2.get("atena_code")

        # ログイン
        self.do_login()
        #資格申請管理をクリック
        self.click_by_id("CmdProcess1_1") 
        #住民コードを検索
        self.send_keys_by_name("AtenaCD", atena_code)
        self.click_by_id("Kensaku") 
        #自立支援医療(育成医療)を選択
        self.click_by_id("02:0000000030:QAG030") 
        self.screen_shot("RAGF001_003_001_初期表示", caption="RAGF001_003_001_初期表示")
        #申請内容入力をクリック
        self.click_by_id("CmdShinsei")
        self.screen_shot("RAGF001_003_002_申請内容入力", caption="RAGF001_003_002_申請内容入力")
        #初期表示をクリック
        self.click_by_id("span_CmdShoki")
        self.screen_shot("RAGF001_003_003_初期表示", caption="RAGF001_003_003_初期表示")
        #申請内容入力をクリック
        self.click_by_id("CmdShinsei")
        self.screen_shot("RAGF001_003_004_申請内容入力", caption="RAGF001_003_004_申請内容入力")

        #todo:加入医療保険情報取得ボタンを使用すること
        #届出保険情報をクリック
        self.click_by_id("btnCommon5")
        self.screen_shot("RAGF001_003_005_届出保険情報", caption="RAGF001_003_005_届出保険情報")
        #加入状況を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbKanyuJokyo1"),"保険加入")
        #保険者検索をクリック
        self.click_by_id("CmdHokenshaKensaku")
        #保険者を検索
        self.send_keys_by_name("txtKanaMeisho", "フクシ")
        self.click_by_id("BtnKensaku") 
        self.screen_shot("RAGF001_003_006_保険者検索", caption="RAGF001_003_006_保険者検索")
        #No1をクリック
        self.click_by_id("Sel1") 
        #扶養者区分を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbFuyousha"),"本人")
        #被保険者検索をクリック
        self.click_by_id("CmdHiHokenshaKensaku") 
        #No5をクリック
        self.screen_shot("RAGF001_003_007_世帯員検索", caption="RAGF001_003_007_世帯員検索")
        self.click_by_id("Sel1") 
        #記号を入力
        self.send_keys_by_name("TxtKigo", "１２３")
        #番号を入力
        self.send_keys_by_name("TxtBango", "４５６７")
        #個人番号を入力
        self.send_keys_by_name("TxtKojinShikiBango", "８")
        #資格取得日を入力
        self.send_keys_by_name("TxtShikakuShutokuYMD", "20230401")
        self.screen_shot("RAGF001_003_008_届出保険情報", caption="RAGF001_003_008_届出保険情報")
        #追加をクリック
        self.click_by_id("CmdTsuika") 
        #入力完了をクリック
        self.driver.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "0件の修正、1件の追加、0件の削除を行います。よろしいですか？")
        self.driver.switch_to.alert.accept()

        #申請事由を選択
        self.select_Option(self.driver,self.find_element(By.ID,"ShinseiShubetsuCmb"),"新規")
        #申請理由を選択
        self.select_Option(self.driver,self.find_element(By.ID,"ShinseiRiyuuCmb"),"新規申請")
        #確定をクリック
        self.click_by_id("CmdKakutei") 
        #申請日を入力
        self.send_keys_by_name("TxtShinseiYMD", "20230401")
        #管理場所を選択
        self.select_Option(self.driver,self.find_element(By.ID,"TantoShokatsukuCmb"),"第一区")
        #受付場所を選択
        #self.select_Option(self.driver,self.find_element(By.ID,"UketsukeBashoCmb"),"第一区")
        #担当場所を選択
        #self.select_Option(self.driver,self.find_element(By.ID,"TantoBashoCmb"),"第一区")
        #所得判定年度を選択
        self.select_Option(self.driver,self.find_element(By.ID,"ShotokuHanteiNendoCmb"),"令和04年")
        #交付方法を選択
        self.select_Option(self.driver,self.find_element(By.ID,"KofuHohoCmb"),"郵送")
        #添付書類（意見書）を入力
        self.form_input_by_id("TempShoruiChkBox")
        #添付書類（世帯の所得状況等確認書類）を入力
        self.form_input_by_id("TempShorui2ChkBox")
        #添付書類（個人番号確認書類）を入力
        self.form_input_by_id("TempShorui3ChkBox")
        self.screen_shot("RAGF001_003_009_申請内容入力", caption="RAGF001_003_009_申請内容入力")

        #支給認定基準世帯作成をクリック
        self.click_by_id("CmdHokenSetai")
        self.screen_shot("RAGF001_003_010_支給認定基準世帯情報", caption="RAGF001_003_010_支給認定基準世帯情報")
        #保険の種類を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbHokenKbn"),"国民健康保険")
        #追加をクリック
        self.click_by_id("CmdTsuika")
        #住民コードを検索
        self.send_keys_by_name("AtenaCD", atena_code2)
        self.click_by_id("Kensaku") 

        #todo:自動実行だと未申告エラーで使用できない・・
        #税情報取得をクリック
        #self.click_by_id("CmdZeiJyoho")

        #本人から見た続柄（本人）を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbHZoku_1"),"子")
        #被保険者・被扶養者区分コード（本人）を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbHiyosya_1"),"被保険者")
        #受給者との関係（本人）を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbJKankei_1"),"本人")
        #課税非課税区分（本人）を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbKazeiKbn_1"),"市町村民税課税")
        #住民税所得割額（本人）を入力
        self.send_keys_by_name("TxtShotokuW_1", "4")
        #住民税均等割額（本人）を入力
        self.send_keys_by_name("TxtKintoW_1", "3")
        #合計所得（本人）を入力
        self.send_keys_by_name("TxtGokeiShotoku_1", "1230000")
        #年金収入（本人）を入力
        self.send_keys_by_name("TxtShogaiNenkin_1", "0")
        #収入額（本人）を入力
        self.send_keys_by_name("TxtSyunyu_1", "1230000")

        #本人から見た続柄（世帯員）を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbHZoku_2"),"母")
        #被保険者・被扶養者区分コード（世帯員）を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbHiyosya_2"),"被保険者")
        #受給者との関係（世帯員）を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbJKankei_2"),"代表保護者")
        #課税非課税区分（世帯員）を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbKazeiKbn_2"),"市町村民税課税")
        #住民税所得割額（世帯員）を入力
        self.send_keys_by_name("TxtShotokuW_2", "4")
        #住民税均等割額（世帯員）を入力
        self.send_keys_by_name("TxtKintoW_2", "3")
        #合計所得（世帯員）を入力
        self.send_keys_by_name("TxtGokeiShotoku_2", "1230000")
        #年金収入（世帯員）を入力
        self.send_keys_by_name("TxtShogaiNenkin_2", "0")
        #収入額（世帯員）を入力
        self.send_keys_by_name("TxtSyunyu_2", "1230000")

        #収入計算をクリック
        self.click_by_id("CmdShunyuKeisan")

        #手当・障害年金等（本人）を入力
        self.send_keys_by_name("TxtTokubetsuShogaiTeate_1", "0")
        #公的年金等の種類（本人）を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbNenkinSyurui_1"),"障害厚生年金")
        #所得確定区分コード（本人）を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbShotokuKbn_1"),"確定")
        #手当・障害年金等（世帯員）を入力
        self.send_keys_by_name("TxtTokubetsuShogaiTeate_2", "0")
        #公的年金等の種類（世帯員）を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbNenkinSyurui_2"),"障害厚生年金")
        #所得確定区分コード（世帯員）を選択
        self.select_Option(self.driver,self.find_element(By.ID,"CmbShotokuKbn_2"),"確定")
        self.screen_shot("RAGF001_003_011_支給認定基準世帯情報", caption="RAGF001_003_011_支給認定基準世帯情報")

        #入力完了をクリック
        self.driver.find_element(By.ID, "CmdNKanryo").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()

        #世帯所得反映をクリック
        self.click_by_id("CmdSetaiShotokuHanei")
        #世所得区分計算をクリック
        self.click_by_id("CmdShotokuKeisan")
        #減免区分を選択
        self.select_Option(self.driver,self.find_element(By.ID,"GenmenKubunCmb"),"５，０００円")
        self.screen_shot("RAGF001_003_012_所得区分", caption="RAGF001_003_012_所得区分")

        #受診機関 追加をクリック
        self.click_by_id("CmdJyushinKikanTsuika")
        #医療機関検索をクリック
        self.click_by_id("CmdIryoKensaku_1")
        self.screen_shot("RAGF001_003_013_医療機関検索", caption="RAGF001_003_013_医療機関検索")
        #No1をクリック
        self.click_by_id("Sel1")
        #入通院区分を選択
        self.select_Option(self.driver,self.find_element(By.ID,"NyugaiKubunCmb_1"),"入院外")
        self.screen_shot("RAGF001_003_014_受診機関", caption="RAGF001_003_014_受診機関")
        #有効開始日を入力
        self.send_keys_by_name("TxtJushinYukokikanKaishi_1", "20230401")
        #有効終了日を入力
        self.send_keys_by_name("TxtJushinYukokikanShuryo_1", "20240331")

        #公費負担の対象となる障害を選択
        self.select_Option(self.driver,self.find_element(By.ID,"ShogaiCmb"),"視覚障害")
        #医療の方針を選択
        self.select_Option(self.driver,self.find_element(By.ID,"HoshinCmb"),"角膜移植")
        #特定疾病療養受給者証の有無を入力
        self.form_input_by_id("ChkShippei")
        #理学療法の有無を入力
        self.form_input_by_id("ChkRigakuRyohoUmu")
        #入院日数を入力
        self.send_keys_by_name("TxtNyuinNissu", "0")
        #通院日数を入力
        self.send_keys_by_name("TxtTsuinNissu", "1")
        #医療費概算額を入力
        self.send_keys_by_name("TxtIryohiGaisangaku", "10000")
        #補装具の有無を入力
        self.form_input_by_id("ChkHosoguUmu")
        #補装具名を入力
        self.send_keys_by_name("TxtHosoguMei", "補装具名テキスト")
        self.screen_shot("RAGF001_003_015_意見書内容", caption="RAGF001_003_015_意見書内容")
        #登録をクリック
        self.driver.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        self.screen_shot("RAGF001_003_016_申請登録", caption="RAGF001_003_016_申請登録")

        #決定内容入力をクリック
        self.driver.find_element(By.ID, "CmdKettei").click()
        #決定結果を選択
        self.select_Option(self.driver,self.find_element(By.ID,"KetteiKekkaCmb"),"決定")

        #有効期間をクリック
        self.click_by_id("CmdYukoKikan")
        #受給者証適用開始日を入力
        self.send_keys_by_name("TxtJukyushashoTekiyoYMD", "20230401")
        #交付日を入力
        self.send_keys_by_name("TxtKofuYMD", "20230401")
        #再交付日を入力
        self.send_keys_by_name("TxtSaiKofuYMD", "20230405")
        #備考を入力
        self.send_keys_by_name("TxtAreaBiko", "備考テキスト")
        self.screen_shot("RAGF001_003_017_決定内容", caption="RAGF001_003_017_決定内容")
        #登録をクリック
        self.driver.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        self.screen_shot("RAGF001_003_018_決定登録", caption="RAGF001_003_018_決定登録")

        #削除をクリック
        self.driver.find_element(By.ID, "CmdSakujo").click()
        self.assertEqual(self.driver.switch_to.alert.text, "削除します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        self.screen_shot("RAGF001_003_019_削除", caption="RAGF001_003_019_削除")