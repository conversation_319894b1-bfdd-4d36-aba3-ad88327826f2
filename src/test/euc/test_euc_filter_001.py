import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
from util.helper import WebDriverHandleHelper

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestEUCFilter001(FukushiSiteTestCaseBase):
    """フィルタ指定でのEUC実行確認"""
    
    def test_case_001(self):
        """フィルタ指定でのEUC実行確認"""
        self.do_login()
        self.transit_euc_std()

        self.screen_shot("EUC初画面")
        target_euc_name = "QAA020療育手帳_基本_01_療育手帳情報"
        # EUCのIFRAMEに制御を移す
        WebDriverHandleHelper.change_euc_driver(self.driver)
        
        error_count = 0
        fail_count = 0
        csv_zero_count = 0
        try:
            WebDriverHandleHelper.open_euc_by_name(self.driver, target_euc_name)
            WebDriverHandleHelper.click_max_col_warn_dialog_if_visible(self.driver)
            WebDriverHandleHelper.set_filter_rec(self.driver, item_name="市区町村コード",item_condition="次の値と等しい",item_value1="991011",item_value2="",item_and="かつ",s1=True,s2=False,e1=False,e2=False)
            WebDriverHandleHelper.set_filter_rec(self.driver, item_name="宛名番号",item_condition="次の値の間",item_value1="000000000000011",item_value2="000000000000013",item_and="または",s1=False,s2=False,e1=False,e2=True)
            WebDriverHandleHelper.set_filter_rec(self.driver, item_name="不足書類種類1",item_condition="次の値と等しい",item_value1="写真",item_value2="",item_and="",s1=False,s2=True,e1=True,e2=False)
            self.screen_shot(f"{target_euc_name}_FILTER_RESULT")
            # 検索ボタンクリック
            span_time_ms = WebDriverHandleHelper.euc_search_click(self.driver)
            self.logger.info(f"TestEUCFilter001-SEARCH-SPAN_MS: {target_euc_name} , {span_time_ms}")
            self.screen_shot(f"{target_euc_name}_SEARCH_RESULT")
        except Exception as e:
            error_count += 1
            self.logger.info(f"TestEUCFilter001-SEARCH-ERROR: {target_euc_name}")
            self.logger.info(str(e))
            self.screen_shot(f"{target_euc_name}_RESULT_ERROR")


        self.assertEqual(fail_count, 0)
        self.assertEqual(csv_zero_count, 0)
        self.assertEqual(error_count, 0)
