import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC080_170103(FukushiSiteTestCaseBase):
    """TestQAC080_170103"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 資格管理画面から、認定請求書の返戻・保留情報の登録が行えることを確認する
    def test_QAC080_170103(self):
        """返戻・保留情報登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 住民コード　入力
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 5 個人検索画面: 「検索」ボタン押下
        self.screen_shot("個人検索画面_5")

        # 6 該当者一覧画面: 表示

        # 7 該当者一覧画面: Noボタン押下
        self.click_button_by_label("1")

        # 8 受給状況参照画面: 表示
        self.screen_shot("受給状況参照画面_8")

        # 9 受給状況参照画面: 「児童手当」ボタン押下
        self.click_button_by_label("児童手当")

        # 10 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_10")

        # 11 児童手当資格管理: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 12 児童手当資格管理: 申請種別： 認定請求　　申請理由 ：出生　を入力
        self.form_input_by_id("ShinseiShubetsuCmb", text=case_data.get("shinsei_shubetsu", ""))
        self.form_input_by_id("ShinseiRiyuuCmb", text=case_data.get("shinsei_riyuu", ""))

        # 13 児童手当資格管理: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 14 児童手当資格管理: 申請年月日： 20231101　支給開始年月： 202312　を入力　受給者区分：空欄被用区分： '被用'　を選択
        self.form_input_by_id("TxtShinseiYMD", value=case_data.get("shinsei_ymd", ""))
        self.form_input_by_id("TxtKaitei", value=case_data.get("kaitei", ""))
        self.form_input_by_id("JyukyusyaKbnCmb", text=case_data.get("jyukyusya_kbn", ""))
        self.form_input_by_id("RdoHiyo", "1")

        # 15 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_15")

        # 受給者区分：施設 または　里親なら、№16へ
        # 16 児童手当資格管理: 「入所施設」ボタン押下
        self.click_button_by_label("入所施設")

        # 17 施設検索: 表示
        self.screen_shot("施設検索_17")

        # 18 施設検索: 施設名カナ：ｻﾄｵﾔ　を入力
        self.form_input_by_id("kanaMeisho", text=case_data.get("kana_meisho", ""))

        # 19 施設検索: 表示
        self.screen_shot("施設検索_19")

        # 20 施設検索: Noボタン押下
        self.click_button_by_label("1")

        # 受給者区分：指定医療機関　なら　№21へ
        # 21 児童手当資格管理: 表示
        # self.screen_shot("児童手当資格管理_21")

        # 22 児童手当資格管理: 「指定医療機関」ボタン押下
        # self.click_button_by_label("指定医療機関")

        # 23 医療機関検索: 表示
        # self.screen_shot("医療機関検索_23")

        # 24 医療機関検索: 医療機関名カナ：　を入力
        # NG 「医療機関検索」画面が見つかりませんでした。

        # 25 医療機関検索: 表示
        # self.screen_shot("医療機関検索_25")

        # 26 医療機関検索: Noボタン押下
        # self.click_button_by_label("1")

        # 27 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_27")

        # 28 児童手当資格管理: 「児童追加」ボタン押下
        self.click_button_by_label("児童追加")

        # 29 世帯員検索画面: 表示
        self.screen_shot("世帯員検索画面_29")

        # 30 世帯員検索画面: Noボタン押下
        self.click_button_by_label("1")

        # 31 支給対象児童入力: 表示
        self.screen_shot("支給対象児童入力_31")

        # 32 支給対象児童入力: 続柄：'子'要件開始年月：資格管理画面「支給開始年月」要件開始事由： '出生'　支給開始年月：資格管理画面「支給開始年月」支給開始事由： '出生'児童との関係：'父母'　を入力
        self.form_input_by_id("CmbZokugara", text=case_data.get("zokugara", ""))
        self.form_input_by_id("TxtSanteiGaitoYMD", value=case_data.get("santei_gait_ymd"))
        self.form_input_by_id("CmbSanteiGaitoJiyu", text=case_data.get("santei_gait"))
        self.form_input_by_id("TxtShikyuGaitoYMD", value=case_data.get("shikyu_gait_ymd"))
        self.form_input_by_id("CmbShikyuGaitoJiyu", text=case_data.get("shikyu_gait"))
        self.form_input_by_id("CmbJidoKankei", text=case_data.get("jido_kankei"))

        # 33 支給対象児童入力: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 34 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_34")

        # 35 児童手当資格管理: 「福祉世帯情報」ボタン押下
        self.click_button_by_label("福祉世帯情報")

        # 36 福祉世帯情報画面: 表示
        self.screen_shot("福祉世帯情報画面_36")

        # 37 福祉世帯情報画面: 受給者との関係：対象児童を選択
        self.form_input_by_id(idstr="JukyuCmb_1", text="対象児童")

        # 38 福祉世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 39 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_39")

        # 40 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_40")

        # 41 児童手当資格管理: 「提出書類管理」ボタン押下
        self.click_button_by_label("提出書類管理")

        # 42 提出書類管理画面: 表示
        self.screen_shot("提出書類管理画面_42")

        # 43 提出書類管理画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 44 提出書類管理画面: 年金加入証明 の未提出にチェックを付ける
        self.form_input_by_id("ChkTeisyutsu_1", case_data.get("chk_teisyutsu_1"))

        # 45 提出書類管理画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 46 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_46")

        # 47 児童手当資格管理: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 48 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_48")

        # 49 児童手当資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")

        # 50 児童手当資格管理: 表示
        # Assert: 「登録しました。」のメッセージのチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("児童手当資格管理_50")

        # 51 児童手当資格管理: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 52 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_52")

        # 53 児童手当資格管理: 決定年月日： '20231110'　決定結果 ： 返戻　または　保留　を入力
        # NG 各IDコントロール取得が出来ませんでした。

        # 54 児童手当資格管理: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 55 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_55")

        # 56 児童手当資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")

        # 57 児童手当資格管理: 表示
        # Assert: 「登録しました。」のメッセージのチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("児童手当資格管理_57")

        # 58 児童手当資格管理: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")

        # 59 帳票印刷画面: 表示
        # self.screen_shot("帳票印刷画面_59")

        # 60 帳票印刷画面: 返戻通知または保留通知を選択
        # NG 該当なフィルドはなさそうです。

        # 61 帳票印刷画面: 「印刷」ボタン押下
        # self.print_online_reports(case_name="ケース名", report_name=case_data.get("form_name_0", ""), hakkou_ymd=case_data.get("hakkou_ymd", ""))

        # 62 帳票印刷画面: 表示
        # Assert: 「プレビューしました。」のメッセージのチェック
        # self.assert_message_area("プレビューしました。")
        # self.screen_shot("帳票印刷画面_62")

        # 63 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下

        # 64 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_64")

        # 65 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 66 帳票印刷画面: 「戻る」ボタン押下
        # self.return_click()

        # 67 児童手当資格管理: 表示
