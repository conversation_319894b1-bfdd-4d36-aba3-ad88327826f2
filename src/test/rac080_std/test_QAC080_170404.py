import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC080_170404(FukushiSiteTestCaseBase):
    """TestQAC080_170404"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # ・資格管理画面から、変更届出の決定登録が行えることを確認する。・バッチ処理の一括決定処理で、変更届出の決定登録が行えることを確認する。
    def test_QAC080_170404(self):
        """変更情報の登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")

        # 3 メインメニュー画面: 「進捗管理」ボタン押下
        self.click_button_by_label("進捗管理")

        # 4 進捗管理対象者検索: 表示
        self.screen_shot("進捗管理対象者検索_4")

        # 5 進捗管理対象者検索: 業務：児童事業：児童手当　を選択
        self.form_input_by_id(idstr="Gyomu", text="児童")
        self.form_input_by_id(idstr="Jigyo", text="児童手当")

        # 6 進捗管理対象者検索: 「確定」ボタンを押下
        self.click_button_by_label("確定")

        # 7 進捗管理対象者検索: 表示
        self.screen_shot("進捗管理対象者検索_7")

        # 8 進捗管理対象者検索: 進捗状況(上段）：変更を入力
        self.form_input_by_id(idstr="CmbPreShinchoku", text="変更")

        # 9 進捗管理対象者検索: 「検索」ボタンを押下
        self.click_button_by_label("検索")

        # 10 進捗管理対象者一覧: 表示
        self.screen_shot("進捗管理対象者一覧_10")

        # 11 進捗管理対象者一覧: Noボタン押下
        # Test: No = 7 TODO: このテストは、No = 7 に対してのみ実行される
        self.click_button_by_label("7")

        # 12 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_12")

        # 13 児童手当資格管理: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 14 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_14")

        # 15 児童手当資格管理: 児童の№ボタン押下
        # Test: 児童の№ = 1 TODO: このテストは、児童の№ = 1 に対してのみ実行される
        self.click_button_by_label("1")

        # 16 支給対象児童入力: 表示
        self.screen_shot("支給対象児童入力_16")

        # 17 支給対象児童入力: 「戻る」ボタン押下
        self.return_click()

        # 18 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_18")

        # 19 児童手当資格管理: 「福祉世帯情報」ボタン押下
        self.click_button_by_label("福祉世帯情報")

        # 20 福祉世帯情報画面: 表示
        self.screen_shot("福祉世帯情報画面_20")

        # 21 福祉世帯情報画面: 「戻る」ボタン押下
        self.return_click()

        # 22 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_22")

        # 23 児童手当資格管理: 「口座情報」ボタン押下
        self.click_button_by_label("口座情報")

        # 24 口座情報画面: 表示
        self.screen_shot("口座情報画面_24")

        # 25 口座情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 26 口座情報画面: 「金融機関」ボタン押下
        self.get_kouza_info()

        # 27 金融機関検索画面: 表示
        self.screen_shot("金融機関検索画面_27")

        # 28 金融機関検索画面: 金融機関名：'みずほ'支店名：'本店'　を入力

        # 29 金融機関検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 30 金融機関検索画面: 表示
        self.screen_shot("金融機関検索画面_30")

        # 31 金融機関検索画面: Noボタン押下
        # Test No = 1 TODO
        self.click_button_by_label("1")

        # 32 口座情報画面: 表示
        self.screen_shot("口座情報画面_32")

        # 33 口座情報画面: 預金種別：普通口座番号：'1234567'　を入力
        self.form_input_by_id(idstr="口座情報_u_KouzaShubetu", text=case_data.get("口座情報_u_KouzaShubetu", ""))
        self.form_input_by_id(idstr="口座情報_u_KouzaBango", value=case_data.get("口座情報_u_KouzaBango", ""))

        # 34 口座情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 35 口座情報画面: 表示
        # Assert: 「登録しました。」のメッセージのチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("口座情報画面_35")

        # 36 口座情報画面: 「戻る」ボタン押下
        self.return_click()

        # 37 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_37")

        # 38 児童手当資格管理: 「住所管理」ボタン押下
        self.click_button_by_label("住所管理")

        # 39 住所管理画面: 表示
        self.screen_shot("住所管理画面_39")

        # 40 住所管理画面: 「戻る」ボタン押下
        self.return_click()

        # 41 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_41")

        # 42 児童手当資格管理: 「提出書類管理」ボタン押下
        self.click_button_by_label("提出書類管理")

        # 43 提出書類管理画面: 表示
        self.screen_shot("提出書類管理画面_43")

        # 44 提出書類管理画面: 「戻る」ボタン押下
        self.return_click()

        # 45 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_45")

        # 46 児童手当資格管理: 「連絡先管理」ボタン押下
        self.click_button_by_label("連絡先管理")

        # 47 連絡先管理: 表示
        self.screen_shot("連絡先管理_47")

        # 48 連絡先管理: 「戻る」ボタン押下
        self.return_click()

        # 49 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_49")

        # 50 児童手当資格管理: 「不現住情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("不現住情報")

        # 51 不現住情報画面: 表示
        self.screen_shot("不現住情報画面_51")

        # 52 不現住情報画面: 「戻る」ボタン押下
        self.return_click()

        # 53 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_53")

        # 54 児童手当資格管理: 決定年月日： '20231210'　決定結果 ： '決定'　決定理由：空欄を入力
        self.form_input_by_id(idstr="TxtKetteiYMD", value=case_data.get("TxtKetteiYMD", ""))
        self.form_input_by_id(idstr="KetteiRiyuCmb", text=case_data.get("KetteiRiyuCmb", ""))

        # 55 児童手当資格管理: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 56 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_56")

        # 57 児童手当資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 58 児童手当資格管理: 表示
        # Assert: 「登録しました。」のメッセージのチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("児童手当資格管理_58")

        # 59 児童手当資格管理: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 60 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_60")

        # 61 帳票印刷画面: 変更用宛名_オンラインを選択

        # 62 帳票印刷画面: 「印刷」ボタン押下
        self.print_online_reports(case_name="帳票印刷画面", report_name="変更用宛名_オンライン", hakkou_ymd="")

        # 63 帳票印刷画面: 表示
        # Assert: 「プレビューしました。」のメッセージのチェック
        self.screen_shot("帳票印刷画面_63")

        # 64 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下

        # 65 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_65")

        # 66 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 67 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 68 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_68")

        # 69 児童手当資格管理: 「戻る」ボタン押下
        self.return_click()

        # 70 進捗管理対象者一覧: 表示
        self.screen_shot("進捗管理対象者一覧_70")

        # 71 進捗管理対象者一覧: 「戻る」ボタン押下
        self.return_click()

        # 72 対象画面名: 操作内容
        # Assert: アサート内容
        self.screen_shot("対象画面名_72")

        # 73 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_73")

        # 74 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 75 個人検索画面: 表示
        self.screen_shot("個人検索画面_74")

        # 76 個人検索画面: 「住民コード」入力
        # self.screen_shot("個人検索画面_75")

        # 77 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 78 該当者一覧画面: 表示
        self.screen_shot("該当者一覧画面_78")

        # 79 該当者一覧画面: Noボタン押下
        # Test 児童の№ = 1 TODO
        # このステップは存在しません.　ご確認お願い致します。
        self.click_button_by_label("1")

        # 80 受給状況参照画面: 表示
        self.screen_shot("受給状況参照画面_80")

        # 81 受給状況参照画面:「児童手当」ボタン押下
        self.click_button_by_label("児童手当")

        # 82 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_82")

        # 83 児童手当資格管理:「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 84 児童手当資格管理: 申請種別：変更　　申請理由 ：その他　を入力
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="変更")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("ShinseiRiyuuCmb", ""))

        # 85 児童手当資格管理	「確定」ボタン押下
        self.click_button_by_label("確定")

        # 86 児童手当資格管理: "申請年月日： 20231020　改定開始年月： 202311　を入力　"
        self.form_input_by_id(idstr="TxtShinseiYMD", value=case_data.get("TxtShinseiYMD", ""))
        self.form_input_by_id(idstr="TxtKaitei", value=case_data.get("TxtKaitei", ""))

        # 87 児童手当資格管理	表示
        self.screen_shot("児童手当資格管理_87")

        # 88 児童手当資格管理:「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 89 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_89")

        # 90 児童手当資格管理:「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 91 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_91")

        # 92 児童手当資格管理:「戻る」ボタン押下
        self.return_click()

        # 93 受給状況参照画面:表示
        self.screen_shot("受給状況参照画面_93")

        # 94 受給状況参照画面:「戻る」ボタン押下
        self.return_click()

        # 95 個人検索画面:表示
        self.screen_shot("個人検索画面_95")

        # 96  個人検索画面: 住民コード　入力

        # 97  個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 98  該当者一覧画面: 表示
        self.screen_shot("該当者一覧画面_98")

        # 99  該当者一覧画面: Noボタン押下
        # Test 児童の№ = 1 TODO
        # このステップは存在しません.　ご確認お願い致します。
        self.click_button_by_label("1")

        # 100  受給状況参照画面: 表示
        self.screen_shot("受給状況参照画面_100")

        # 101  受給状況参照画面: 「児童手当」ボタン押下
        self.click_button_by_label("児童手当")

        # 102  児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_102")

        # 103  児童手当資格管理: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 104  児童手当資格管理: 申請種別：変更　　申請理由 ：その他　を入力
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="変更")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("ShinseiRiyuuCmb", ""))

        # 105  児童手当資格管理: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 106  児童手当資格管理: 申請年月日： 20231020　改定開始年月： 202311　を入力
        self.form_input_by_id(idstr="TxtShinseiYMD", value=case_data.get("TxtShinseiYMD", ""))
        self.form_input_by_id(idstr="TxtKaitei", value=case_data.get("TxtKaitei", ""))

        # 107  児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_107")

        # 108  児童手当資格管理: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 109  児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_108")

        # 110  児童手当資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 111  児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_111")

        # 112  児童手当資格管理: 「戻る」ボタン押下
        self.return_click()

        # 113  受給状況参照画面: 表示
        self.screen_shot("受給状況参照画面_113")

        # 114  受給状況参照画面: 「戻る」ボタン押下
        self.return_click()

        # 115  個人検索画面: 表示
        self.screen_shot("個人検索画面_115")

        # 116  個人検索画面: 住民コード　入力
        # 117  個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 118  該当者一覧画面: 表示
        self.screen_shot("該当者一覧画面_118")

        # 119  該当者一覧画面: Noボタン押下
        # Test 児童の№ = 1 TODO
        # このステップは存在しません.　ご確認お願い致します。
        self.click_button_by_label("1")

        # 120  受給状況参照画面: 表示
        self.screen_shot("受給状況参照画面_120")

        # 121  受給状況参照画面: 「児童手当」ボタン押下
        self.click_button_by_label("児童手当")

        # 122  児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_122")

        # 123  児童手当資格管理: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 124  児童手当資格管理: 申請種別：変更　　申請理由 ：その他　を入力
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="変更")
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("ShinseiRiyuuCmb", ""))

        # 125  児童手当資格管理: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 126  児童手当資格管理: 申請年月日： 20231020　改定開始年月： 202311　を入力
        self.form_input_by_id(idstr="TxtShinseiYMD", value=case_data.get("TxtShinseiYMD", ""))
        self.form_input_by_id(idstr="TxtKaitei", value=case_data.get("TxtKaitei", ""))

        # 127  児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_127")

        # 128  児童手当資格管理: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 129  児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_129")

        # 130  児童手当資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 131  児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_131")

        # 132  児童手当資格管理: 「所得情報」ボタン押下
        self.click_button_by_label("所得情報")

        # 133  税世帯情報画面: 表示
        self.screen_shot("児童手当資格管理_133")

        # 134  税世帯情報画面: 「戻る」ボタン押下
        self.return_click()

        # 135  児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_135")

        # 136  児童手当資格管理: 「戻る」ボタン押下
        self.return_click()

        # 137  受給状況参照画面: 表示
        self.screen_shot("児童手当資格管理_137")

        # 138  受給状況参照画面: 「戻る」ボタン押下
        self.return_click()

        # 139  個人検索画面: 表示
        self.screen_shot("児童手当資格管理_139")

        # 140  個人検索画面: 「戻る」ボタン押下
        self.return_click()

        # 142  メインメニュー画面: 表示
        self.screen_shot("児童手当資格管理_142")

        # 143  メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 144  バッチ起動画面: 表示
        self.screen_shot("児童手当資格管理_144")

        # 145  バッチ起動画面: 業務：児童 事業：児童手当 処理区分：日次処理 処理分類：一括決定処理
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="日次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="一括決定処理")

        # 146  バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_146")

        # 147  バッチ起動画面: 「一括決定処理」のNoボタン押下
        self.click_batch_job_button_by_label("一括決定処理")

        # 148  バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_146")

        # 149  バッチ起動画面: 開始申請日： 20231020　終了申請日： 20231020　決定年月日： 20231030　申請種別：変更 処理区分：1（更新あり)
        params = [
            {"title": "開始申請日：", "type": "text", "value": case_data.get("Kaishi_shinsei_bi", "")},
            {"title": "終了申請日：", "type": "text", "value": case_data.get("shuuryou_shinsei_bi", "")},
            {"title": "決定年月日：", "type": "text", "value": case_data.get("ketteiYMD", "")},
            {"title": "申請種別：", "type": "text", "value": "1"},  # 申請種別　０：全て　１：認　２：額　３：変　４：喪
            # {"title": "処理区分", "type": "text", "value": "1"}, # 無し
        ]
        self.set_job_params(params)

        # 150  バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_150")

        # 151  バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 152  ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_152")

        # 153  ジョブ実行履歴画面: 「検索」ボタン押下
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 154  ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_154")

        # 155  ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 156  ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_156")

        # 157  ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 158  ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_158")

        # 159  ジョブ帳票履歴画面: 「児童手当一括決定対象者一覧」のNoボタン押下
        # 160  ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # 161  帳票（PDF）: 表示
        # 162  帳票（PDF）: ×ボタン押下でPDFを閉じる
        # 163  ジョブ帳票履歴画面: 「特例給付一括決定対象者一覧」のNoボタン押下
        # 164  ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # 165  帳票（PDF）: 表示
        # 166  帳票（PDF）: ×ボタン押下でPDFを閉じる
        # 167  ジョブ帳票履歴画面: 「一括決定エラーリスト」のNoボタン押下
        # 168  ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # 169  帳票（PDF）: 表示
        # 170  帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 171  ジョブ帳票履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 172  メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_172")

        # 173  メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 174  個人検索画面: 表示
        self.screen_shot("個人検索画面_174")

        # 175  個人検索画面: 「住民コード」入力
        # 176  個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 177  受給状況参照画面: 表示
        self.screen_shot("受給状況参照画面_177")

        # 178  受給状況参照画面: 「児童手当」ボタン押下
        self.click_button_by_label("児童手当")

        # 179  児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_179")

        # 180  児童手当資格管理: 「戻る」ボタン押下
        self.return_click()

        # 181  受給状況参照画面: 「戻る」ボタン押下
        self.return_click()

        # 182  個人検索画面: 表示
        self.screen_shot("個人検索画面_182")

        # 183  個人検索画面: 「住民コード」入力
        # 184  個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 185  受給状況参照画面: 表示
        self.screen_shot("受給状況参照画面_185")

        # 186  受給状況参照画面: 「児童手当」ボタン押下
        self.click_button_by_label("児童手当")

        # 187  児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_187")

        # 188  児童手当資格管理: 「戻る」ボタン押下
        self.return_click()

        # 189  受給状況参照画面: 「戻る」ボタン押下
        self.return_click()

        # 190  個人検索画面: 表示
        self.screen_shot("児童手当資格管理_190")

        # 191  個人検索画面: 「住民コード」入力
        # 192  個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 193  受給状況参照画面: 表示
        self.screen_shot("受給状況参照画面_193")

        # 194  受給状況参照画面: 「児童手当」ボタン押下
        self.click_button_by_label("児童手当")

        # 195  児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_195")
