DELETE FROM WR$$JICHITAI_CODE$$QA..QAZ受給状況 WHERE 宛名コード = '$$ATENA_QAA020_SAIKOUHU$$' AND 業務コード = 'QAA020'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAZ福祉世帯 WHERE 本人宛名コード = '$$ATENA_QAA020_SAIKOUHU$$' AND 業務コード = 'QAA020'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA資格履歴 WHERE 宛名コード = '$$ATENA_QAA020_SAIKOUHU$$' AND 業務コード = 'QAA020'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA療育手帳資格内容 WHERE 宛名コード = '$$ATENA_QAA020_SAIKOUHU$$' AND 業務コード = 'QAA020'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA療育手帳判定記録 WHERE 宛名コード = '$$ATENA_QAA020_SAIKOUHU$$' AND 業務コード = 'QAA020'


--QAZ受給状況
INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ受給状況 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,資格取得日,資格喪失日,申請年月日,申請種別,申請理由,進達年月日1,進達判定年月日1,進達結果1,進達年月日2,進達判定年月日2,進達結果2,決定年月日,決定結果,決定理由,進捗状況,業務固有コード1,業務固有コード2,業務固有コード3,複数申請フラグ,職権フラグ,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA020',522,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA020_SAIKOUHU$$',N'20240522',N'99999999',N'20230401',4,1,N'20230401',N'00000000',0,N'00000000',N'00000000',0,N'00000000',0,0,N'0000300002',N'福祉県1030206',N'',N'',0,N'0',N'0',N'9501',N'9501','2024-05-22 15:29:37.233','2024-05-23 11:59:26.010',N'RAAF003');

--QAZ福祉世帯
INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ福祉世帯 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,本人宛名コード,福祉世帯員宛名コード,該当日,非該当日,本人から見た続柄,受給者との関係,汎用項目,同居別居コード,旧姓併記有無,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA020',521,0,N'$$JICHITAI_CODE$$',N'00000',N'',N'$$ATENA_QAA020_SAIKOUHU$$',N'$$ATENA_QAA020_SAIKOUHU_HOGOSHA$$',N'20240522',N'99999999',N'0000000006',N'0000000007',N'0000000000',N'0000000000',N'0',N'0',N'9501',N'9501','2024-05-22 15:29:37.200','2024-05-22 15:29:37.200',N'RAAF003'),
	 (N'QAA020',521,0,N'$$JICHITAI_CODE$$',N'00000',N'',N'$$ATENA_QAA020_SAIKOUHU$$',N'$$ATENA_QAA020_SAIKOUHU$$',N'20240522',N'99999999',N'0000000001',N'0000000001',N'0000000000',N'0000000000',N'0',N'0',N'9501',N'9501','2024-05-22 15:29:37.170','2024-05-22 15:29:37.170',N'RAAF003');

--QAA資格履歴
INSERT INTO WR$$JICHITAI_CODE$$QA..QAA資格履歴 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,申請年月日,申請種別,申請理由,申請内容入力日,進達年月日1,進達判定年月日1,進達結果1,進達内容入力日1,進達年月日2,進達判定年月日2,進達結果2,進達内容入力日2,決定年月日,決定結果,決定理由,決定内容入力日,業務固有コード1,業務固有コード2,業務固有コード3,職権フラグ,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA020',522,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA020_SAIKOUHU$$',N'20230401',4,1,N'20240522',N'20230401',N'00000000',0,N'20240523',N'00000000',N'00000000',0,N'00000000',N'20240522',0,0,N'00000000',N'福祉県1030206',N'',N'',N'0',N'0',N'9501',N'9501','2024-05-22 15:42:53.357','2024-05-23 11:59:25.980',N'RAAF003'),
	 (N'QAA020',521,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA020_SAIKOUHU$$',N'20240522',4,4,N'20240522',N'20240522',N'00000000',0,N'20240522',N'00000000',N'00000000',0,N'00000000',N'20240522',1,0,N'20240522',N'福祉県1030206',N'',N'',N'0',N'0',N'9501',N'9501','2024-05-22 15:29:37.123','2024-05-22 15:33:16.490',N'RAAF003');

--QAA療育手帳判定記録
INSERT INTO WR$$JICHITAI_CODE$$QA..[QAA療育手帳判定記録] ([自治体コード],[福祉事務所コード],[支所コード],[業務コード],[履歴番号],[履歴分類],[宛名コード],[判定依頼日],[判定年月日],[判定結果],[次回判定年月],[次回判定年月を定めない],[判定機関コード],[障害程度],[ケース番号],[療育判定],[知能指数],[知能指数コード],[判定基準],[診断書有無],[社会生活能力点数],[重症心身障害児判定],[非該当理由],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム]) VALUES
	(N'$$JICHITAI_CODE$$',N'99301',N'',N'QAA020',521,0,N'$$ATENA_QAA020_SAIKOUHU$$',N'00000000',N'20240522',N'0000000000',N'000000',N'0',N'0000000000',N'0000000000',N'',N'0000000000',N'',N'0000000000',N'0000000000',N'',N'',N'',N'0000000000',N'0',N'9501',N'9501','2024-05-22 15:32:29.763','2024-05-22 15:32:29.763',N'RAAF004 ');
	
