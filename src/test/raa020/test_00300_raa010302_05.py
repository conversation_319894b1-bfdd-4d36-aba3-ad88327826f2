import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TEST00300RAA01030205(FukushiSiteTestCaseBase):
    """TEST00300RAA01030205"""

    def test_00300_raa010302_05(self):
        """test_00300_raa010302_05"""

        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        shinsei_kaishi_ymd = case_data.get("shinsei_kaishi_ymd", "")
        shinsei_owari_ymd = case_data.get("shinsei_owari_ymd", "")
        shintatsu_ymd1 = case_data.get("shintatsu_ymd1", "")
        shintatsu_ymd_start = case_data.get("shintatsu_ymd_start", "")
        shintatsu_ymd_end = case_data.get("shintatsu_ymd_end", "")
        bunsyo_bangou = case_data.get("bunsyo_bangou", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")

        # ログイン
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA020")
        self.return_click()
        self.return_click()
        self.return_click()
        self.screen_shot("raa010302_05_6")

        self.click_button_by_label("進達処理")
        self.form_input_by_id(idstr= "Gyomu", text = "障害")
        self.form_input_by_id(idstr= "Jigyo", text = "療育手帳")
        self.click_button_by_label("確定")
        self.screen_shot("raa010302_05_10")

        self.form_input_by_id(idstr= "ShinseiKaishiYMD", value = shinsei_kaishi_ymd)
        self.form_input_by_id(idstr= "ShinseiOwariYMD", value = shinsei_owari_ymd)
        self.form_input_by_id(idstr= "ShinseiShubetsu", text = "再交付")
        self.click_button_by_label("検索")
        self.screen_shot("raa010302_05_12")

        self.form_input_by_id(idstr= "ShintatsuYMD1", value = shintatsu_ymd1)
        self.form_input_by_id(idstr= "ChkSentakuNo_1", value = "1")
        self.click_button_by_label("進達情報")
        self.screen_shot("raa010302_05_16")
        self.pdf_output_and_download(button_id="TourokuBtn", case_name="ケース名")
        self.assert_message_area("登録しました。")
        self.screen_shot("raa010302_05_18")

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        self.screen_shot("raa010302_05_24_「戻る」ボタン押下")
        self.return_click()
        self.screen_shot("raa010302_05_26_「戻る」ボタン押下")

        # 「バッチ起動」ボタン押下
        self.batch_kidou_click()
        self.save_screenshot_migrate(driver, "raa010302_05_28" , True)

        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="療育手帳")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="随時処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="進達一覧作成")
        self.save_screenshot_migrate(driver, "raa010302_05_29" , True)

        # 「療育手帳交付申請者一覧_進達_再出力処理」の行の数字ボタン押下
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name)
        self.save_screenshot_migrate(driver, "raa010302_05_31_「療育手帳交付申請者一覧_進達_再出力処理」選択" , True)
        params = [
            {"title": "進達年月日開始", "type": "text", "value": shintatsu_ymd_start},
            {"title": "進達年月日終了", "type": "text", "value": shintatsu_ymd_end},
            {"title": "文書番号", "type": "text", "value": bunsyo_bangou}
        ]
        self.set_job_params(params)
        self.save_screenshot_migrate(driver, "raa010302_05_32_パラメータ入力" , True)

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.save_screenshot_migrate(driver, "raa010302_05_35_「実行履歴」ボタン押下" , True)

        # 「検索」ボタン押下
        self.wait_job_finished(30,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.save_screenshot_migrate(driver, "raa010302_05_37_「検索」ボタン押下" , True)

        # 「帳票履歴」ボタン押下
        self.click_report_log()
        self.save_screenshot_migrate(driver, "raa010302_05-39_「帳票履歴」ボタン押下" , True)

        # 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="ケース名")
        self.save_screenshot_migrate(driver, "raa010302_05-41_「検索」ボタン押下" , True)

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる
        self.save_screenshot_migrate(driver, "raa010302_05-46" , True)
