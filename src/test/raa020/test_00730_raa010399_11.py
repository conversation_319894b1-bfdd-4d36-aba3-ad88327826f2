import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TEST00730RAA01039911(FukushiSiteTestCaseBase):
    """TEST00730RAA01039911"""

    def test_00730_raa010399_11(self):
        """test_00730_raa010399_11"""

        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        kijun_ymd = case_data.get("kijun_ymd", "")
        chocho_kubun = case_data.get("chocho_kubun", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        # ログイン
        self.do_login()
        self.batch_kidou_click()
        self.save_screenshot_migrate(driver, "010399-11-02" , True)

        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="療育手帳")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="統計処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="統計処理")
        self.save_screenshot_migrate(driver, "010399-11-07" , True)

        #「療育手帳町丁別集計表出力」の行の数字ボタン押下
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name)
        self.save_screenshot_migrate(driver, "010399-11-09" , True)
        params = [
            {"title": "基準年月日", "type": "text", "value": kijun_ymd},
            {"title": "町丁区分", "type": "text", "value": chocho_kubun}
        ]
        self.set_job_params(params)
        self.save_screenshot_migrate(driver, "010399-11-10" , True)

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        self.save_screenshot_migrate(driver, "010399-11-11" , True)

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.save_screenshot_migrate(driver, "010399-11-13" , True)

        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(30,20)
        # 状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.save_screenshot_migrate(driver, "010399-11-15" , True)

        # 「帳票履歴」ボタン押下
        self.click_report_log()
        self.save_screenshot_migrate(driver, "010399-11-17_「帳票履歴」ボタン押下" , True)

        # 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="ケース名")
        self.save_screenshot_migrate(driver, "010399-11-19_「検索」ボタン押下" , True)

        # 「戻る」ボタン押下
        self.return_click()

        # メインメニュー画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "010399-11-21" , True)
