import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TEST00220RAA01030122(FukushiSiteTestCaseBase):
    """TEST00220RAA01030122"""

    #ここに操作したコードをコピーして貼り付ける
    def test_00220_raa010301_22(self):
        """test_00220_raa010301_22"""
        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        kijun_ymd1 = case_data.get("kijun_ymd1", "")
        kijun_ymd2 = case_data.get("kijun_ymd2", "")
        bunsyo_bangou = case_data.get("bunsyo_bangou", "")
        sort = case_data.get("sort", "")
        
        # ログイン
        self.do_login()
        #1	メインメニュー画面	「バッチ起動」ボタン押下
        self.batch_kidou_click()
        #2	バッチ起動画面	表示
        self.screen_shot("raa010301-22-02")
        #3	バッチ起動画面	業務「障害」選択
        self.form_input_by_id(idstr= "GyomuSelect", text = "障害")
        #4	バッチ起動画面	事業「療育手帳」選択
        self.form_input_by_id(idstr= "JigyoSelect", text = "療育手帳")
        #5	バッチ起動画面	処理区分「随時処理」選択
        self.form_input_by_id(idstr= "ShoriKubunSelect", text = "随時処理")
        #6	バッチ起動画面	処理分類「帳票出力処理(一括印刷)」選択
        self.form_input_by_id(idstr= "ShoriBunruiSelect", text = "帳票出力処理（一括印刷）")
        #7	バッチ起動画面	表示
        self.screen_shot("raa010301-22-07")

        if not self.check_batch_job_exist(insatsu_tyouhyou_name):
            return

        # 8	バッチ起動画面	「療育手帳交付_再交付について出力処理」の行の数字ボタン押下
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name) 
        params = [
            {"title": "発行年月日", "type": "text", "value": hakkou_ymd},
            {"title": "文書番号", "type": "text", "value": bunsyo_bangou},
            {"title": "決定年月日開始", "type": "text", "value": kijun_ymd1},
            {"title": "決定年月日終了", "type": "text", "value": kijun_ymd2},
            {"title": "出力順序", "type": "select", "value": sort}
        ]
        self.set_job_params(params)
        #バッチ起動画面	表示
        self.screen_shot("raa010301-22-11")

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("raa010301-22-12")

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.screen_shot("raa010301-22-17")

        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(30,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("raa010301-22-19")
        
        # No.の「１」行の「ダウンロード」ボタンを押下
        self.click_by_id("DownLoad1")
        self.screen_shot("raa010301-22-21")

        # ダウンロード画面のNo.の「１」ボタンを押下
        self.get_job_output_files(case_name="ケース名")
        self.screen_shot("raa010301-22-23")

        # 「戻る」ボタン押下
        self.return_click()
        # 「帳票履歴」ボタン押下
        self.click_report_log()
        self.screen_shot("raa010301-22-24")

        # 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="ケース名")
        self.screen_shot("raa010301-22-25")

        # 帳票（PDF）表示
        # self.click_by_id("Sel1")
        # self.screen_shot("raa010301-22-26")
        # 「戻る」ボタン押下
        self.return_click()

        # メインメニュー画面表示(エビデンス取得)
        self.screen_shot("raa010301-22-27")

