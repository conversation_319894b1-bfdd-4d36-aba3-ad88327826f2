import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TEST00170RAA01030117(FukushiSiteTestCaseBase):
    """TEST00170RAA01030117"""
    
    def test_00170_raa010301_17(self):
        """test_00170_raa010301_17"""
        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        # 検査(予約)日
        kensa_ymd = case_data.get("kensa_ymd", "")
        # 検査(予約)時間
        kensa_hh = case_data.get("kensa_hh", "")
        kensa_mm = case_data.get("kensa_mm", "")
        # 判定機関
        hantei = case_data.get("hantei", "")
        # 検査機関
        kensa_kikan = case_data.get("kensa_kikan", "")
        # 医師診察日
        ishi_sinsatu_ymd = case_data.get("ishi_sinsatu_ymd", "")
        # 医師診察時間
        ishi_shinsatu_hh = case_data.get("ishi_shinsatu_hh", "")
        ishi_shinsatu_mm = case_data.get("ishi_shinsatu_mm", "")
        # 判定機関受付日
        hantei_kikan_ymd = case_data.get("hantei_kikan_ymd", "")
        # 判定機関結果送付日
        hantei_kikan_kekka_soufu_ymd = case_data.get("hantei_kikan_kekka_soufu_ymd", "")
        # 判定日
        hantei_ymd = case_data.get("hantei_ymd", "")
        # 障害程度
        shogai = case_data.get("shogai", "")
        # 再判定年月
        hantei_ym = case_data.get("hantei_ym", "")

        # 決定日
        kettei_ymd = case_data.get("kettei_ymd", "")
        # 決定理由
        kettei_riyu = case_data.get("kettei_riyu", "")
        # 手帳番号
        techo_kigo = case_data.get("techo_kigo", "")
        techo_bango = case_data.get("techo_bango", "")
        # 初回交付日
        kofu_ymd = case_data.get("kofu_ymd", "")
        # 再交付日
        saikofu_ymd = case_data.get("saikofu_ymd", "")
        # 次回判定機関
        jikai_hanei_kikan = case_data.get("jikai_hanei_kikan", "")
        # カード登録日
        card_touroku_ymd = case_data.get("card_touroku_ymd", "")
        # カード解除日
        card_kaijo_ymd = case_data.get("card_kaijo_ymd", "")
        # カード発行日
        card_hakko_ymd = case_data.get("card_hakko_ymd", "")
        # 手帳受領日
        techo_juryo_ymd = case_data.get("techo_juryo_ymd", "")
        # 通知発送日
        tsuchi_hasso_ymd = case_data.get("tsuchi_hasso_ymd", "")
        # 手帳引渡日
        techo_hkiwatashi_ymd = case_data.get("techo_hkiwatashi_ymd", "")
        # 旅客運賃割引
        techo_shubetsu = case_data.get("techo_shubetsu", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA020")
       
        #8	療育手帳資格管理画面	表示
        self.screen_shot("raa010301_17_8")
        #9	療育手帳資格管理画面	「判定記録」ボタン押下
        self.click_button_by_label("判定記録")
        #10	療育手帳判定記録画面	表示
        self.screen_shot("raa010301_17_10")
        #11	療育手帳資格管理画面	「追加」ボタン押下
        self.click_button_by_label("追加")
        #11  療育手帳判定記録画面  判定方法「来所」
        self.form_input_by_id(idstr= "CmbHanteiHouhou", text= "来所")
        #11  療育手帳判定記録画面  検査(予約)日「20230405」
        self.form_input_by_id(idstr= "TxtKensaYmd", value = kensa_ymd)
        #11  療育手帳判定記録画面  検査(予約)時間「12：00」
        self.form_input_by_id(idstr= "TxtKensaHh", value = kensa_hh)
        self.form_input_by_id(idstr= "TxtKensaMm", value = kensa_mm)
        #11  療育手帳判定記録画面  判定機関「知的障害者更生相談所(パラメータ指定)」選択
        self.form_input_by_id(idstr= "CmbHantei", text = hantei)
        #11  療育手帳判定記録画面  検査機関「福祉市立病院(パラメータ指定)」
        self.form_input_by_id(idstr= "CmbKensaKikan", text = kensa_kikan)
        #11  療育手帳判定記録画面  IQ「68」
        self.form_input_by_id(idstr= "TxtChinoShisu", value = "68")
        #11  療育手帳判定記録画面  精神疾患の有無「チェック有」
        self.form_input_by_id(idstr= "ChkSeisinsikkan", value = "1")
        #11  療育手帳判定記録画面  発達障害の有無「チェック有」
        self.form_input_by_id(idstr= "ChkHattatusyougai", value = "1")
        #11  療育手帳判定記録画面  精神・発達年齢「12歳4か月」
        self.form_input_by_id(idstr= "TxtSeisinHattatunenrei", value = "12歳4か月")
        #11  療育手帳判定記録画面  その他検査・程度内容「社会生活能力」
        self.form_input_by_id(idstr= "TxtSonotaKensaTeido", value = "社会生活能力")
        #11  療育手帳判定記録画面  総合判定(処遇方針の検討)の状況「著しい発達の変化が予想される」
        self.form_input_by_id(idstr= "TxtSougouHanteiJoukyou", value = "著しい発達の変化が予想される")
        #11  療育手帳判定記録画面  医師診察日「20230115」
        self.form_input_by_id(idstr= "TxtIshiSinsatuYmd", value = ishi_sinsatu_ymd)
        #11  療育手帳判定記録画面  医師診察時間「13：30」
        self.form_input_by_id(idstr= "TxtIshiShinsatuHh", value = ishi_shinsatu_hh)
        self.form_input_by_id(idstr= "TxtIshiShinsatuMm", value = ishi_shinsatu_mm)
        #11  療育手帳判定記録画面  医師名_氏「医師」
        self.form_input_by_id(idstr= "TxtIshimeiShi", value = "医師")
        #11  療育手帳判定記録画面  医師名_名「太郎」
        self.form_input_by_id(idstr= "TxtIshimeiMei", value = "太郎")
        #11  療育手帳判定記録画面  心理判定員_氏「判定員」
        self.form_input_by_id(idstr= "TxtShinriHanteiinShi", value = "判定員")
        #11  療育手帳判定記録画面  心理判定員_名「太郎」
        self.form_input_by_id(idstr= "TxtShinriHanteiinMei", value = "太郎")
        #11  療育手帳判定記録画面  判定機関受付日「20230116」
        self.form_input_by_id(idstr= "TxtHanteiKikanYmd", value = hantei_kikan_ymd)
        #11  療育手帳判定記録画面  判定機関結果送付日「20230117」
        self.form_input_by_id(idstr= "TxtHanteiKikanKekkaSoufuYmd", value = hantei_kikan_kekka_soufu_ymd)
        #11  療育手帳判定記録画面  判定日「20230401」入力
        self.form_input_by_id(idstr= "TxtHanteiYmd", value = hantei_ymd)
        #11  療育手帳判定記録画面  判定結果「決定」選択
        self.form_input_by_id(idstr= "CmbHanteiKekka", text = "決定")
        #11  療育手帳判定記録画面  判定理由詳細「障害の程度判定基準に該当しないため」入力
        self.form_input_by_id(idstr= "TxtHanteiRiyu", value = "障害の程度判定基準に該当しないため")
        #11  療育手帳判定記録画面  障害程度「1度(パラメータ指定)」
        self.form_input_by_id(idstr= "CmbShogai", text = shogai)
        #11  療育手帳判定記録画面  再判定年月「202512」
        self.form_input_by_id(idstr= "TxtHanteiYm", value = hantei_ym)
        #11	療育手帳判定記録画面	表示
        self.screen_shot("raa010301_17_11")
        #12	療育手帳判定記録画面	「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        #13	療育手帳判定記録画面	表示
        self.screen_shot("raa010301_17_13")
        #14	療育手帳判定記録画面	「戻る」ボタン押下
        self.return_click()
        #15	療育手帳資格管理画面	表示
        self.screen_shot("raa010301_17_15")
        #16	療育手帳資格管理画面	「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")
        #17   療育手帳資格管理画面   決定日「20230401」入力
        self.form_input_by_id(idstr= "TxtKetteiYMD", value = kettei_ymd)
        #17   療育手帳資格管理画面   決定結果「決定」選択
        self.form_input_by_id(idstr= "KetteiKekkaCmb", text = "決定")
        #17   療育手帳資格管理画面   決定理由「○○(パラメータ指定)」
        if kettei_riyu != "":
            self.form_input_by_id(idstr="KetteiRiyuCmb", text = kettei_riyu)
        #17   療育手帳資格管理画面   決定理由詳細「決定理由詳細テキスト」
        self.form_input_by_id(idstr= "TxtHanteiRiyu", value = "決定理由詳細テキスト")
        #17   療育手帳資格管理画面   決裁日「20230406」入力
        self.form_input_by_id(idstr= "TxtKessaiYMD", value = "20230406")
        #17   療育手帳資格管理画面   変更日「20230407」
        self.form_input_by_id(idstr= "TxtHenkouYMD", value = "20230407")
        #17   療育手帳資格管理画面   手帳再出力等の手続き有「チェック有」
        self.form_input_by_id(idstr= "TetsudukiAriChkBox", value = "1")
        #17   療育手帳資格管理画面   手帳再出力等の手続き処理済「チェック有」
        self.form_input_by_id(idstr= "TetsudukiShoriZumiChkBox", value = "1")
        #17   療育手帳資格管理画面    表示
        self.screen_shot("raa010301_17_17")
        #18	療育手帳資格管理画面	「最新の判定記録を反映」ボタン押下
        self.click_button_by_label("最新の判定記録を反映")
        self.assertEqual(u"最新の判定記録を決定内容にコピーします。よろしいですか", self.alert_ok())
        #19	療育手帳資格管理画面	表示
        self.screen_shot("raa010301_17_19")

        #20   療育手帳資格管理画面   手帳番号「(パラメータ指定)」入力
        self.form_input_by_id(idstr= "TechoKigoCmb", text = techo_kigo)        
        self.form_input_by_id(idstr= "TxtTechoBango", value = techo_bango)
        #20   療育手帳資格管理画面   初回交付日「20230401」入力
        self.form_input_by_id(idstr= "TxtKofuYMD", value = kofu_ymd)
        #20   療育手帳資格管理画面   再交付日「(空白)」
        self.form_input_by_id(idstr= "TxtKofuYMD", value = saikofu_ymd)
        self.click_button_by_label("指導記録")
        #20   療育手帳資格管理画面   旅客運賃割引
        if self.exist_item(item_type="select", item_id="TechoShubetsuCmb"):
            self.find_element(By.ID,"TechoShubetsuCmb").click()
            self.select_Option(driver,self.find_element(By.ID,"TechoShubetsuCmb"), techo_shubetsu)
            
        #20   療育手帳資格管理画面   初回交付場所「○○障害者更生相談所」
        self.form_input_by_id(idstr= "TxtShokaiKouhuBasho", value = "テスト区役所障害者更生相談所")
        #20   療育手帳資格管理画面   程度変更状況「B→A」
        self.form_input_by_id(idstr= "TeidoHenkoJoukyoCmb", text = "Ｂ→Ａ")
        #20   療育手帳資格管理画面   次回判定機関「知的障害者更生相談所(パラメータ指定)」
        self.form_input_by_id(idstr= "JikaiHaneiKikanCmb", text = jikai_hanei_kikan)
        #20   療育手帳資格管理画面   カード登録日「20230501」
        self.form_input_by_id(idstr= "TxtCardTourokuYMD", value = card_touroku_ymd)
        #20   療育手帳資格管理画面   カード解除日「20230502」
        self.form_input_by_id(idstr= "TxtCardKaijoYMD", value = card_kaijo_ymd)
        #20   療育手帳資格管理画面   カード発行日「20230503」
        self.form_input_by_id(idstr= "TxtCardHakkoYMD", value = card_hakko_ymd)
        #20   療育手帳資格管理画面   手帳受領日「20230504」入力
        self.form_input_by_id(idstr="TxtTechoJuryoYMD", value=techo_juryo_ymd)
        #20   療育手帳資格管理画面   通知発送日「20230505」
        self.form_input_by_id(idstr= "TxtTsuchiHassoYMD", value = tsuchi_hasso_ymd)
        #20   療育手帳資格管理画面   手帳引渡日「20230506」
        self.form_input_by_id(idstr= "TxtTechoHkiwatashiYMD", value = techo_hkiwatashi_ymd)
        #20   療育手帳資格管理画面   NHK受信料減免有無「チェック有」
        self.form_input_by_id(idstr= "NHKJushinMengenChkBox", value = "1")
        #20   療育手帳資格管理画面   NHK受信料減免お客様番号「123-4567-890」    
        self.form_input_by_id(idstr= "TxtNHKJushinMengenDenwaBango", value = "123-4567-890")
        #20   療育手帳資格管理画面   有料道路減免有無「チェック有」
        self.form_input_by_id(idstr= "YuryoDoroGenmenChkBox", value = "1")
        #20   療育手帳資格管理画面   備考「備考欄テキスト８９０」
        self.form_input_by_id(idstr= "Txtbiko", value = "備考欄テキスト８９０")
        #21	療育手帳資格管理画面	表示
        self.screen_shot("raa010301_17_21")
        #22	療育手帳資格管理画面	「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        #23	療育手帳資格管理画面	表示
        self.screen_shot("raa010301_17_23")




