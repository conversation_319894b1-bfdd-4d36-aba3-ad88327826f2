import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
class TESTRAA020025(FukushiSiteTestCaseBase):
    """TESTRAA020025"""

    def test_case_raa020_025(self):
        """test_case_raa020_025"""
        driver = None
        test_data = self.common_test_data
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=test_data.get("case_common_raa020_atena_code1"), gyoumu_code="QAA020")
        self.save_screenshot_migrate(driver, "RAA025-001-9-2", True)

        self.find_element(By.ID,"CmdInsatsu").click()
        self.save_screenshot_migrate(driver, "RAA025-001-9-4", True)

        self.find_element(By.ID,"印刷コントロール部_UZABC001_HtmlPrinterName").click()
        self.select_Option(driver,self.find_element(By.ID,"印刷コントロール部_UZABC001_HtmlPrinterName"),"Adobe PDFテスト")
        self.find_element(By.ID,"印刷コントロール部_PrintSelect").click()
        self.select_Option(driver,self.find_element(By.ID,"印刷コントロール部_PrintSelect"),"プレビュー")  

        #療育手帳
        self.find_element(By.ID,"insatsuChk_4").click()        
        self.exec_online_print()
        self.save_screenshot_migrate(driver, "RAA025-001-9-11", True)

        self.find_element(By.ID,"GOBACK").click()
   
        #改ページデータ作成
        self.find_element(By.ID,"CmdShinsei").click()
        self.find_element(By.ID,"ShinseiShubetsuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"ShinseiShubetsuCmb"),"更新")
        self.find_element(By.ID,"ShinseiRiyuuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"ShinseiRiyuuCmb"),"再判定")
        self.find_element(By.ID,"CmdKakutei").click()

        self.find_element(By.ID,"TxtShinseiYMD").send_keys("20210808")
        if self.exist_item(item_type="select", item_id="TantoShokatsukuCmb"):
            self.select_Option(driver,self.find_element(By.ID,"TantoShokatsukuCmb"),"第一区")
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())

        dropdown = self.driver.find_element(By.ID, "履歴選択部_QAZC011_CmbPage")
        dropdown.find_element(By.XPATH, "//option[. = '02']").click()
        self.driver.find_element(By.ID, "履歴選択部_QAZC011_CmdJumpPage").click()
        self.save_screenshot_migrate(driver, "RAA025-001-9-22", True)
        self.driver.find_element(By.ID, "履歴選択部_QAZC011_CmdBackPage").click()
        self.save_screenshot_migrate(driver, "RAA005-001-9-24", True)
        self.driver.find_element(By.ID, "履歴選択部_QAZC011_CmdNextPage").click()
        self.save_screenshot_migrate(driver, "RAA005-001-9-26", True)
        #改ページデータ削除
        self.driver.find_element(By.ID, "履歴選択部_QAZC011_CmdBackPage").click() 
        self.find_element(By.ID,"CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.find_element(By.ID,"CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())        
