import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TEST00100RAA01030110(FukushiSiteTestCaseBase):
    """TEST00100RAA01030110"""

    #ここに操作したコードをコピーして貼り付ける
    def test_00100_raa010301_10(self):
        """test_00100_raa010301_10"""
        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        # 帳票名
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        # 発行年月日
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        # 決定年月日開始(基準年月日1)
        kijun_ymd1 = case_data.get("kijun_ymd1", "")
        # 決定年月日終了(基準年月日2)
        kijun_ymd2 = case_data.get("kijun_ymd2", "")
        # 出力順序
        output_order = case_data.get("output_order", "")
        #文書番号
        bunsyo_bangou = case_data.get("bunsyo_bangou", "")

        # ログイン
        self.do_login()
        #1	メインメニュー画面	「バッチ起動」ボタン押下
        self.batch_kidou_click()
        #2	バッチ起動画面	表示
        self.screen_shot("raa010301-10-02")
        #3	バッチ起動画面	業務「障害」選択
        self.form_input_by_id( "GyomuSelect", text = "障害")
        #4	バッチ起動画面	事業「療育手帳」選択
        self.form_input_by_id( "JigyoSelect", text = "療育手帳")
        #5	バッチ起動画面	処理区分「随時処理」選択
        self.form_input_by_id( "ShoriKubunSelect", text = "随時処理")
        #6	バッチ起動画面	処理分類「帳票出力処理(一括印刷)」選択
        self.form_input_by_id( "ShoriBunruiSelect", text = "帳票出力処理（一括印刷）")
        #7	バッチ起動画面	表示
        self.screen_shot("raa010301-10-07")
        
        if not self.check_batch_job_exist(insatsu_tyouhyou_name):
            return

        #療育手帳交付_再交付について_郵送_出力処理
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name) 
        #9	バッチ起動画面	表示
        self.screen_shot("raa010301-10-09")
        params = [
            {"title": "発行年月日", "type": "text", "value": hakkou_ymd},
            {"title": "文書番号", "type": "text", "value": bunsyo_bangou},
            {"title": "決定年月日開始", "type": "text", "value": kijun_ymd1},
            {"title": "決定年月日終了", "type": "text", "value": kijun_ymd2},
            {"title": "出力順序", "type": "select", "value": output_order}
        ]
        self.set_job_params(params)

        #11	バッチ起動画面	表示
        self.screen_shot("raa010301-10-11")
        #12	バッチ起動画面	「処理開始」ボタン押下12	バッチ起動画面	「処理開始」ボタン押下
        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)
        #13	バッチ起動画面	「ファイルを開く(O)」ボタンを押下
        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        #14	バッチ起動画面	表示
        #15	バッチ起動画面	×ボタン押下でPDFを閉じる
        self.screen_shot("raa010301-10-12")
        #16	バッチ起動画面	「実行履歴」ボタン押下
        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        #17	ジョブ実行履歴画面	表示
        self.screen_shot("raa010301-10-17")

        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(30,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        #18	ジョブ実行履歴画面	「検索」ボタン押下
        #19	ジョブ実行履歴画面	表示
        self.screen_shot("raa010301-10-19")
        
        #20	ジョブ実行履歴画面	No.の「１」行の「ダウンロード」ボタンを押下
        # No.の「１」行の「ダウンロード」ボタンを押下
        self.click_by_id("DownLoad1")
        #21	ダウンロード画面	表示
        self.screen_shot("raa010301-10-21")
        #22	ダウンロード画面	No.の「１」ボタンを押下
        # ダウンロード画面のNo.の「１」ボタンを押下
        self.get_job_output_files(case_name="ケース名")
        #23	ダウンロード画面	表示
        self.screen_shot("raa010301-10-23")
        #24	ダウンロード画面	「ファイルを開く(O)」ボタンを押下   
        #25	ダウンロード画面	×ボタン押下でCSVファイルを閉じる
        #26	ジョブ実行履歴画面	「戻る」ボタン押下
        # 「戻る」ボタン押下
        self.return_click()
        #26	ジョブ実行履歴画面	「戻る」ボタン押下
        # 「戻る」ボタン押下
        self.return_click()
        #27	メインメニュー画面	表示
        # メインメニュー画面表示(エビデンス取得)
        self.screen_shot("raa010301-10-27")

