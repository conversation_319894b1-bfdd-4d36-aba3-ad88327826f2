import time
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TEST00050RAA01030105(FukushiSiteTestCaseBase):
    """TEST00050RAA01030105"""

    def test_00050_raa010301_05(self):
        """test_00050_raa010301_05"""

        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        kensa_ymd = case_data.get("kensa_ymd", "")
        kensa_hh = case_data.get("kensa_hh", "")
        kensa_mm = case_data.get("kensa_mm", "")
        hantei = case_data.get("hantei", "")
        ishisinsatu_ymd = case_data.get("ishisinsatu_ymd", "")
        ishishinsatu_hh = case_data.get("ishishinsatu_hh", "")
        ishishinsatu_mm = case_data.get("ishishinsatu_mm", "")
        hantei_kikan_ymd = case_data.get("hantei_kikan_ymd", "")
        hantei_kikan_kekka_soufu_ymd = case_data.get("hantei_kikan_kekka_soufu_ymd", "")
        hantei_ymd = case_data.get("hantei_ymd", "")
        hantei_ym = case_data.get("hantei_ym", "")
        shogai = case_data.get("shogai", "")
        kettei_ymd = case_data.get("kettei_ymd", "")

        #ログイン        
        # #7	受給状況画面	「療育手帳」ボタン押下
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA020")
        
        #8	療育手帳資格管理画面	表示
        self.screen_shot( "raa010301-05-08")
        
        #9	療育手帳資格管理画面	「判定記録」ボタン押下
        self.click_button_by_label("判定記録")
        #10	療育手帳判定記録画面	表示
        self.screen_shot( "raa010301-05-10")
        
        #11	療育手帳資格管理画面	「追加」ボタン押下
        self.click_button_by_label("追加")
        #12    療育手帳判定記録画面    判定方法「来所」
        self.form_input_by_id( "CmbHanteiHouhou", text = "来所")
        
        #12    療育手帳判定記録画面    検査(予約)日「20230405」
        self.form_input_by_id( "TxtKensaYmd", value = kensa_ymd)
        
        #12    療育手帳判定記録画面    検査(予約)時間「12：00」
        self.form_input_by_id( "TxtKensaHh", value = kensa_hh)
        self.form_input_by_id( "TxtKensaMm", value = kensa_mm)
        
        #12    療育手帳判定記録画面    判定機関「知的障害者更生相談所(パラメータ指定)」選択
        self.form_input_by_id( "CmbHantei", text = hantei)
        
        #12    療育手帳判定記録画面    検査機関「福祉市立病院(パラメータ指定)」
        self.form_input_by_id( "CmbKensaKikan", text = "福祉市立病院")
        
        #12    療育手帳判定記録画面    IQ「68」
        self.form_input_by_id( "TxtChinoShisu", value = "68")
        
        #12    療育手帳判定記録画面    精神疾患の有無「チェック有」
        self.form_input_by_id( "ChkSeisinsikkan", value= "1")
        
        #12    療育手帳判定記録画面    発達障害の有無「チェック有」
        self.form_input_by_id( "ChkHattatusyougai", value= "1")
        
        #12    療育手帳判定記録画面    精神・発達年齢「12歳4か月」
        self.form_input_by_id( "TxtSeisinHattatunenrei", value = "12歳4か月")
        
        #12    療育手帳判定記録画面    その他検査・程度内容「社会生活能力」
        self.form_input_by_id( "TxtSonotaKensaTeido", value = "社会生活能力")
        
        #12    療育手帳判定記録画面    総合判定(処遇方針の検討)の状況「著しい発達の変化が予想される」
        self.form_input_by_id( "TxtSougouHanteiJoukyou", value = "著しい発達の変化が予想される")
        
        #12    療育手帳判定記録画面    医師診察日「20230115」
        self.form_input_by_id( "TxtIshiSinsatuYmd", value = ishisinsatu_ymd)
        
        #12    療育手帳判定記録画面    医師診察時間「13：30」
        self.form_input_by_id( "TxtIshiShinsatuHh", value = ishishinsatu_hh)
        self.form_input_by_id( "TxtIshiShinsatuMm", value = ishishinsatu_mm)
        
        #12    療育手帳判定記録画面    医師名_氏「医師」
        self.form_input_by_id( "TxtIshimeiShi", value = "医師")
        
        #12    療育手帳判定記録画面    医師名_名「太郎」
        self.form_input_by_id( "TxtIshimeiMei", value = "太郎")
        
        #12    療育手帳判定記録画面    心理判定員_氏「判定員」
        self.form_input_by_id( "TxtShinriHanteiinShi", value = "判定員")
        
        #12    療育手帳判定記録画面    心理判定員_名「太郎」
        self.form_input_by_id( "TxtShinriHanteiinMei", value = "太郎")
        
        #12    療育手帳判定記録画面    判定機関受付日「20230116」
        self.form_input_by_id( "TxtHanteiKikanYmd", value = hantei_kikan_ymd)
        
        #12    療育手帳判定記録画面    判定機関結果送付日「20230117」
        self.form_input_by_id( "TxtHanteiKikanKekkaSoufuYmd", value = hantei_kikan_kekka_soufu_ymd)
        
        #12    療育手帳判定記録画面    判定日「20230401」入力
        self.form_input_by_id( "TxtHanteiYmd", value = hantei_ymd)
        
        #12    療育手帳判定記録画面    判定理由詳細「障害の程度判定基準に該当しないため」入力
        self.form_input_by_id( "TxtHanteiRiyu", value = "障害の程度判定基準に該当しないため")
        
        #12    療育手帳判定記録画面    障害程度「1度(パラメータ指定)」  
        self.form_input_by_id( "CmbShogai", text = shogai)

        #12    療育手帳判定記録画面    再判定年月「202512」
        self.form_input_by_id( "TxtHanteiYm", value = hantei_ym)
        
        #12    療育手帳判定記録画面	判定結果「却下」選択
        self.form_input_by_id( "CmbHanteiKekka", text = "決定")
        
        #12    療育手帳判定記録画面	表示
        self.screen_shot( "raa010301-05-12")
        
        #13	療育手帳判定記録画面	「登録」ボタン押下	
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        
        #14	療育手帳判定記録画面	表示
        self.screen_shot( "raa010301-05-14")
        
        #15	療育手帳判定記録画面	「修正」ボタン押下
        self.click_button_by_label("修正")
        
        #16	療育手帳判定記録画面	判定結果「却下」選択
        self.form_input_by_id( "CmbHanteiKekka", text = "却下")
        self.form_input_by_id( "TxtHanteiRiyu", value = "test")
        
        #17	療育手帳判定記録画面	「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        
        #18	療育手帳資格管理画面	表示
        self.screen_shot( "raa010301-05-18")
        
        #19	療育手帳判定記録画面	「戻る」ボタン押下
        self.return_click()
        
        #20	療育手帳資格管理画面	表示
        self.screen_shot( "raa010301-05-20")

        #21	療育手帳資格管理画面	「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")
        
        #22	療育手帳資格管理画面	決定日「20230401」入力
        self.form_input_by_id( "TxtKetteiYMD", value = kettei_ymd)
        
        #22	療育手帳資格管理画面	決定結果「却下」選択
        self.form_input_by_id( "KetteiKekkaCmb", text = "却下")
        self.form_input_by_id( "TxtHanteiRiyu", value = "test")
        self.screen_shot( "raa010301-05-22")
        
        #23	療育手帳資格管理画面	「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        
        #24	療育手帳資格管理画面	表示
        self.screen_shot( "raa010301-05-24")