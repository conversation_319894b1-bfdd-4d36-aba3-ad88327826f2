import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TEST00480RAA01030408(FukushiSiteTestCaseBase):
    """TEST00480RAA01030408"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        super().setUp()
        
    def test_00480_raa010304_08(self):
        """test_00480_raa010304_08"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        # 帳票名
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        # 発行年月日
        hakkou_ymd = case_data.get("hakkou_ymd", "")

        #ログイン
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA020")
        # 印刷
        self.click_button_by_label("印刷")        
        self.screen_shot("raa010304_08-02")

        if not self.check_online_report_exist(insatsu_tyouhyou_name):
            return

        # 「療育手帳」行の印刷チェックボックス選択
        report_param_list = [
            {
                "report_name": insatsu_tyouhyou_name
            }
        ]
        self.print_online_reports(case_name="ケース名", report_param_list=report_param_list)
        self.screen_shot("raa010304_08-06")
        
        # 「ファイルを開く(O)」ボタンを押下 メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 帳票印刷画面: 「戻る」ボタン押下

        self.return_click()
        self.screen_shot("raa010304_08-09")
