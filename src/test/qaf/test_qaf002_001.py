import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAF002001(FukushiSiteTestCaseBase):
    """TESTQAF002001"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        
        super().setUp()
    
    def test_case_qaf002_001(self):
        """test_case_qaf002_001"""
        driver = None
        test_data = self.common_test_data

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=test_data.get("qaf002_atena_code"), gyoumu_code="QAF010")
        self.save_screenshot_migrate(driver, "QAF001-002-2-1", True)
        #申請内容入力
        self.find_element(By.ID,"span_CmdShinsei").click()
        self.find_element(By.ID,"ShinseiShubetsuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"ShinseiShubetsuCmb"),"加入")

        # time.sleep(2)
        
        self.find_element(By.ID,"span_CmdKakutei").click()
        self.find_element(By.ID,"span_CmdKojinKensaku1").click()
        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qaf002_atena_code2"))
        self.find_element(By.ID,"span_Kensaku").click()
        self.save_screenshot_migrate(driver, "QAF001-002-2-9", True)
        self.find_element(By.ID,"KanyushaZokugaraCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"KanyushaZokugaraCmb"),"子")
        
        self.find_element(By.ID,"TxtShinseiYMD").click()
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("")
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("20210701")
        self.find_element(By.ID,"TxtKenKanyuBango").click()
        self.find_element(By.ID,"TxtKenKanyuBango").send_keys("")
        self.find_element(By.ID,"TxtKenKanyuBango").send_keys("30212")
        self.find_element(By.ID,"TxtKanyuYMD1").click()
        self.find_element(By.ID,"TxtKanyuYMD1").send_keys("")
        self.find_element(By.ID,"TxtKanyuYMD1").send_keys("20210701")
        self.find_element(By.ID,"TxtKakekinKijunbi1").click()
        self.find_element(By.ID,"TxtKakekinKijunbi1").send_keys("")
        self.find_element(By.ID,"TxtKakekinKijunbi1").send_keys("20210701")
        self.find_element(By.ID,"TantoShokatsukuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"TantoShokatsukuCmb"),"第一区")
        
        self.find_element(By.ID,"span_CmdKakekinKeisan").click()
        #登録
        self.find_element(By.ID,"span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAF001-002-2-14", True)
        #進達内容入力
        self.find_element(By.ID,"span_CmdShintatsu1").click()
        self.find_element(By.ID,"TxtShintatsu1YMD").send_keys("")
        self.find_element(By.ID,"TxtShintatsu1YMD").send_keys("20210701")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAF001-002-2-17", True)
        #決定結果入力
        # time.sleep(2)
        self.find_element(By.ID,"span_CmdKettei").click()
        self.find_element(By.ID,"TxtKetteiYMD").send_keys("")
        self.find_element(By.ID,"TxtKetteiYMD").send_keys("20210701")
        self.find_element(By.ID,"KetteiKekkaCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"KetteiKekkaCmb"),"決定")
        # time.sleep(2)

        self.find_element(By.ID,"span_CmdKakekinKeisan").click()
        self.find_element(By.ID,"span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAF001-002-2-20", True)
        #削除
        self.find_element(By.ID,"span_CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAF001-002-2-21", True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAF001-002-2-22", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAF001-002-2-23", True)
        	
        
        

        
    
        