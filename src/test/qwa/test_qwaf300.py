
from base.fukushi_case import FukushiSiteTestCaseBase
from selenium.webdriver.common.by import By

class Test_QWAF300(FukushiSiteTestCaseBase):
   """Test_QWAF300"""
   def test_case_001(self):
        """test_case_001""" 
        test_data = self.common_test_data
        driver = None
                
        self.do_login()
        self.click_button_by_label("申請書出力")
        self.find_element(By.ID, "AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qwaf300_atena_code"))
        self.find_element(By.ID, "Kensaku").click()
        self.screen_shot("QWAF300_1",caption="QWAF300_画面初期化")            
        self.find_element(By.ID, "CmbJigyoID").click()
        dropdown = self.find_element(By.ID, "CmbJigyoID")
        dropdown.find_element(By.XPATH, "//option[. = '児童扶養手当']").click()
        self.find_element(By.ID, "span_CmdKensaku").click()
        self.screen_shot("QWAF300_2",caption="QWAF300_業務検索結果")
        self.find_element(By.ID, "ChkChohyoNo_6").click()
        self.find_element(By.ID, "span_CmdCheck").click()
        self.screen_shot("QWAF300_3",caption="QWAF300_出力対象のみ")
        self.pdf_output_and_download_no_alert(button_id="CmdInsatsu", case_name="QWAF300_印刷ボタン押下")
        self.screen_shot("QWAF300_4",caption="QWAF300_印刷ボタン押下")            
  
