
from base.fukushi_case import FukushiSiteTestCaseBase
from selenium.webdriver.common.by import By

class Test_QWAF110(FukushiSiteTestCaseBase):
   """Test_QWAF110"""
   def test_case_001(self):
        """test_case_001"""
        test_data = self.common_test_data               
        self.do_login()   
        self.click_button_by_label("子育てシミュ")
        self.driver.find_element(By.ID, "span_AtenaKensaku_BTN").click()
        self.driver.find_element(By.ID, "AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qwaf110_atena_code1"))   
        self.driver.find_element(By.ID, "span_Kensaku").click()
        self.driver.find_element(By.ID, "span_TsuikaBTN").click()
        assert self.driver.switch_to.alert.text == "世帯データを追加しますか？　　"
        self.driver.switch_to.alert.accept()
  
        self.driver.find_element(By.ID, "CmbShotoku_1").click()
        dropdown = self.driver.find_element(By.ID, "CmbShotoku_1")
        dropdown.find_element(By.XPATH, "//option[. = '確定']").click()
        self.driver.find_element(By.ID, "TxtSetai_Shotoku1").click()
        self.driver.find_element(By.ID, "TxtSetai_Shotoku1").send_keys("100")
        self.driver.find_element(By.ID, "TxtSetai_Kojo1").click()
        self.driver.find_element(By.ID, "TxtSetai_Kojo1").send_keys("100")
        self.driver.find_element(By.ID, "TxtSetai_JuminZei1").click()
        self.driver.find_element(By.ID, "TxtSetai_JuminZei1").send_keys("100")
        self.driver.find_element(By.ID, "TxtSetai_ShotokuZei1").click()
        self.driver.find_element(By.ID, "TxtSetai_ShotokuZei1").send_keys("100")
        self.driver.find_element(By.ID, "TxtSetai_Fuyo1").click()
        self.driver.find_element(By.ID, "TxtSetai_Fuyo1").send_keys("1")     
        self.driver.find_element(By.ID, "span_AtenaKensaku_BTN").click()
        self.driver.find_element(By.ID, "AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qwaf110_atena_code2"))   
        self.driver.find_element(By.ID, "span_Kensaku").click()
        self.driver.find_element(By.ID, "span_Jikko_BTN").click()
        self.screen_shot("QWAF110_1",caption="QWAF110_画面初期化")               
        self.driver.find_element(By.ID, "ChkShinseishoShuturyoku1").click()
        self.driver.find_element(By.ID, "span_KekkaKoshin_BTN").click()     
        self.screen_shot("QWAF110_2",caption="QWAF110_結果表示更新ボタン押下_I01") 
        self.driver.find_element(By.ID, "span_HyojiKirikae_BTN").click()
        self.screen_shot("QWAF110_3",caption="QWAF110_結果詳細表示")                 
        self.driver.find_element(By.ID, "HyojiKirikae_BTN").click()
        self.screen_shot("QWAF110_4",caption="QWAF110_備考表示")          
        self.driver.find_element(By.ID, "span_ShinseiSho_BTN").click()
        self.screen_shot("QWAF110_5",caption="QWAF110_申請書出力")            
        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "span_Insatsu_BTN1").click()                         
        self.screen_shot("QWAF110_6",caption="QWAF110_印刷")      
