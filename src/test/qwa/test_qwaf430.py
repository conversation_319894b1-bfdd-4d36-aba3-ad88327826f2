import time
import unittest
import os
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class Test_QWAF430(FukushiSiteTestCaseBase):
    """Test_QWAF430"""
    def setUp(self):
        self.exec_sqlfile("QWAF430_実行前スクリプト.sql")
        super().setUp()   

    def test_case_001(self):
        """test_case_001""" 
        test_file_path = os.path.join(self.test_data_root_path, "QAAB0001.crf")

        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.click_button_by_label("申請書マスタメンテナンス") 
        self.screen_shot("QWAF430_1",caption="QWAF430_画面初期化")

        # self.find_element(By.ID, "CmbGyomu").click()
        # dropdown = self.find_element(By.ID, "CmbGyomu")
        # dropdown.find_element(By.XPATH, "//option[. = '障がい']").click()
        self.find_element(By.ID,"CmbGyomu").click()
        self.select_Option(self.driver,self.find_element(By.ID,"CmbGyomu"),"子育て")
      
        # self.find_element(By.ID, "CmbJigyo").click()
        # dropdown = self.find_element(By.ID, "CmbJigyo")
        # dropdown.find_element(By.XPATH, "//option[. = '日常生活用具・住宅設備']").click()

        self.find_element(By.ID,"CmbJigyo").click()
        self.select_Option(self.driver,self.find_element(By.ID,"CmbJigyo"),"児童扶養手当")

        self.find_element(By.ID, "span_CmdKakutei").click()
        self.find_element(By.ID, "span_CmdKensaku").click()
        self.screen_shot("QWAF430_2",caption="QWAF430_検索ボタン押下")
        self.find_element(By.ID, "span_CmdTsuika").click()
        
        self.find_element_by_id("SanshoFile").send_keys(test_file_path)
        self.driver.execute_script("return document.getElementById('SanshoFile').dispatchEvent(new Event('change', { 'bubbles': true }))")

        self.find_element(By.ID, "TxtChohyoID1a").click()
        self.find_element(By.ID, "TxtChohyoID1a").send_keys("000001")
        self.find_element(By.ID, "TxtChohyoID1b").send_keys("0001")
        self.find_element(By.ID, "TxtChohyoID2").send_keys("0")
        self.find_element(By.ID, "TxtChohyoName").send_keys("テスト帳票名称")
        self.find_element(By.ID, "span_CmdKoshin").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QWAF430_3",caption="QWAF430_登録ボタン押下_I01")

        self.find_element(By.ID, "CmdNo1").click()
        self.find_element(By.ID, "TxtChohyoName").click()
        self.find_element(By.ID, "TxtChohyoName").send_keys("テスト帳票名称１２３")
        self.find_element(By.ID, "span_CmdKoshin").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QWAF430_4",caption="QWAF430_更新ボタン押下_U02")

        self.find_element(By.ID, "CmdNo1").click()
        self.find_element(By.CSS_SELECTOR, ".wr_table:nth-child(4) .wr_table > td:nth-child(2)").click()
        self.find_element(By.ID, "span_CmdShokiHyoji").click()
        self.screen_shot("QWAF430_5",caption="QWAF430_「初期表示」ボタン押下")
        self.find_element(By.ID, "CmdSakujo").click()
        assert self.driver.switch_to.alert.text == "削除します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QWAF430_6",caption="QWAF430_削除ボタン押下_D01")

        self.find_element(By.ID, "span_CmdTsuika").click()
        self.find_element_by_id("SanshoFile").send_keys(test_file_path)
        self.driver.execute_script("return document.getElementById('SanshoFile').dispatchEvent(new Event('change', { 'bubbles': true }))")

        self.find_element(By.ID, "TxtChohyoID1a").click()
        self.find_element(By.ID, "TxtChohyoID1a").send_keys("000001")
        self.find_element(By.ID, "TxtChohyoID1b").send_keys("0001")
        self.find_element(By.ID, "TxtChohyoID2").send_keys("0")
        self.find_element(By.ID, "TxtChohyoName").send_keys("テスト帳票名称")
        self.find_element(By.ID, "span_CmdKoshin").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QWAF430_7",caption="QWAF430_登録ボタン押下_U01")


        self.find_element(By.ID, "Tsugi").click()
        self.screen_shot("QWAF430_8",caption="QWAF430_「次頁」ボタン押下")
        self.find_element(By.ID, "Mae").click()
        self.screen_shot("QWAF430_9",caption="QWAF430_「前頁」ボタン押下")
        self.find_element(By.ID, "PageCmb").click()
        dropdown = self.find_element(By.ID, "PageCmb")
        dropdown.find_element(By.XPATH, "//option[. = '02']").click()
        self.find_element(By.ID, "Idobtn").click()
        self.screen_shot("QWAF430_10",caption="QWAF430_「「へ移動」ボタン押下")
        self.find_element(By.ID, "span_CmdShokiHyoji").click()
        self.screen_shot("QWAF430_11",caption="QWAF430_「初期表示」ボタン押下")
  