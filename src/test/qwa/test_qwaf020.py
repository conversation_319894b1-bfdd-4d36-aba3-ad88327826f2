
from base.fukushi_case import FukushiSiteTestCaseBase
from selenium.webdriver.common.by import By

class Test_QWAF020(FukushiSiteTestCaseBase):
   """Test_QWAF020"""
   def test_case_001(self):
        """test_case_001"""  
        driver = None
        test_data = self.common_test_data                       
        self.do_login()   
        self.click_button_by_label("高齢・障害シミュ")
        self.screen_shot("QWAF020_1",caption="QWAF020_画面初期化")               
        self.driver.find_element(By.ID, "span_AtenaKensaku_BTN").click()
        self.screen_shot("QWAF020_2",caption="QWAF020_住基検索")             
        self.driver.find_element(By.ID, "AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qwaf020_atena_code1"))   
        self.driver.find_element(By.ID, "span_Kensaku").click()
        self.driver.find_element(By.ID, "TsuikaBTN").click()        
        assert self.driver.switch_to.alert.text == "世帯データを追加しますか？　　"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QWAF020_3",caption="QWAF020_追加")            
        self.driver.find_element(By.ID, "TxtHomnim_Shotoku").click()
        self.driver.find_element(By.ID, "TxtHomnim_Shotoku").send_keys("100")
        self.driver.find_element(By.ID, "TxtHomnim_Kojo").click()
        self.driver.find_element(By.ID, "TxtHomnim_Kojo").send_keys("100")
        self.driver.find_element(By.ID, "TxtHomnim_Juminzei").click()
        self.driver.find_element(By.ID, "TxtHomnim_Juminzei").send_keys("100")
        self.driver.find_element(By.ID, "TxtHomnim_Shotokuzei").click()
        self.driver.find_element(By.ID, "TxtHomnim_Shotokuzei").send_keys("100")
        self.driver.find_element(By.ID, "TxtHomnim_Fuyo").click()
        self.driver.find_element(By.ID, "TxtHomnim_Fuyo").send_keys("1")
        self.driver.find_element(By.ID, "TxtHomnim_Fuyo").send_keys("1")
        self.driver.find_element(By.ID, "TxtHomnim_Nenkin").click()
        self.driver.find_element(By.ID, "TxtHomnim_Nenkin").send_keys("100")
        self.driver.find_element(By.ID, "span_AtenaKensaku_BTN").click()
        self.driver.find_element(By.ID, "AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qwaf020_atena_code2"))   
        self.driver.find_element(By.ID, "span_Kensaku").click()
        self.driver.find_element(By.ID, "span_Jikko_BTN").click()
        self.screen_shot("QWAF020_4",caption="QWAF020_シミュレーション実行ボタン押下_I01")
        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "span_Rireki_BTN").click()
        self.screen_shot("QWAF020_5",caption="QWAF020_シミュレーション履歴")            
        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "span_RirekiKekka_BTN").click()
        self.screen_shot("QWAF020_6",caption="QWAF020_履歴の結果参照")            
        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "span_ByomeiKensaku_BTN").click()
        self.screen_shot("QWAF020_7",caption="QWAF020_病名検索")                    
        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "span_ShokiHyoji_BTN").click()
        self.screen_shot("QWAF020_8",caption="QWAF020_初期表示")             
                       
  
