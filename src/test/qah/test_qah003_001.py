import time
import datetime
from datetime import timedelta
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAH003001(FukushiSiteTestCaseBase):
    """TESTQAH003001"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAH003.sql", params=atena_list)
        super().setUp()
    
    def test_case_qah003_001(self):
        """test_case_qah003_001"""
        #システム日付を変数に設定
        date = datetime.date.today()
        today = format(date, '%Y%m%d')
        Day = 'CmdDay{0}'.format(today)

        driver = None
        test_data = self.test_data
        self.do_login()

        #メインメニュー・健康管理押下
        self.save_screenshot_migrate(driver, "QAH001-003-1-1" , True)
        self.find_element(By.ID,"CmdProcess33_1").send_keys(Keys.ENTER)

        self.find_element(By.ID,"SetaiCD").send_keys("")
        self.find_element(By.ID,"SetaiCD").send_keys(test_data.get("atena_code"))
        self.find_element(By.ID,"Kensaku").click()
        self.find_element(By.ID,"NoBtn1").click()

        self.save_screenshot_migrate(driver, "QAH003-003-2-7" , True)
        self.find_element(By.ID,"CmdSeijin4").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-9" , True)
        
        self.find_element(By.ID,"CmdModori").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-10" , True)

        self.find_element(By.ID,"CmdSusumi").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-11" , True)

        self.find_element(By.ID,"Btn_QAH_Calendar0").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-12" , True)

        self.driver.switch_to.frame(1)
        self.find_element(By.CSS_SELECTOR, "tr:nth-child(2) > .TD2").click()
        self.driver.switch_to.default_content()

        self.save_screenshot_migrate(driver, "QAH003-003-2-13" , True)

        self.find_element(By.ID,"CmdTouNendo").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-14" , True)

        self.find_element(By.ID,"CmdNendoKirikae").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-15" , True)

        self.find_element(By.ID,"CmdHyoujiKirikae").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-16" , True)

        self.find_element(By.ID,"CmdHyoujiKirikae").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-17" , True)

        self.find_element(By.ID,"CmdShogamen").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-18" , True)

        self.find_element(By.ID,"CmdTransit").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-19" , True)

        #操作対象をサブ画面に切り替える
        self.driver.switch_to.frame(0)
        self.find_element(By.ID,"CmdCommon2").click()
        #操作対象をメイン画面に切り替える
        self.driver.switch_to.default_content()
        
        self.save_screenshot_migrate(driver, "QAH003-003-2-21" , True)

        self.find_element(By.ID,"SetaiCD").send_keys("")
        self.find_element(By.ID,"SetaiCD").send_keys(test_data.get("atena_code"))
        self.find_element(By.ID,"Kensaku").click()
        self.find_element(By.ID,"NoBtn1").click()

        self.save_screenshot_migrate(driver, "QAH003-003-2-26" , True)
        self.find_element(By.ID,"CmdKenshin3").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-28" , True)

        self.find_element(By.ID,"CmdTsuika").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-29" , True)

        self.find_element(By.ID,"CmbFukushi").send_keys(u"第一区")
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20240401")
        self.find_element(By.ID,"TxtJushinBasho").click()
        self.find_element(By.ID,"TxtJushinBasho").send_keys("")
        self.find_element(By.ID,"TxtJushinBasho").send_keys("0000000001")
        self.find_element(By.ID,"01002001").send_keys(u"青国")
        self.find_element(By.ID,"01002002").send_keys(u"本人")
        self.find_element(By.ID,"01002003").send_keys(u"老人")        
        self.find_element(By.ID,"01002004Code").click()
        self.find_element(By.ID,"01002004Code").send_keys("")
        self.find_element(By.ID,"01002004Code").send_keys("1")
        self.find_element(By.ID,"01002005").click()
        self.find_element(By.ID,"01002005").send_keys("")
        self.find_element(By.ID,"01002005").send_keys("202404")
        self.find_element(By.ID,"01003001").send_keys("なし") 
        self.find_element(By.ID,"01003002").send_keys("なし") 
        self.find_element(By.ID,"01003003").send_keys("なし") 
        self.find_element(By.ID,"01003004").send_keys("なし") 
        self.find_element(By.ID,"01003005").send_keys("なし") 
        self.find_element(By.ID,"01003006").click()
        self.find_element(By.ID,"01003006").send_keys("")
        self.find_element(By.ID,"01003006").send_keys("1")
        self.find_element(By.ID,"01003007").send_keys(u"異常なし") 
        self.find_element(By.ID,"01003008").send_keys(u"放置") 
        self.find_element(By.ID,"01004001").click()
        self.find_element(By.ID,"01004001").send_keys("")
        self.find_element(By.ID,"01004001").send_keys("1")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH003-003-2-32" , True)

        self.find_element(By.ID,"CmdShuusei").click()
        self.find_element(By.ID,"01002001").send_keys(u"後期高齢") 
        self.find_element(By.ID,"01002002").send_keys(u"家族") 
        self.find_element(By.ID,"01002003").send_keys(u"生保") 
        self.find_element(By.ID,"01002004Code").click()
        self.find_element(By.ID,"01002004Code").send_keys("")
        self.find_element(By.ID,"01002004Code").send_keys("2")
        self.find_element(By.ID,"01002005").click()
        self.find_element(By.ID,"01002005").send_keys("")
        self.find_element(By.ID,"01002005").send_keys("202406")
        self.find_element(By.ID,"CmdShoki").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-35" , True)

        self.find_element(By.ID,"CmdShuusei").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-36" , True)

        self.find_element(By.ID,"01002001").send_keys(u"後期高齢") 
        self.find_element(By.ID,"01002002").send_keys(u"家族") 
        self.find_element(By.ID,"01002003").send_keys(u"生保") 
        self.find_element(By.ID,"01002004Code").click()
        self.find_element(By.ID,"01002004Code").send_keys("")
        self.find_element(By.ID,"01002004Code").send_keys("2")
        self.find_element(By.ID,"01002005").click()
        self.find_element(By.ID,"01002005").send_keys("")
        self.find_element(By.ID,"01002005").send_keys("202406")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH003-003-2-38" , True) 
  
        self.find_element(By.ID,"CmdZenNendo").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-39" , True)

        self.find_element(By.ID,"CmdJiNendo").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-40" , True)

        self.find_element(By.ID,"Btn_QAH_Calendar0").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-41" , True)

        self.driver.switch_to.frame(0)
        self.find_element(By.CSS_SELECTOR, "tr:nth-child(4) > .TD2").click()
        self.driver.switch_to.default_content()

        self.save_screenshot_migrate(driver, "QAH003-003-2-42" , True)
        self.find_element(By.ID,"CmdJiNendo").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-43" , True)
        
        self.find_element(By.ID,"CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH003-003-2-44" , True)

        self.find_element(By.ID,"btnCommon0").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-46" , True)

        self.find_element(By.ID,"CmdHyojiKirikae").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-47" , True)

        self.find_element(By.ID,"CmdHyojiKirikae").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-48" , True)

        self.find_element(By.ID,"CmdCol2024").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-49" , True)

        self.find_element(By.ID,"CmdShoki").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-50" , True)

        self.find_element(By.ID,"CmdRow_M2").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-51" , True)

        self.find_element(By.ID,"CmdModosu").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-52" , True)

        self.find_element(By.ID,"CmdJiNendo").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-53" , True)

        self.find_element(By.ID,"CmdZenNendo").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-54" , True)

        self.find_element(By.ID,"Btn_QAH_Calendar0").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-55" , True)

        self.driver.switch_to.frame(0)
        self.find_element(By.CSS_SELECTOR, "tr:nth-child(4) > .TD2").click()
        self.driver.switch_to.default_content()

        self.save_screenshot_migrate(driver, "QAH003-003-2-56" , True)
        self.find_element(By.ID,"CmdGyomuKirikae2").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-57" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-59" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-61" , True)

        self.find_element(By.ID,"CmdSeiken3").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-63" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-65" , True)

        self.find_element(By.ID,"CmdMoushikomi5").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-67" , True)

        self.find_element(By.ID,Day).click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-68" , True)

        self.find_element(By.ID,u"集団処理対象_1").click()
        self.find_element(By.ID,u"集団個人検索ボタン").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-70" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-73" , True)

        self.find_element(By.ID,"CmdPrintOut").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-75" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-77" , True)

        self.find_element(By.NAME,"img").click()
        self.find_element(By.ID,"btnCommon13").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-79" , True)

        self.find_element(By.ID,"CmdShiborikomi").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-80" , True)

        self.find_element(By.NAME,"ChkDelete_1").click()
        
        self.find_element(By.ID,"CmdDelete").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH003-003-2-82" , True)

        self.find_element(By.ID,"CmdShoki").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-83" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-85" , True)

        self.find_element(By.NAME,"img").click()
        self.find_element(By.ID,"btnCommon15").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-87" , True)

        self.find_element(By.ID,"CmdHitsuuchi").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-88" , True)

        self.find_element(By.ID,"CmdShoki").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH003-003-2-89" , True)

        self.find_element(By.ID,"CmdHitsuuchi").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-90" , True)
        
        self.find_element(By.ID,"CmdUpdate").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH003-003-2-91" , True)

        self.find_element(By.ID,"CmdKaijo").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-92" , True)

        self.find_element(By.ID,"CmdUpdate").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH003-003-2-93" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-95" , True)

        self.find_element(By.ID,"CmdShogamen").click()
 
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-97" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-98", True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH003-003-2-99", True)	
