import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAH016001(FukushiSiteTestCaseBase):
    """TESTQAH016001"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAH016.sql", params=atena_list)
        super().setUp()
    
    def test_case_qah016_001(self):
        """test_case_qah016_001"""
        driver = None
        test_data = self.test_data
        self.do_login()

        #メインメニュー・健康管理押下
        self.find_element(By.ID,"CmdProcess33_1").send_keys(Keys.ENTER)

        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("atena_code"))
        self.find_element(By.ID,"Kensaku").click()
        self.save_screenshot_migrate(driver, "QAH016-016-1-5" , True)

        self.find_element(By.ID,"CmdNinpu").click()
        self.save_screenshot_migrate(driver, "QAH016-016-1-7" , True)

        self.find_element(By.ID,"CmdSinki").click()
        self.save_screenshot_migrate(driver, "QAH016-016-1-9" , True)
        
        #操作対象をサブ画面に切り替える
        self.driver.switch_to.frame(0)
        self.find_element(By.ID,"CmdKettei").click()
        #操作対象をメイン画面に切り替える
        self.driver.switch_to.default_content()

        self.save_screenshot_migrate(driver, "QAH016-016-1-10" , True)

        self.find_element(By.ID,"CmdTsuika").click()

        self.find_element(By.ID,"CmbFukushi").send_keys(u"第一区")
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20240401")
        
        self.find_element(By.ID,"TxtJushinBasho").click()
        self.find_element(By.ID,"TxtJushinBasho").send_keys("")
        self.find_element(By.ID,"TxtJushinBasho").send_keys("0000000001")

        self.find_element(By.ID,"01001004").click()
        self.find_element(By.ID,"01001004").send_keys("")
        self.find_element(By.ID,"01001004").send_keys("1")
     
        self.find_element(By.ID,"01001005").click()
        self.find_element(By.ID,"01001005").send_keys("")
        self.find_element(By.ID,"01001005").send_keys("20240401")
     
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH016-016-1-15" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH016-016-1-17" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH016-016-1-19" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH016-016-1-21" , True)

        self.find_element(By.ID,"GOBACK").click()

        self.find_element(By.ID,"CmdProcess33_4").click()
        self.find_element(By.ID,"BtnSearch").click()
        
        self.find_element(By.ID,"BtnShokiHyoji").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.find_element(By.ID,u"生年月日開始").click()
        self.find_element(By.ID,u"生年月日開始").send_keys("")
        self.find_element(By.ID,u"生年月日開始").send_keys("20150505")
        self.find_element(By.ID,u"生年月日終了").click()
        self.find_element(By.ID,u"生年月日終了").send_keys("")
        self.find_element(By.ID,u"生年月日終了").send_keys("20150505")
        self.find_element(By.ID,"BtnSearch").click()
        self.find_element(By.ID,"No_1").click()
        self.save_screenshot_migrate(driver, "QAH016-016-1-29" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH016-016-1-31" , True)

        self.find_element(By.ID,u"母親検索_1").click()
        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("atena_code"))
        self.find_element(By.ID,"Kensaku").click()
        self.save_screenshot_migrate(driver, "QAH016-016-1-34" , True)

        self.find_element(By.ID,"No_1").click()
        self.save_screenshot_migrate(driver, "QAH016-016-1-36" , True)

        
        self.find_element(By.ID,"BtnTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.find_element(By.ID,"GOBACK").click()

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH016-016-1-37", True)
        
    # テスト後に実行したい場合はこちら
    def tearDown(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAH016.sql", params=atena_list)
        super().tearDown()