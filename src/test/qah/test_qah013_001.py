import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAH013001(FukushiSiteTestCaseBase):
    """TESTQAH013001"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAH013_1.sql", params=atena_list)
        super().setUp()
    
    def test_case_qah013_001(self):
        """test_case_qah013_001"""
        driver = None
        test_data = self.test_data
        self.do_login()
        
        #メインメニュー・バッチ起動
        self.find_element(By.ID,"CmdProcess4_1").click()
        self.save_screenshot_migrate(driver, "QAH013-013-001-2" , True)

        self.find_element(By.ID,"GyomuSelect").send_keys(u"健康管理")

        self.find_element(By.ID,"JigyoSelect").send_keys(u"予防接種")

        self.find_element(By.ID,"ShoriKubunSelect").send_keys(u"一括取込処理")

        self.find_element(By.ID,"ShoriBunruiSelect").send_keys(u"一括取込処理")

        self.find_element(By.ID,"Sel1").click()
        self.save_screenshot_migrate(driver, "QAH013-013-1-5" , True)

        self.find_element(By.ID,"item5").send_keys("1")
        self.find_element(By.ID,"item9").send_keys(u"住民コード")

        #★TODO：ジョブが正常に実行できる状態になったら実施する。
#        self.find_element(By.ID,"ExecuteButton").click()
#        self.assertEqual(u"処理を開始します。よろしいですか？", self.alert_ok())
#        self.save_screenshot_migrate(driver, "QAH013-013-1-6" , True)

        self.find_element(By.ID,"JobListButton").click()
        self.save_screenshot_migrate(driver, "QAH013-013-1-7" , True)

        self.find_element(By.ID,"SearchButton").click()
        self.save_screenshot_migrate(driver, "QAH013-013-1-8" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH013-013-1-10" , True)

        #取込エラー修正ボタン
        self.find_element(By.ID,"CmdProcess33_3").click()
        self.save_screenshot_migrate(driver, "QAH013-013-1-12" , True)

        self.find_element(By.ID,"RdoYobo").click()
        self.find_element(By.ID,"CmdClear").click()
        self.save_screenshot_migrate(driver, "QAH013-013-1-14" , True)

        self.find_element(By.ID,"RdoYobo").click()
        self.find_element(By.ID,"CmbJigyo").send_keys(u"新型コロナ")
        self.find_element(By.ID,"CmbKenshinKubun").send_keys(u"１回目")
        self.find_element(By.ID,"CmdKakutei").click()
        self.save_screenshot_migrate(driver, "QAH013-013-1-18" , True)

        self.find_element(By.ID,"CmdSearch").click()
        self.save_screenshot_migrate(driver, "QAH013-013-1-19" , True)

        #★TODO：ジョブが正常に実行できる状態になったら実施する。
#        self.find_element(By.ID,"Sel1").click()
#        self.save_screenshot_migrate(driver, "QAH013-013-1-21" , True)
#
#        self.find_element(By.ID,'TxtAtenaCode').send_keys(Keys.CONTROL + "a")
#        self.find_element(By.ID,'TxtAtenaCode').send_keys(Keys.DELETE)
#        self.find_element(By.ID,"TxtAtenaCode").send_keys("111")
#        self.find_element(By.ID,"CmdShoki").click()
#
#        self.find_element(By.ID,'TxtAtenaCode').send_keys(Keys.CONTROL + "a")
#        self.find_element(By.ID,'TxtAtenaCode').send_keys(Keys.DELETE)
#        self.find_element(By.ID,"CmdKojinKensaku").click()
#        self.save_screenshot_migrate(driver, "QAH013-013-1-27" , True)
#
#        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("atena_code"))
#        self.find_element(By.ID,"Kensaku").click()
#        self.save_screenshot_migrate(driver, "QAH013-013-1-31" , True)
#
#        self.find_element(By.ID,"CmdKakutei").click()
#        self.save_screenshot_migrate(driver, "QAH013-013-1-32" , True)
#
#        self.find_element(By.ID,'CMCOM001').send_keys(Keys.CONTROL + "a")
#        self.find_element(By.ID,'CMCOM001').send_keys(Keys.DELETE)
#        self.find_element(By.ID,"CMCOM001").send_keys("99301")
#        self.find_element(By.ID,'01001001').send_keys(Keys.CONTROL + "a")
#        self.find_element(By.ID,'01001001').send_keys(Keys.DELETE)
#        self.find_element(By.ID,"01001001").send_keys("1")
#        self.find_element(By.ID,"01001002").click()
#        self.find_element(By.ID,'01001005').send_keys(Keys.CONTROL + "a")
#        self.find_element(By.ID,'01001005').send_keys(Keys.DELETE)
#        self.find_element(By.ID,"01001005").send_keys("0000000001")
#        self.find_element(By.ID,'01001004').send_keys(Keys.CONTROL + "a")
#        self.find_element(By.ID,'01001004').send_keys(Keys.DELETE)
#        self.find_element(By.ID,"01001004").send_keys("099101")        
#        
#        self.find_element(By.XPATH,"//span[@id='CmdTouroku']/u").click()
#        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
#        self.save_screenshot_migrate(driver, "QAH013-013-1-35" , True)
#
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH013-013-1-37" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH013-013-1-38", True)
