# テストの名称と結果ファイルの関連

テスト結果(HTML/XLSX)に表示される名称と、テストコード内の名称は以下の関係になります。

![img301](./img/image301.png)

- ケースID: `[test配下のディレクトリパス].[Class名].[メソッド名]`
- 内容: `[classのdoc-string]` - `[methodのdoc-string]`

```
class [Class名](FukushiSiteTestCaseBase):
    """[classのdoc-string]"""
    
    @unittest.skip('skipped')
    def [メソッド名](self):
        """methodのdoc-string"""
```

エビデンスのスクリーンショットのタイトルは下記部分が該当します。

![img302](./img/image302.png)

```
self.screen_shot("メインメニュー", caption="[エビデンスのタイトル]")
```